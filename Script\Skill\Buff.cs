using Excel.skillDisplay;
using System;
using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

public class PartDamage
{
    public float splitDamage;
    public float damage;
    public float interval;
    public float lastTime;
}
[Serializable]
public class Buff
{
    private GameEntity m_attacher;
    public GameEntity Attacher { get { return m_attacher; } set { m_attacher = value; } }

    private Actor m_releaser;
    public Actor Releaser { get => m_releaser; set => m_releaser = value; }

    private Skill m_bindSkill;
    public Skill bindSkill { get => m_bindSkill; }

    private BuffData m_buffData;
    public BuffData BuffData { get => m_buffData; set => m_buffData = value; }

    [SerializeField]
    public BuffData.BuffEnum m_buffType;
    public BuffData.BuffEnum buffType { get => m_buffType; set => m_buffType = value; }

    private float m_startTime;
    public float StartTime { get => m_startTime; set => m_startTime = value; }

    private int m_playHaloCount;
    private float m_intervalTime;
    private float m_lastTime;

    private Dictionary<AttributeEnum, float> attributeCache = new Dictionary<AttributeEnum, float>();
    [SerializeField]
    private AttributeEnum m_attributeEnum;

    private bool m_enabled;
    public bool Enable { get => m_enabled; set => m_enabled = value; }

    public float m_shieldHealth;
    public float ShieldHealth { get => m_shieldHealth; set => m_shieldHealth = value; }

    private bool m_isDelay = false;
    public bool isDelay { get => m_isDelay; set => m_isDelay = value; }

    private float m_triggerTime;
    public float triggerTime { get => m_triggerTime; set => m_triggerTime = value; }

    private float m_attributeChange = 0f;

    private SkillBuffShow m_skillBuffShow;
    public SkillBuffShow skillBuffShow { get => m_skillBuffShow; set => m_skillBuffShow = value; }

    private GameObject m_goSkillBuffShow;

    /// <summary>
    /// 受本Buff影响的其他GameEntity并加到其他GameEntity的Buff
    /// </summary>
    private Dictionary<GameEntity,Buff> m_affectedEntitiesBuffs;
    /// <summary>
    /// 受本Buff影响的其他GameEntity并加到其他GameEntity的Buff
    /// </summary>
    public Dictionary<GameEntity, Buff> AffectedEntitiesBuffs { get => m_affectedEntitiesBuffs; set => m_affectedEntitiesBuffs = value; }

    //private int m_currentStacks = 1;
    //public int currentStacks { get => m_currentStacks; set => m_currentStacks = value; }
    public List<float> m_stackTimeList = new List<float>();
    public GameEntity lastTarget = null;

    private List<PartDamage> m_partDamageList = new List<PartDamage>();
    public List<PartDamage> partDamageList { get => m_partDamageList; }

    private int m_oldSkillID;
    private int m_newSkillID;
    public Buff(int id, Skill skill=null)
    {
        m_bindSkill = skill;
        m_buffData = new BuffData(id);
        m_buffType = m_buffData.BuffType;
        m_isDelay = false;
        m_triggerTime = Time.time;

        if (m_buffData.ShowID > 0)
        {
            buffshowEntity entity = ExcelData.Instance.GetBuffShow(m_buffData.ShowID);
            if (entity == null) return;
            m_skillBuffShow = new SkillBuffShow(m_buffData.ShowID);
        }
    }

    public Buff()
    {
        m_isDelay = false;
        m_triggerTime = Time.time;
    }

    public void Start()
    {
        m_startTime = Time.time;

        switch (m_buffType)
        {
            case BuffData.BuffEnum.ChangeAttribute:
                ChangeAttribute();
                break;
            case BuffData.BuffEnum.DamageOverTime:
                bool rightAwayDamage = m_buffData.buffParams4 > 0;
                m_intervalTime = m_buffData.buffParams3;
                m_lastTime = Time.time;
                if (rightAwayDamage)
                    DamageOverTime();
                break;
            case BuffData.BuffEnum.RestrictSkill:
                for (int i = 0; i < m_attacher.skillList.Count; i++)
                {
                    RestrictSkill(m_attacher.skillList[i]);
                }
                break;
            case BuffData.BuffEnum.StopAct:
                m_attacher.disableAction = ((DisableActionEnum)m_buffData.buffParams1);
                break;
            case BuffData.BuffEnum.Taunt:
                //一个目标身上同时存在多个不用buff组的嘲讽效果时，保留最先攻击的那个嘲讽目标，当buff消失或嘲讽的目标死亡后，
                //再按照新的嘲讽buff效果重新选择新的攻击目标
                break;
            case BuffData.BuffEnum.HealthOverTime:
                m_lastTime = Time.time;
                m_intervalTime = m_buffData.buffParams3;
                if (m_buffData.buffParams4 > 0)
                    HealthOverTime();
                break;
            case BuffData.BuffEnum.Halo:
                m_playHaloCount = (int)m_buffData.buffParams2;
                m_lastTime = Time.time;
                m_intervalTime = m_buffData.buffParams4 / 1000.0f;
                if (m_buffData.buffParams5 > 0)
                    Halo();
                break;
            case BuffData.BuffEnum.Shield:
                float coefficient = m_buffData.buffParams2;
                switch ((int)m_buffData.buffParams1)
                {
                    case 1:
                        ShieldHealth = m_attacher.AttackDamage * coefficient;
                        break;
                    case 2:
                        ShieldHealth = m_attacher.AttackDamage * coefficient;
                        break;
                }
                break;
            case BuffData.BuffEnum.Rebound:
                break;
            case BuffData.BuffEnum.ReplaceSkill:
                m_oldSkillID = (int)m_buffData.buffParams1;
                m_newSkillID = (int)m_buffData.buffParams2;
                m_attacher.ReplaceSkill(m_oldSkillID, m_newSkillID);
                break;
            case BuffData.BuffEnum.GainPowerBasedAllies:
                //构造新的Buff
                m_attacher.AddAroundBuff(this);
                break;
            case BuffData.BuffEnum.ChangeAttributesInRange:
                m_attacher.AddAroundBuff(this);
                break;
            case BuffData.BuffEnum.AttributeOnFollowerStatus://改变跟随者属性
                {
                    int type = (int)m_buffData.buffParams1;
                    //士兵单位状态
                    //1 = 跟随
                    //2 = 固守
                    //3 = 警戒
                    if (type == 1)
                    {
                        int buffID = (int)(m_buffData.buffParams2);
                        Buff buff = new Buff(buffID);
                        if (m_affectedEntitiesBuffs == null)
                            m_affectedEntitiesBuffs = new Dictionary<GameEntity, Buff>();
                        else
                            m_affectedEntitiesBuffs.Clear();

                        for (int i = 0; i < m_attacher.FollowerList.Count; i++)
                        {
                            if (!m_attacher.FollowerList[i].buffSystem.haveBuff(buffID))
                            {
                                m_affectedEntitiesBuffs.Add(m_attacher.FollowerList[i], buff);
                                m_attacher.FollowerList[i].buffSystem.AddBuff(buff);
                            }
                        }
                    }
                    //m_attacher.AddFollowerAttributeBuff(this);
                }
                break;
        }

        if (m_skillBuffShow != null && !string.IsNullOrEmpty(m_skillBuffShow.buffEffect))
        {
            GameObject prefab = Resources.Load(m_skillBuffShow.buffEffect) as GameObject;
            if (prefab == null) return;
            m_goSkillBuffShow = GameObject.Instantiate(prefab) as GameObject;
            Transform anchor = m_attacher.GetAnchor(m_skillBuffShow.buffEffectAnchor);
            if (anchor != null)
            {
                if (m_skillBuffShow.buffMove)
                {
                    m_goSkillBuffShow.transform.parent = anchor;
                    m_goSkillBuffShow.transform.localPosition = Vector3.zero;
                }
                else
                {
                    m_goSkillBuffShow.transform.position = anchor.position;
                }
            }
        }
    }

    public Buff GetSubBuff()
    {
        Buff buff = new Buff();
        return buff;
    }
    public void ChangeAttribute()
    {
        m_attributeEnum = (AttributeEnum)m_buffData.buffParams1;
        m_attacher.AddAttributeBuff(this);
        m_attacher.AttributeChange(m_attributeEnum);
    }


    public void Reset()
    {
        m_lastTime = Time.time;
        m_startTime = Time.time;
        if (m_buffType == BuffData.BuffEnum.Halo)
            m_playHaloCount = (int)m_buffData.buffParams2;

        //m_isDelay = false;
        //m_triggerTime = Time.time;
        //AttributeRestore();
    }
    public void Update()
    {
        if (!m_isDelay)
        {
            if (m_triggerTime + m_buffData.buffDelay < Time.time)
            {
                Start();
                m_isDelay = true;
            }
            return;
        }

        switch (m_buffType)
        {
            case BuffData.BuffEnum.DamageOverTime:
                if (m_lastTime + m_intervalTime > Time.time)
                {
                    DamageOverTime();
                    m_lastTime = Time.time;
                }
                break;
            case BuffData.BuffEnum.HealthOverTime:
                if (m_lastTime + m_intervalTime > Time.time)
                {
                    HealthOverTime();
                    m_lastTime = Time.time;
                }
                break;
            case BuffData.BuffEnum.Halo:
                if( m_playHaloCount > 0 && Time.time - m_lastTime > m_intervalTime)
                {
                    Halo();
                }
                break;
            //部分伤害缓慢扣除
            case BuffData.BuffEnum.PartDamageOverTime:
                for (int i = 0; i < m_partDamageList.Count; i++)
                {
                    if (m_partDamageList[i].lastTime + m_partDamageList[i].interval < Time.time && m_partDamageList[i].damage > 0)
                    {
                        m_attacher.BuffDamage(this, m_partDamageList[i].splitDamage);
                        m_partDamageList[i].damage -= m_partDamageList[i].splitDamage;
                        m_partDamageList[i].lastTime = Time.time;
                    }
                }

                m_partDamageList.RemoveAll( element => element.damage <= 0 );
                break;
            case BuffData.BuffEnum.ChangeAttributesByTargetCount:
                {
                    if (m_attributeChange > 0) return;

                    List<GameEntity> list = null;
                    int type          = (int)m_buffData.buffParams1;
                    float range       = m_buffData.buffParams2;
                    int count         = (int)m_buffData.buffParams3;
                    int attribute     = (int)m_buffData.buffParams4;
                    float coefficient = m_buffData.buffParams5;
                    int method        = (int)m_buffData.buffParams6;

                    if (type == 1)// 1 = 友方
                        list = GameMain.Instance.GetFriend(m_attacher, range);
                    else
                        list = GameMain.Instance.GetEnemy(m_attacher, range);
                    if (list.Count < count) return;

                    float originalValue = m_attacher.GetAttributeOriginalValue((AttributeEnum)attribute);
                    float value = 0;
                    if( method == 1)//1=基础属性*（1+系数值）
                        value = originalValue * Mathf.Max(0, 1 + coefficient);
                    else if( method == 2)//2=基础属性+系数值
                        value = originalValue + coefficient;

                    m_attributeChange = value - originalValue;
                    m_attacher.SetAttributeExtra((AttributeEnum)attribute, m_attributeChange);
                }
                break;
        }
    }
    public bool isComplete
    {
        get  
        { 
            if(m_stackTimeList.Count > 1)
            {
                for( int i = 0; i < m_stackTimeList.Count; i++ )
                {
                    if (m_stackTimeList[i] + m_buffData.duration > Time.time)
                        return false;
                }
                return true;
            }
            else
                return m_startTime + m_buffData.duration < Time.time;
        }
    }

    public void LeaveActor()
    {
        switch (m_buffType)
        {
            case BuffData.BuffEnum.ChangeAttribute:
                m_attacher.DelAttributeChange(this);
                m_attacher.AttributeChange((AttributeEnum)m_buffData.buffParams1);
                break;
            case BuffData.BuffEnum.RestrictSkill:
                for (int i = 0; i < m_attacher.skillList.Count; i++)
                {
                    m_attacher.skillList[i].Enable = true;
                    m_attacher.skillList[i].FreezeCoolDown = false;
                }
                break;
            case BuffData.BuffEnum.StopAct:
                Actor actor = m_attacher as Actor;
                actor.EnableAction();
                break;
            case BuffData.BuffEnum.ReplaceSkill:
                {
                    m_attacher.ReplaceSkill(m_newSkillID, m_oldSkillID);
                }
                break;
            case BuffData.BuffEnum.GainPowerBasedAllies:
                m_attacher.RemoveAroundBuff(this);
                break;
            case BuffData.BuffEnum.ChangeAttributesInRange:
                m_attacher.RemoveAroundBuff(this);
                break;
            case BuffData.BuffEnum.AttributeOnFollowerStatus:
                {
                    if (m_affectedEntitiesBuffs != null)
                    {
                        foreach (var item in m_affectedEntitiesBuffs)
                        {
                            item.Key.buffSystem.RemoveBuff(item.Value);
                        }
                        m_affectedEntitiesBuffs.Clear();
                    }
                }
                break;
            case BuffData.BuffEnum.AttackEnemiesToPowerUp:
                AttributeEnum attribute = (AttributeEnum)BuffData.buffParams1;
                //还原值
                m_attacher.SetAttributeExtraOffset(attribute, -m_lastPowerUpValue);
                break;
            case BuffData.BuffEnum.ChangeAttributesByTargetCount:
                {
                    int attributeIndex = (int)m_buffData.buffParams4;

                    m_attacher.SetAttributeExtra((AttributeEnum)attributeIndex, -m_attributeChange);
                }
                break;
        }
        if (m_goSkillBuffShow != null)
            GameObject.Destroy(m_goSkillBuffShow);
    }
    /// <summary>
    /// 伤害持续buff
    /// </summary>
    public void DamageOverTime()
    {
        float coefficient = BuffData.buffParams1;
        int mode = (int)BuffData.buffParams2;

        float damage = 0;
        switch (mode)
        {
            case 1:
                damage = coefficient;
                break;
            case 2:
                damage = m_attacher.Health * (coefficient / 10000.0f);
                break;
            case 3:
                damage = m_attacher.AttackDamage * (coefficient / 10000.0f);
                break;
            case 4:
                damage = m_attacher.Health * (coefficient / 10000.0f);
                break;
        }

        //m_attacher.AttackResult(damage);
        m_attacher.BuffDamage(this, damage);
    }

    bool RestrictSkill(Skill skill)
    {
        switch ((int)m_buffData.buffParams1)
        {
            case 1:     //禁止目标使用所有类型的技能
                break;
            case 2:     //禁止目标使用普攻
                break;
            case 3:     //禁止目标使用技能，普攻跟大招可正常使用
                break;
            case 4:     //禁止目标使用大招，普攻跟技能可正常使用
                break;
        }
        //1 = 只禁用技能
        //2 = 禁用技能的同时还冻结技能CD
        //3 = 只冻结技能CD
        switch ((int)m_buffData.buffParams2)
        {
            case 1:
                skill.Enable = false;
                break;
            case 2:
                skill.Enable = false;
                skill.FreezeCoolDown = true;
                break;
            case 3:
                skill.FreezeCoolDown = true;
                break;
        }
        return false;
    }
    /// <summary>
    /// 持续性治疗
    /// </summary>
    public void HealthOverTime()
    {
        float coefficient = m_buffData.buffParams1;
        int mode = (int)m_buffData.buffParams2;

        float health = 0;
        switch (mode)
        {
            case 1:
                health = coefficient;
                break;
            case 2:
                health = m_attacher.Health * (coefficient / 10000.0f);
                break;
            case 3:
                health = m_attacher.AttackDamage * (coefficient / 10000.0f);
                break;
            case 4:
                health = m_attacher.Health * (coefficient / 10000.0f);
                break;
        }

        m_attacher.CurrentHealth += health;
    }

    public void Halo()
    {
        m_playHaloCount--;
        m_lastTime = Time.time;
        string effectID = ((int)m_buffData.buffParams1).ToString();

        if (m_buffData.buffParams3 == 1)//以施加buff的单位为释放者
        {
            m_attacher.AddSkillEffect(bindSkill, effectID, bindSkill.bindActor);
        }
        else if (m_buffData.buffParams3 == 2)//以受到buff的单位为释放者
        {
            m_attacher.AddSkillEffect(bindSkill, effectID, m_attacher);
        }

    }

    public void ReboundDamage(GameEntity attacker, float damage)
    {
        float coefficient = m_buffData.buffParams1;
        float maxDamage = m_buffData.buffParams2;
        float reboundDamage = damage * coefficient;
        if (maxDamage > 0)
        {
            if (reboundDamage > maxDamage)
                reboundDamage = maxDamage;
        }
        attacker.ReboundDamage(reboundDamage);
    }


    //public bool CanAddBuff(Buff buff)
    //{
    //    if(BuffData.BuffType != BuffData.BuffEnum.Unbeatable)
    //        return true;
    //    //1 = 免疫控制效果类型的buff，对应buff表的buff大类：3
    //    //2 = 免疫异常状态类型的buff，对应buff表的buff大类：4
    //    //3 = 同时免疫控制效果和异常状态类型的buff，对应buff表的buff大类：3和4
    //    switch((int) BuffData.buffParams1)
    //    {
    //        case 1:
    //            if( buff.BuffData.MainType == 3 )
    //                return false;
    //            break;
    //        case 2:
    //            if (buff.BuffData.MainType == 4 )
    //                return false;
    //            break;
    //        case 3:
    //            if (buff.BuffData.MainType == 4 || buff.BuffData.MainType == 3)
    //                return false;
    //            break;
    //    }
    //    return false;
    //}

    public bool isUnbeatableBuff { get => m_buffType == BuffData.BuffEnum.Unbeatable; }

    //无敌期间，无敌单位免疫的buff
    public bool IsNeedRemoveBuff
    {
        get
        {
            if (m_buffType != BuffData.BuffEnum.Unbeatable) return false;
            return m_buffData.buffParams2 > 0;
        }
    }

    public bool isRebound { get => m_buffType == BuffData.BuffEnum.Rebound; }

    public bool isShield { get => m_buffType == BuffData.BuffEnum.Shield; }
    public void AttackResult(float damage)
    {
        if (m_buffType == BuffData.BuffEnum.LifeSteal)
        {
            m_attacher.CurrentHealth += (damage * (m_buffData.buffParams1 / 10000.0f));
        }
    }

    public void Stack()
    {
        if (m_stackTimeList.Count < m_buffData.MaxSupersition)
        {
            //开始叠加时，说明已经是第二个，要加上第一个
            if (m_stackTimeList.Count == 0)
                m_stackTimeList.Add(m_startTime);

            m_stackTimeList.Add(Time.time);

            //某个buff的时间超过了buff的持续时间，则移除掉
            for (int i = m_stackTimeList.Count - 1; i > 0; i--)
            {
                if (m_stackTimeList[i] + m_buffData.duration < Time.time)
                    m_stackTimeList.RemoveAt(i);
            }
             
            if (m_buffType == BuffData.BuffEnum.ChangeAttribute)
                ChangeAttribute();
            else if( m_buffType == BuffData.BuffEnum.AttackEnemiesToPowerUp)
                ApplyAttackEnemiesToPowerUp();
        }

    }

    public int StackCount 
    {
        get
        {
            int stack = 1;
            if (m_stackTimeList.Count > 1)
                stack = m_stackTimeList.Count;
            return stack;
        }
    }
    /// <summary>
    /// 是否有无视异常状态
    /// </summary>
    public bool isIgnoreAbnormal
    {
        get => m_buffType == BuffData.BuffEnum.IgnoreAbnormal; 
    }
    public bool IgnoreAbnormal(Buff buff)
    {
        BuffData.MainEnum targetMain = (BuffData.MainEnum)m_buffData.buffParams1;
        int targetSub  = (int)m_buffData.buffParams2;
        int select = (int)m_buffData.buffParams3;
        int coefficient = (int)m_buffData.buffParams4;

        if(targetSub != 0)
        {
            if (targetMain == buff.BuffData.MainType && targetSub == buff.BuffData.SubType)
                return true;
        }
        else
        {
            if (targetMain == buff.BuffData.MainType)
                return true;
        }
        return false;
    }

    //上次应攻击敌人而增加的属性值
    float m_lastPowerUpValue = 0 ;
    /// <summary>
    /// 计算攻击敌人而增加的属性值
    /// </summary>
    public void ApplyAttackEnemiesToPowerUp()
    {
        if (buffType != BuffData.BuffEnum.AttackEnemiesToPowerUp) return;

        if (m_attacher == null || m_attacher.isDeath) return;

        AttributeEnum attribute = (AttributeEnum)m_buffData.buffParams1;
         float originalValue = m_attacher.GetAttributeOriginalValue(attribute);
       //先还原值
        m_attacher.SetAttributeExtraOffset(attribute, -m_lastPowerUpValue);

        float coefficient = m_buffData.buffParams2 * StackCount;
        float value = originalValue * Mathf.Max(0, 1 + coefficient);
        float add = value - originalValue;
        m_lastPowerUpValue = add;

        m_attacher.SetAttributeExtraOffset(attribute, add);
    }
}

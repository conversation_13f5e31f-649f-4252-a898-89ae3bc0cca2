using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;

public partial class GridPathfinding : SceneSingleton<GridPathfinding>
{
    // 导入C++插件函数
    [DllImport("AStarPathfinding")]
    private static extern void InitializePathfinding(int numThreads);

    [DllImport("AStarPathfinding")]
    private static extern void ShutdownPathfinding();

    [DllImport("AStarPathfinding")]
    private static extern int CreatePathAsync(
        int width, int height,
        byte[] gridData,
        int startX, int startY,
        int endX, int endY,
        int obstacleRadius,
        PathResultCallback callback,
        IntPtr userData);

    [DllImport("AStarPathfinding")]
    private static extern bool IsPathComplete(int taskId);

    [DllImport("AStarPathfinding")]
    private static extern IntPtr GetPathResult(int taskId);

    [DllImport("AStarPathfinding")]
    private static extern void DestroyPath(IntPtr path);

    [DllImport("AStarPathfinding")]
    private static extern bool ClosePathfindingTask(int taskId);

    [DllImport("AStarPathfinding")]
    private static extern IntPtr CreatePathDirect(
        int width, int height,
        byte[] gridData,
        int startX, int startY,
        int endX, int endY,
        int obstacleRadius);

    [DllImport("AStarPathfinding")]
    private static extern IntPtr CreatePathJPS(
        int width, int height,
        byte[] gridData,
        int startX, int startY,
        int endX, int endY,
        int obstacleRadius);
    // 回调函数委托
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    private delegate void PathResultCallback(IntPtr result, IntPtr userData);

    // 路径结果结构体
    [StructLayout(LayoutKind.Sequential)]
    private struct PathResult
    {
        public int length;
        public IntPtr pathX;
        public IntPtr pathY;
    }

    private Dictionary<int, Action<List<Vector3>>> pendingPathRequests = new Dictionary<int, Action<List<Vector3>>>();

    private PathResultCallback pathResultCallback;


    public int FindPathAsync(Vector3 startWorld, Vector3 endWorld, Action<List<Vector3>> callback)
    {
        Vector2Int startGrid = WorldToGrid(startWorld);
        Vector2Int endGrid = WorldToGrid(endWorld);

        // 将二维网格转换为一维数组
        byte[] gridData = new byte[m_gridDimensions.x * m_gridDimensions.y];
        for (int x = 0; x < m_gridDimensions.x; x++)
        {
            for (int y = 0; y < m_gridDimensions.y; y++)
            {
                // 使用行优先顺序填充一维数组
                gridData[y * m_gridDimensions.x + x] = m_walkableGrid[x, y];
            }
        }

        // 调用C++插件异步寻找路径
        int taskId = CreatePathAsync(
            m_gridDimensions.x, m_gridDimensions.y,
            gridData,
            startGrid.x, startGrid.y,
            endGrid.x, endGrid.y,
                        1, // 障碍物半径
            pathResultCallback,
            IntPtr.Zero
        );

        // 存储回调函数
        if (taskId > 0)
        {
            pendingPathRequests[taskId] = callback;
        }
        else
        {
            // 创建任务失败
            callback?.Invoke(null);
        }

        return taskId;
    }
    private void OnPathResult(IntPtr resultPtr, IntPtr userData)
    {
        // 这个回调是在C++线程中调用的，不要在这里直接操作Unity对象
        // 结果会在Update中处理
    }


    /// <summary>
    /// 取消一个正在进行的寻路任务
    /// </summary>
    /// <param name="taskId">要取消的任务ID</param>
    /// <returns>如果成功取消返回true，否则返回false</returns>
    public bool CancelPathfinding(int taskId)
    {
        // 检查任务ID是否有效
        if (taskId <= 0)
        {
            Debug.LogWarning("尝试取消无效的寻路任务ID: " + taskId);
            return false;
        }

        // 检查任务是否在待处理列表中
        if (!pendingPathRequests.ContainsKey(taskId))
        {
            Debug.LogWarning("尝试取消不存在的寻路任务ID: " + taskId);
            return false;
        }

        // 调用C++插件取消任务
        bool result = ClosePathfindingTask(taskId);

        if (result)
        {
            // 如果成功取消，从待处理列表中移除
            Action<List<Vector3>> callback = pendingPathRequests[taskId];
            pendingPathRequests.Remove(taskId);

            // 调用回调函数，传递null表示路径未找到
            callback?.Invoke(null);

            Debug.Log("成功取消寻路任务ID: " + taskId);
        }
        else
        {
            Debug.LogError("取消寻路任务失败，ID: " + taskId);
        }

        return result;
    }
    public void FindDirectPath(Vector3 startWorld, Vector3 endWorld, Action<List<Vector3>> callback)
    {
        Vector2Int startGrid = WorldToGrid(startWorld);
        Vector2Int endGrid = WorldToGrid(endWorld);

        //Debug.Log($"FindDirectPath start {Time.realtimeSinceStartup}");
        byte[] gridData = new byte[m_gridDimensions.x * m_gridDimensions.y];
        for (int x = 0; x < m_gridDimensions.x; x++)
        {
            for (int y = 0; y < m_gridDimensions.y; y++)
            {
                // 使用行优先顺序填充一维数组
                gridData[y * m_gridDimensions.x + x] = m_walkableGrid[x, y];
            }
        }
        IntPtr resultPtr = CreatePathDirect(
            m_gridDimensions.x, m_gridDimensions.y,
            gridData,
            startGrid.x, startGrid.y,
            endGrid.x, endGrid.y, 1);

        if (resultPtr != IntPtr.Zero)
        {
            // 解析结果
            PathResult result = Marshal.PtrToStructure<PathResult>(resultPtr);

            // 创建路径点列表
            List<Vector3> path = new List<Vector3>(result.length);

            // 读取路径坐标
            int[] pathX = new int[result.length];
            int[] pathY = new int[result.length];

            Marshal.Copy(result.pathX, pathX, 0, result.length);
            Marshal.Copy(result.pathY, pathY, 0, result.length);

            // 转换为世界坐标
            for (int i = 0; i < result.length; i++)
            {
                Vector3 worldPos = new Vector3(
                    pathX[i] * gridSize + m_worldOrigin.x,
                    m_heightGrid[pathX[i], pathY[i]],
                    pathY[i] * gridSize + m_worldOrigin.z
                );
                path.Add(worldPos);
            }
            path.RemoveAt(0);
            callback?.Invoke(path);
            // 释放C++分配的内存
            DestroyPath(resultPtr);
        }
        //Debug.Log("FindDirectPath end  " + Time.realtimeSinceStartup);

    }

    public static List<Vector3> FindPathEnhance(Vector3 startWorld, Vector3 endWorld)
    {
        Vector2Int startGrid = WorldToGrid(startWorld);
        Vector2Int endGrid = WorldToGrid(endWorld);

        byte[] gridData = new byte[m_gridDimensions.x * m_gridDimensions.y];
        for (int x = 0; x < m_gridDimensions.x; x++)
        {
            for (int y = 0; y < m_gridDimensions.y; y++)
            {
                // 使用行优先顺序填充一维数组
                gridData[y * m_gridDimensions.x + x] = m_walkableGrid[x, y];
            }
        }
        IntPtr resultPtr = CreatePathDirect(
            m_gridDimensions.x, m_gridDimensions.y,
            gridData,
            startGrid.x, startGrid.y,
            endGrid.x, endGrid.y, 1);

        if (resultPtr != IntPtr.Zero)
        {
            // 解析结果
            PathResult result = Marshal.PtrToStructure<PathResult>(resultPtr);

            // 创建路径点列表
            List<Vector3> path = new List<Vector3>(result.length);

            // 读取路径坐标
            int[] pathX = new int[result.length];
            int[] pathY = new int[result.length];

            Marshal.Copy(result.pathX, pathX, 0, result.length);
            Marshal.Copy(result.pathY, pathY, 0, result.length);

            // 转换为世界坐标
            for (int i = 0; i < result.length; i++)
            {
                Vector3 worldPos = new Vector3(
                    pathX[i] * gridSize + m_worldOrigin.x,
                    m_heightGrid[pathX[i], pathY[i]],
                    pathY[i] * gridSize + m_worldOrigin.z
                );
                path.Add(worldPos);
            }
            path.RemoveAt(0);
            // 释放C++分配的内存
            DestroyPath(resultPtr);
            //Debug.Log("FindPathEnhance end  " + Time.realtimeSinceStartup);
            return path;
        }
        else
        {
            Debug.LogWarning($"Failed to find path {startWorld} {endWorld}  {Time.realtimeSinceStartup}");
            return null;
        }
    }
}

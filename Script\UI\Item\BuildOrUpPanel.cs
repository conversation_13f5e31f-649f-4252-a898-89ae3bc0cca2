using System;
using System.Collections.Generic;
using Excel.monster;
using TMPro;
using UnityEngine;
public class BuildOrUpPanel : MonoBehaviour
{
    [SerializeField] private TMP_Text NameText; // 名称
    [SerializeField] private TMP_Text NowLevelText; // 当前等级
    [SerializeField] private TMP_Text NextLevelText; // 下一级等级
    [SerializeField] private TMP_Text FunctionName; // 功能名称
    [SerializeField] private TMP_Text Desc; // 描述

    [Tooltip("属性")]
    [SerializeField] private TMP_Text HPText; // 血量
    [SerializeField] private TMP_Text NextLevelHPText; // 下一级血量差
    [SerializeField] private TMP_Text AttackText; // 攻击力
    [SerializeField] private TMP_Text NextLevelAttackText; // 下一级攻击力差
    [SerializeField] private TMP_Text SoldierText; // 兵力
    [SerializeField] private TMP_Text NextSoldierText; // 下一级兵力差
    [SerializeField] private TMP_Text IncomeText; // 收益
    [SerializeField] private TMP_Text NextLevelIncomeText; // 下一级收益差
    [SerializeField] private List<GameObject> nextShowObject = new List<GameObject>(); // 下一级展示的对象
    [SerializeField] private TMP_Text FunctionText; // 功能
    [SerializeField] private UIRingLoader ringLoader; // 加载进度条
    [SerializeField] private TMP_Text useCost; // 消耗
    
    [Tooltip("侧边属性")]
    [SerializeField] private GameObject AsideAttribute; // 侧边属性
    [SerializeField] private TMP_Text SoldierName; // 召唤物名称
    [SerializeField] private TMP_Text SoldierHp; // 召唤物血量

    [SerializeField] private GameObject SlotPrefab; // 插槽预制体
    [SerializeField] private Transform SlotContainer; // 插槽容器
    private BuildingBase buildingBase; // 当前建筑
    public Action<BuildingBase> buildingFinish; // 建筑完成
    // 界面展示配置
    private BuildingBase.EditUiShowAttribute viewConfig 
    {
        get {
            if(buildingBase == null) return new BuildingBase.EditUiShowAttribute();
            return buildingBase.editUiShowAttribute;
        }
    }
    public void SetBuildingData(BuildingBase buildingBase)
    {
        if(buildingBase == null) return;
        this.buildingBase = buildingBase;
        clearContent();
        string buildingName = Language.GetText(buildingBase.ArchitectureEntity.name);
        bool isMaxLv = buildingBase.CurrentLv >= buildingBase.MaxLv;
        NameText.text = buildingName;
        // 当前等级
        NowLevelText.text = $"Lv.{buildingBase.CurrentLv}";
        // 下一级等级
        if (!isMaxLv) {
            NextLevelText.text = $"Lv.{buildingBase.CurrentLv + 1}";
        } 
        // 功能名称
        FunctionName.text = Language.GetText(buildingBase.CurrentLv <= 0 ? 1001 : 1002);
        // 简介
        Desc.text = Language.GetText(buildingBase.ArchitectureLvEntity.desc);

        // 血量
        updateHp();
        // 攻击力
        updateAttrAttack();
        // 召唤战士数量
        updateSummon();
        // 收益
        updateAttrProfit();
        // 功能展示
        updateFunction();
        // 消耗展示
        updateUseCost();
        // 侧边栏
        updateAsideAttribute();
        // 宝石栏
        initSlotItem();
        // 下一级展示
        setNextEntity(!isMaxLv);
        // 执行回调
        ringLoader.onEnd(executeBuildOrUpgrade);
    }
    // 升级或建造完成监听
    public void onBuildingFinishing(Action<BuildingBase> action) 
    {
        buildingFinish = action;
    }
    // 根据状态展示属性
    private void setViewAttr(TMP_Text text, bool state) 
    {
        text.transform.parent.gameObject.SetActive(state);
    }
    // 升级消耗展示
    private void updateUseCost() 
    {
        setViewAttr(useCost, buildingBase.Cost > 0);
        if (buildingBase.Cost <= 0) return;
        useCost.text = buildingBase.Cost.ToString();
    }
    // HP 展示
    private void updateHp() 
    {
        setViewAttr(HPText, viewConfig.showHp);
        if(!viewConfig.showHp) return;
        float nowHp = buildingBase.ArchitectureLvEntity.hp;
        HPText.text = nowHp.ToString();
        if(buildingBase.CurrentLv >= buildingBase.MaxLv) return;
        float nextHp = buildingBase.NextLvArchitectureLvEntity.hp;
        NextLevelHPText.text = $"+{nextHp - nowHp}";
    }

    // 攻击力展示
    private void updateAttrAttack()
    {
        setViewAttr(AttackText, viewConfig.showAttcack); // 攻击力
        if(!viewConfig.showAttcack) return;
        float nowAtk = buildingBase.ArchitectureLvEntity.atk;
        AttackText.text = nowAtk.ToString();
        if(buildingBase.CurrentLv >= buildingBase.MaxLv) return;
        float nextAtk = buildingBase.NextLvArchitectureLvEntity.atk;
        NextLevelAttackText.text = $"+{nextAtk - nowAtk}";
    }
    // 兵力展示
    private void updateSummon() {
        setViewAttr(SoldierText, viewConfig.showSummon); // 召唤战士数量
        if(!viewConfig.showSummon) return;
        string nowStr = buildingBase.Effect?.effectParam1?.Split(':')[1];
        int nowSummonCount = nowStr == null ? 0 : int.Parse(nowStr);
        SoldierText.text = nowSummonCount.ToString();
        //  下一级
        if(buildingBase.CurrentLv >= buildingBase.MaxLv) return;
        string nextStr = buildingBase.nextEffect?.effectParam1?.Split(':')[1];
        int nextSummonCount = nextStr == null ? 0 : int.Parse(nextStr);
        NextSoldierText.text = $"+{nextSummonCount - nowSummonCount}";
    }
    // 收益展示
    private void updateAttrProfit() 
    {
        setViewAttr(IncomeText, viewConfig.showProfit); // 收益
        if (!viewConfig.showProfit) return;
        float nowProfit = buildingBase.ArchitectureLvEntity.produce;
        IncomeText.text = nowProfit.ToString();
        if(buildingBase.CurrentLv >= buildingBase.MaxLv) return;
        float nextProfit = buildingBase.NextLvArchitectureLvEntity.produce;
        NextLevelIncomeText.text = $"+{nextProfit - nowProfit}";
    }
    // 侧边属性展示
    private void updateAsideAttribute() 
    {
        string nowStrId = buildingBase.Effect?.effectParam1?.Split(':')[0];
        if(!viewConfig.showAsideAttribute || nowStrId == null) return;
        AsideAttribute.SetActive(true);
        monsterBaseEntity monster = ExcelData.Instance.GetMonster(int.Parse(nowStrId));
        string soldierName = Language.GetText(monster.name) ?? "";
        string soldierHp = monster.hp.ToString() ?? "0";
        // 战士名称
        SoldierName.text = soldierName;
        // 战士血量
        SoldierHp.text = soldierHp;
    }
    // 升级或建造完成
    private void executeBuildOrUpgrade() 
    {
        if(buildingBase == null) return;
        int cost = buildingBase.Cost * -1;
        EventDispatcher.TriggerEvent<int>(EventDispatcherType.JadeIncome, cost);
        if(buildingBase.CurrentLv <= 0) {
            buildingBase.Build();
            SuccessBuild();
            return;
        }
        if(buildingBase.CurrentLv < buildingBase.MaxLv) {
            buildingBase.Upgrade();
            SuccessBuild();
        }
    }
    // 升级或建造成功
    private void SuccessBuild() {
        if(buildingBase == null) return;
        buildingBase.EditOver = true;
        buildingFinish?.Invoke(buildingBase);
    }
    // 下一级展示
    void setNextEntity(bool state) {
        if(nextShowObject.Count <= 0) return;
        for(int i = 0; i< nextShowObject.Count; i++) {
            nextShowObject[i].SetActive(state);
        }
    }

    // 清空内容
    private void clearContent() 
    {
        NameText.text = "";
        NowLevelText.text = "";
        FunctionName.text = "";
        Desc.text = "";
        HPText.text = "";
        NextLevelText.text = "";
        NextLevelHPText.text = "";
        AttackText.text = "";
        NextLevelAttackText.text = "";
        SoldierText.text = "";
        NextSoldierText.text = "";
        IncomeText.text = "";
        NextLevelIncomeText.text = "";
    }
    
    // 功能展示
    void updateFunction() 
    {
        string renderText = "";
        ringLoader.DisableRing = false;
        // 是否满级
        if(buildingBase.CurrentLv >= buildingBase.MaxLv) 
        {
            renderText = Language.GetText(1003);
            ringLoader.DisableRing = true;
        }
        // 不是主城的需要判断主城等级
        else if (buildingBase != MainBase.Instance && MainBase.Instance.CurrentLv <= buildingBase.CurrentLv)
        {
            renderText = Language.GetText(1004);
            ringLoader.DisableRing = true;
        }

        else if (GameMain.Instance.jadeTotal < buildingBase.Cost) {
            renderText = Language.GetText(1005);
            ringLoader.DisableRing = true;
        }
        else if(buildingBase.CurrentLv <= 0) 
        {
            renderText = Language.GetText(1006);
        } 
        else 
        {
            renderText = Language.GetText(1007);
        }
        ringLoader.setText(renderText);
    }
    // 宝石展示
    private void initSlotItem() 
    {
        if (buildingBase == null || SlotPrefab == null) return;
        var config = buildingBase.editUiShowAttribute;
        if(!config.showEssenceSlot && !config.showGemSlot) return;
        SlotContainer.gameObject.SetActive(true);
        int slotNumber = buildingBase.MaxSlotNum;
        var insertedList = buildingBase.SlotList;
        var buildLv = buildingBase.CurrentLv;
        for (int i = 0; i < slotNumber; i++) 
        {
            GameObject slotItem = Instantiate(SlotPrefab, SlotContainer);
            var script = slotItem.GetComponent<BuildEditTabItem>();
            script.IsTab = false; // 是否顶部tab
            script.IsInserted = true; // 是否插槽
            script.Index = i; // 索引
            script.LockSlot = i >= buildLv; // 锁定
            GemGoods initialData = insertedList.Count > i ? insertedList[i] : null;
            script.SetData(initialData);
        }
    }
}
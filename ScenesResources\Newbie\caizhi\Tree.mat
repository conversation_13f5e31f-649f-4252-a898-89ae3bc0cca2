%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Tree
  m_Shader: {fileID: 4800000, guid: 7633a2fd8a3bed44791c9e952ff86e5d, type: 3}
  m_Parent: {fileID: 2100000, guid: 1ea36b74245d9fb4a94b728213d6f3d0, type: 2}
  m_ModifiedSerializedProperties: 16
  m_ValidKeywords:
  - _MK_ARTISTIC_PROJECTION_SCREEN_SPACE
  - _MK_LIGHT_BANDED
  - _MK_OUTLINE_DATA_UV7
  - _MK_OUTLINE_HULL_CLIP
  - _MK_OUTLINE_NOISE
  - _MK_RECEIVE_SHADOWS
  m_InvalidKeywords:
  - _FRESNELHIGHLIGHTS_ON
  - _MK_ARTISTIC_ANIMATION_STUTTER
  - _MK_DETAIL_BLEND_MIX
  - _MK_FRESNEL_HIGHLIGHTS
  - _MK_WRAPPED_DIFFUSE
  - _RECEIVESHADOWS_ON
  - _WRAPPEDLIGHTING_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    IGNOREPROJECTOR: False
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _GoochRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AdditionalLightsFalloff: 0
    - _AdditionalLightsThreshold: 1
    - _AdvancedTab: 1
    - _AlphaCutoff: 0.491
    - _ColorGrading: 0
    - _Cutoff: 0.491
    - _GoochRampIntensity: 1
    - _Light: 2
    - _LightBands: 3
    - _LightBandsScale: 0.5
    - _LightThreshold: 0.3
    - _LightTransmissionIntensity: 0
    - _Metallic: 0
    - _Outline: 3
    - _OutlineData: 1
    - _OutlineSize: 0
    - _ReceiveShadows: 1
    - _Smoothness: 1
    - _WrappedLighting: 1
    m_Colors:
    - _GoochBrightColor: {r: 0.9811321, g: 0.51371574, b: 0, a: 1}
    - _GoochDarkColor: {r: 0.3773585, g: 0.25115126, b: 0.097899616, a: 1}
    - _OutlineColor: {r: 0.754717, g: 0.754717, b: 0.754717, a: 0}
  m_BuildTextureStacks: []

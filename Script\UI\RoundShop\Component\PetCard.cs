using UnityEngine;
using System.Linq;
using Excel.shop;
using Excel.pet;
using System.Collections.Generic;
using System;
using UnityEngine.UI;
using Excel.text;
public enum PetAttrType {
    // 冰
    Ice = 1,
    // 火
    Fire = 2,
    // 电
    Flash = 3,
    // 风
    Wind = 4,
    // 暗
    Dark = 5,
}

[Serializable]
public class PetAttrToSprite {
    public PetAttrType type;
    public Sprite sprite;
}

public class PetItemEntity {
    public petLvEntity lvData;
    public petBaseEntity baseData;
}
[Serializable] 
public class PetGoods: BaseGoodsData
{
    public PetItemEntity dataEntity = new PetItemEntity();
}
public class PetCard : BaseGoodsItem
{
    [SerializeField] List<PetAttrToSprite> petAttrToSprite;
    [SerializeField] Image leftIcon; // 辅助宝石标签
    [SerializeField] GameObject DetailWindow; // 详情窗口
    private List<gecang_petEntity> weightData; //权重数据
    private List<petBaseEntity> BaseSourceData; // 源数据
    private List<petLvEntity> LvSourceData; // 等级数据
    private List<petLabelEntity> labelData; // 标签数据
    public void Awake() {
        // 加载Excel数据
        weightData = null;//Resources.Load<shopExcel>("Excel/shopExcel")?.gecang_petList;
        petExcel petExcelData = Resources.Load<petExcel>("Excel/petExcel");
        BaseSourceData = petExcelData?.petBaseList;
        LvSourceData = petExcelData?.petLvList;
        labelData = petExcelData?.petLabelList;
    }
    
    // 获取权重列表
    private List<int> getListWeight(List<gecang_petEntity> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    // 查询是否存在前置
    private bool isExistFront(int id) {
        List<BaseGoodsData> packageData = OtherInventory.Instance.buyList.FindAll(x=> x.goodsType == 999);
        if(packageData == null || packageData.Count <= 0) 
        {
            return false;
        }

        for (int i = 0; i < packageData.Count; i++)
        {
            PetGoods petGoods = packageData[i] as PetGoods;
            if(petGoods.dataEntity.lvData.lvId == id)
            {
                return true;
            }
        }
        return false;
    }
    // 查询购买次数
    private int getBuyNum(int id) {
        List<BaseGoodsData> packageData = OtherInventory.Instance.buyList.FindAll(x=> x.goodsType == 999);
        if(packageData == null || packageData.Count <= 0) 
        {
            return 0;
        }
        int result = 0;
        for (int i = 0; i < packageData.Count; i++)
        {
            PetGoods petGoods = packageData[i] as PetGoods;
            if(petGoods.dataEntity.lvData.lvId == id)
            {
                result++;
            }
        }
        return result;
    }
    // 判断回合限制
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    // 筛选
    private List<gecang_petEntity> filterData() {
        List<gecang_petEntity> result = new List<gecang_petEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 前置条件筛选
            bool hasExistFront = weightData[i].refFront < 0 || (weightData[i].refFront > 0 && isExistFront(weightData[i].refFront));
            // 购买次数限制筛选
            bool isMaxBuy = weightData[i].buyNumMax > 0 && getBuyNum(weightData[i].id) >= weightData[i].buyNumMax;
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);

            if(hasExistFront && !isMaxBuy && isRoundLimit) {
                result.Add(weightData[i]);
            }
        }
        return result;
    }

    private void renderLabel(int type) {
        if(petAttrToSprite != null && petAttrToSprite.Count <= 0) return;
        PetAttrToSprite data = petAttrToSprite.Find(x=> x.type == (PetAttrType)type);
        leftIcon.sprite = data.sprite;
        leftIcon.gameObject.SetActive(true);
    }

    private void setInfomation() 
    {
        PetGoods goods = baseGoodsData as PetGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(itemsDetail == null || dataEntity == null) return;
        // 定义好符文的品质数据做转换
         
        // 标签信息
        List<MutationsLabelData> labelsList = new List<MutationsLabelData>();
        string [] labelsSplit = dataEntity.lvData.label.Split('|');
        for(int i = 0; i < labelsSplit.Length; i++)
        {
            int labelId = int.Parse(labelsSplit[i]);
            petLabelEntity label = labelData.Find(x=> x.id == labelId);
            MutationsLabelData mutationsLabelData = new MutationsLabelData();
            mutationsLabelData.color = (BorderColor)label.quality;
            mutationsLabelData.text = Language.GetText(label.textId);
            labelsList.Add(mutationsLabelData);
        }
        itemsDetail.labels = labelsList;
        itemsDetail.Content = Language.GetText(dataEntity.lvData.desc);
        itemsDetail.render();
    }

    // 创建宝石实例
    private void renderUI(int id) {
        if(LvSourceData == null || LvSourceData.Count <= 0) return;
        petLvEntity target = LvSourceData.Find(x=> x.lvId == id);
        if(target == null) return;
        setPrice(target.price); // 价格
        setFrontImg(target.icon); // 图标
        petBaseEntity baseDataEntity = BaseSourceData.Find(x=> x.petId == target.petId);
        if(baseDataEntity == null) return;
        baseBorderItem.CurrentBorder = baseDataEntity.quality; // 边框颜色
        FlipEndEvent.AddListener(()=> renderLabel(baseDataEntity.petType)); // icon
        initGoods(target, baseDataEntity);
        // 详情
        setInfomation();
    }
    private void initGoods(petLvEntity target, petBaseEntity baseData)
    {
        PetGoods goods = new PetGoods();
        goods.dataEntity.lvData = target;
        goods.dataEntity.baseData = baseData;
        goods.id = target.lvId;
        goods.price = target.price;
        goods.sellDiscount = shopConfig.sellDiscount;
        goods.sellPrice = (int)Math.Round(target.price * shopConfig.sellDiscount, MidpointRounding.AwayFromZero);
        goods.icon = target.icon;
        goods.background = backImageUrl;
        goods.goodsType = (int)GoodsType.Gems;
        baseGoodsData = goods;
    }
    public override void LoadItem() {
        List<gecang_petEntity> filter = filterData(); // 前置筛选
        List<int> itemTypeList = getListWeight(filter); // 获取权重列表
        int index = WeightCalculator.GetWeightedRandomIndex(itemTypeList); // 根据权重获取随机索引
        int targetId = filter[index].id; // 根据索引获取对应的数据
        renderUI(targetId);
    }
    public override void LoadItem(int id) {
        renderUI(id);
    }
    // 购买
    public override void BuyItem() {
        PetGoods goods = baseGoodsData as PetGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || dataEntity.lvData == null || OtherInventory.Instance == null) {
            return;
        }
        // 校验空位
        // bool isAdd = OtherInventory.Instance.CheckAdd();
        // // 超出上限
        // if(!isAdd) return;
        // 支付
        bool payRes = GameMain.Instance.payJade(dataEntity.lvData.price);
        if(!payRes) return;
        // 添加到底部列表
        OtherInventory.Instance.AddItems(baseGoodsData);
    }
    /// <summary>
    ///  选择
    /// </summary>
    public override void ChooseItem() {
        PetGoods goods = baseGoodsData as PetGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || OtherInventory.Instance == null) {
            return;
        }
        OtherInventory.Instance.AddItems(baseGoodsData);
    }
}
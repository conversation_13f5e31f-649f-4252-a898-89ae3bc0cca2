using System.Collections.Generic;
using System.Collections;
using TMPro;
using UnityEngine;
using Excel.monster;
using Excel.unit;
using UnityEngine.EventSystems;
public class BeastDomainUI : MonoBehaviour {
    [SerializeField] private TMP_Text timeOutText; // 倒计时
    [SerializeField] private RectTransform Titles; // 标题列表
    [SerializeField] private GameObject Content; // 内容
    [SerializeField] private TMP_Text ShouWei; // 守卫
    [SerializeField] private TMP_Text JianZhu; // 建筑
    [SerializeField] private TMP_Text JiangLi; // 奖励
    [SerializeField] private GameObject TitlePrefab; // 标题预设
    [SerializeField] private GameObject ContentTitle; // 内容标题
    [SerializeField] private GameObject ListBuilding; // 建筑列表;
    [SerializeField] private GameObject ListMonster; // 怪列表;
    [SerializeField] private GameObject monsterUiItemPrefab; // 怪UI
    [SerializeField] private GameObject buildingUiItemPrefab; // 建筑UI
    [SerializeField] private GameObject TimerUI; // 倒计时UI
    [SerializeField] private TMP_Text TimerText; // 倒计时
    [SerializeField] private TMP_Text TimerViewText;
    [SerializeField] private TMP_Text HintText;
    [SerializeField] private BeastDetail beastDetail;
    [SerializeField] private BeastRewardItem beastRewardItem;
    private List<GameObject> MonsterItems;
    private List<GameObject> BuildingItems;
    private BeastDomain beastDomain;
    private bool toggleState = false; // Content的打开状态
    private float holdTime = 0f; // 按下F的累计时间
    private bool actionTriggered = false; // 是否触发过长按F动作
    private unitExcel unitExcelData; // 单位数据
    private float timeout = 0f; // 倒计时
    // 在现有字段区新增字段
    private Coroutine timerCoroutine;
    // 是否达到需求等级
    private bool isStartLevel {
        get {
            return beastDomain?.BeastData?.centerArchLv <= MainBase.Instance?.CurrentLv;
        }
    } 
    private void Awake()
    {
        initExcelData();
        initStaticText();
    }
    
    private void initStaticText() 
    {
        ShouWei.text = Language.GetText(1021);// 守卫
        JianZhu.text = Language.GetText(800016); // 建筑
        JiangLi.text = Language.GetText(1022); // 奖励
    }

    private void initExcelData() {
        if(unitExcelData == null)
        {
            unitExcelData = ExcelData.Instance.UnitExcel;
        }
    }

    private void OnEnable() {
        EventDispatcher.AddEventListener(EventDispatcherType.GameLose, defeatBeastDomain);
    }
    private void OnDisable() {
        EventDispatcher.RemoveEventListener(EventDispatcherType.GameLose, defeatBeastDomain);
    }

    private architectureEntity getBuildingData(int id) {
        initExcelData();
        return unitExcelData.architectureList.Find(element => element.id == id);
    }
    private architectureLvEntity getBuildingLvData(int id) {
        initExcelData();
        return unitExcelData.architectureLvList.Find(element => element.id == id);;
    }

    private void updateTitle()
    {
        if(beastDomain == null || beastDomain.BeastData == null) return;
        BeastDomainTitleUI titleUI = beastDomain.titleUI.GetComponent<BeastDomainTitleUI>();
        titleUI.UpdataTitle(beastDomain);
        BeastDomainTitleUI ContentTitleUI = ContentTitle.GetComponent<BeastDomainTitleUI>();
        ContentTitleUI.UpdataTitle(beastDomain);
        titleUI.Disable = beastDomain.IsDefeat || !isStartLevel;
        ContentTitleUI.Disable = beastDomain.IsDefeat || !isStartLevel;
    }
    // 更新怪列表
    private void updateMonsterList() {
        if(beastDomain == null || monsterUiItemPrefab == null) return;
        
        if(MonsterItems == null)
        {
            MonsterItems = new List<GameObject>();
        }
        // 清理旧数据
        if(MonsterItems.Count > 0) 
        {
            foreach(GameObject item in MonsterItems) 
            {
                Destroy(item);
            }
            MonsterItems.Clear();
        }

        string[] monsterSplit = beastDomain.BeastData.monsterShow.Split('|');
        foreach(string monster in monsterSplit) {
            string[] monsterConfig = monster.Split(':');
            int id = int.Parse(monsterConfig[0]);
            int count = int.Parse(monsterConfig[1]);
            monsterBaseEntity monsterData = ExcelData.Instance.GetMonster(id);
            GameObject item = Instantiate(monsterUiItemPrefab, ListMonster.transform);
            NextRoundMonstersItem script = item.GetComponent<NextRoundMonstersItem>();
            script.iconSprite = Resources.Load<Sprite>(monsterData.icon);
            script.monsterNumber = count;
            script.quality = monsterData.quality;
            script.onHoverIn = (PointerEventData eventData) => {
                beastDetail.gameObject.SetActive(true);
                beastDetail.target = item;
                beastDetail.Title = Language.GetText(monsterData.name);
                beastDetail.ContentText = Language.GetText(monsterData.desc);
                beastDetail.Show();
            };
            script.onHoverOut = (PointerEventData eventData) => {
                beastDetail.Hide();
                beastDetail.gameObject.SetActive(false);
            };
            script.LoadItem();
            MonsterItems.Add(item);
        }
    }
    // 更新建筑列表
    private void updateBuildingList() {
        if(beastDomain == null || buildingUiItemPrefab == null) return;
        if(BuildingItems == null)
        {
            BuildingItems = new List<GameObject>();
        }
        if(BuildingItems.Count > 0) 
        {
            foreach(GameObject item in BuildingItems) 
            {
                Destroy(item);
            }
            BuildingItems.Clear();
        }
        if(beastDomain.BeastData.architectureShow == "") return;
        string[] buildingSplit = beastDomain.BeastData.architectureShow.Split('|');
        foreach(string building in buildingSplit) {
            int id = int.Parse(building);
            architectureLvEntity buildingLvData = getBuildingLvData(id);
            architectureEntity buildingData = getBuildingData(buildingLvData.idGroup);
            GameObject item = Instantiate(buildingUiItemPrefab, ListBuilding.transform);
            Sprite icon = Resources.Load<Sprite>(buildingData.icon);
            BuildItemUi script = item.GetComponent<BuildItemUi>();
            script.icon = icon;
            script.level = buildingLvData.lv;
            script.loadItem();
            script.onHoverIn = (PointerEventData eventData) => {
                beastDetail.gameObject.SetActive(true);
                beastDetail.target = item;
                beastDetail.Title = Language.GetText(buildingData.name);
                beastDetail.ContentText = Language.GetText(buildingData.desc);
                beastDetail.Show();
            };
            script.onHoverOut = (PointerEventData eventData) => {
                beastDetail.Hide();
                beastDetail.gameObject.SetActive(false);
            };
            BuildingItems.Add(item);
        }

    }

    private void updateTheReward() 
    {
        if(beastDomain == null || beastRewardItem == null) return;
        beastRewardItem.onHoverIn = (PointerEventData eventData) => {
            beastDetail.gameObject.SetActive(true);
            beastDetail.target = beastRewardItem.gameObject;
            string title = Language.GetText(beastDomain.BeastData.rewardNameShow);
            beastDetail.Title = string.IsNullOrEmpty(title) ? "这是标题" : title;
            string content = Language.GetText(beastDomain.BeastData.rewardDescShow);
            beastDetail.ContentText = string.IsNullOrEmpty(content) ? "奖励描述可能字多一点，因为这个数据没有获取到" : content;
            beastDetail.Show();
        };
        beastRewardItem.onHoverOut = (PointerEventData eventData) => {
            beastDetail.Hide();
            beastDetail.gameObject.SetActive(false);
        };
    }

    private void updateHintText() {
        if (!isStartLevel) 
        {
            // 天枢核心等级不足
            HintText.text = Language.GetText(1004);
            HintText.color = Color.red;
        }
        else if (beastDomain.IsDefeat)
        {
            // 当前回合已挑战失败
            HintText.text = Language.GetText(1018);
            HintText.color = Color.red;
        } else {
            // 长按F键开始挑战
            HintText.text = Language.GetText(1019);
            HintText.color = Color.green;
        }
    }
    // 进入视线
    public void EnterViewScreen(BeastDomain script) {
        beastDomain = script;
        if(script.titleUI == null) 
        {
            GameObject go = Instantiate(TitlePrefab, Titles);
            beastDomain.selfUI = this;
            beastDomain.titleUI = go;
            // 更新标题
            updateTitle();
            return;
        }
        updateTitle();
        beastDomain.titleUI.SetActive(true);
    }
    // 离开视线
    public void ExitViewScreen(BeastDomain data) 
    {
        data?.titleUI?.SetActive(false);
    }
    // 切换
    public void toggle(BeastDomain data) 
    {
        if(data.FightState || GameMain.Instance.isBeastRound) return;
        beastDomain = data;
        toggleState = !toggleState;
        // 打开时更新UI
        if(toggleState) {
            updateMonsterList(); // 怪物列表
            updateBuildingList(); // 建筑列表
            updateTheReward(); // 奖励
            TimerViewText.text = beastDomain.BeastData.challengeTime.ToString() + "s";
            updateHintText();
        } else {
            RingLoader.Instance.Hide();
        }
        updateTitle();
        Content.SetActive(toggleState);
        Titles.gameObject.SetActive(!toggleState);
    }

    private void DoAction()
    {
        EventDispatcher.TriggerEvent(EventDispatcherType.BeastDomainStartFight);
        toggle(beastDomain);
        timeout = beastDomain.BeastData.challengeTime + RuneSystem.Instance.BeastDomainAdditionalTime;
        // 发送消息
        beastDomain.titleUI.SetActive(false);
        // 新增计时器逻辑
        TimerUI.SetActive(true); // 1.展示TimerUI
        if (timerCoroutine != null)
        {
            StopCoroutine(timerCoroutine);
        }
        timerCoroutine = StartCoroutine(CountdownTimer());
        beastDomain.StartFiting();
    }

    public void StopRoundTimer() {
        if (timerCoroutine != null)
        {
            StopCoroutine(timerCoroutine);
        }
    }
    
    // 新增协程方法
    private IEnumerator CountdownTimer()
    {
        while (timeout > 0)
        {
            timeout -= 1;
            
            // 更新文本
            TimerText.text = Mathf.CeilToInt(timeout).ToString()  + "s";
            
            // 根据规则设置颜色
            if (timeout >= 100)
            {
                TimerText.color = Color.green; // 绿色
            }
            else if (timeout > 10)
            {
                TimerText.color = new Color32(255, 165, 0, 255); // 橙色
            }
            else
            {
                TimerText.color = Color.red; // 红色
            }
            
            yield return new WaitForSeconds(1f);
        }
        
        // 倒计时结束处理
        GameLose();
        // 这里可以添加倒计时结束后的逻辑
        beastDomain.defeatBeastDomain(); // 结束战斗
    }
    public void GameLose() {
        beastDomain.titleUI.SetActive(true);
        StopGameAction(false);
    }
    public void winGame() {
        StopGameAction(true);
    }

    private void StopGameAction(bool isWin) {
        EventDispatcher.TriggerEvent(EventDispatcherType.BeastDomainEndFight);
        TimerText.color = isWin ? Color.green : Color.red;
        StopRoundTimer();
        timeout = 0;
        TimerUI.SetActive(false);
        holdTime = 0;
        updateTitle();
        updateHintText();
    }

    public void defeatBeastDomain() {
        if(!GameMain.Instance.isBeastRound) return;
        timeout = 0;
    }

    private void Update()
    {
        if(beastDomain == null || beastDomain.FightState || !toggleState || beastDomain.IsDefeat || !isStartLevel) return;
        if (Input.GetKey(KeyCode.F))
        {
            holdTime += Time.deltaTime;
            // 新增进度条控制
            RingLoader.Instance.Show();
            RingLoader.Instance.SetProgress(holdTime / 3f);
            // 持续按下且未触发过动作
            if (holdTime >= 3f && !actionTriggered)
            {

                DoAction();
                actionTriggered = true; // 防止重复触发
                RingLoader.Instance.Hide(); // 完成操作后隐藏
            }
        }
        else
        {
            if (holdTime > 0)
            {
                RingLoader.Instance.Hide(); // 松开时隐藏
            }
            // 松开时重置时间和标记
            holdTime = 0f;
            actionTriggered = false;
        }
    }
}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &71616561876246567
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3939844322891453753}
  m_Layer: 0
  m_Name: RigLFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3939844322891453753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 71616561876246567}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012239578, y: -0.0048797023, z: 0.034114677, w: 0.99933106}
  m_LocalPosition: {x: -0.053491075, y: 0, z: -0.000000038146972}
  m_LocalScale: {x: 0.9999987, y: 0.9999998, z: 1.0000014}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7727587605223918558}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &92606619100255530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5303038770870329880}
  m_Layer: 0
  m_Name: RigRPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5303038770870329880
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 92606619100255530}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: 0.117125824, y: 0.004488783, z: 0.09778633}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &323503361599853450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4163518614752448059}
  m_Layer: 0
  m_Name: RigHeadBone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4163518614752448059
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 323503361599853450}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000006664002, y: 8.4669435e-29, z: -1.2705494e-21, w: 1}
  m_LocalPosition: {x: -0.49453285, y: 0.09610081, z: 0.00000008530383}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3737338140398046859}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &420547618216725072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8730843318347810037}
  m_Layer: 0
  m_Name: + Back
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8730843318347810037
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 420547618216725072}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999997, y: 0.50000006, z: 0.5, w: 0.49999997}
  m_LocalPosition: {x: 0.13352661, y: -0.18530917, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4759032755052842699}
  m_Father: {fileID: 5386465769804823862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &509428100886659282
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8576634501204173538}
  m_Layer: 0
  m_Name: RigLFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8576634501204173538
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 509428100886659282}
  serializedVersion: 2
  m_LocalRotation: {x: 0.017789535, y: -0.017345412, z: 0.059156705, w: 0.99793947}
  m_LocalPosition: {x: -0.03831825, y: 0, z: 0.000000095367426}
  m_LocalScale: {x: 1.0000002, y: 0.9999998, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5942303251257862145}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &789926763478219509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5929236920278261956}
  m_Layer: 0
  m_Name: RigLPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5929236920278261956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 789926763478219509}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0.11712611, y: 0.0044879722, z: 0.100568734}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &813495516364018710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680515366828099792}
  m_Layer: 0
  m_Name: RigRPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680515366828099792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 813495516364018710}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7164266, y: -0.07365166, z: 0.04593946, w: 0.6922413}
  m_LocalPosition: {x: -0.20861332, y: 0.00000017166137, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7433482410997369251}
  - {fileID: 2442908316582967699}
  - {fileID: 1211307262157179675}
  m_Father: {fileID: 5181868671102997837}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &911524188104137587
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3172233859579702835}
  m_Layer: 0
  m_Name: RigLFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3172233859579702835
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 911524188104137587}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0009200043, y: 0.47066167, z: 0.0029013588, w: 0.88230854}
  m_LocalPosition: {x: -0.03561676, y: 0.00009338379, z: 0.05246233}
  m_LocalScale: {x: 0.9999987, y: 0.99999994, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7727587605223918558}
  m_Father: {fileID: 2282179686785320678}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1662843730574885309
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6100381333260652993}
  m_Layer: 0
  m_Name: RigLToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6100381333260652993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662843730574885309}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00053793885, y: -0.0016754845, z: 0.9954929, w: 0.09481994}
  m_LocalPosition: {x: -0.05733176, y: 0.0000000011920929, z: -0.000000009536743}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1254829350512662572}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1687652690044146907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8135939416289821632}
  m_Layer: 0
  m_Name: RigSpine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8135939416289821632
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1687652690044146907}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000003637839, y: 0.0000000053254308, z: 0.00037867192, w: 0.99999994}
  m_LocalPosition: {x: -0.12812133, y: 0, z: 0}
  m_LocalScale: {x: 1.0000004, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7900616672080264374}
  m_Father: {fileID: 7571107305310978017}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2008699550505359955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8647156247115061757}
  m_Layer: 0
  m_Name: RigNeck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8647156247115061757
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008699550505359955}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000007756446, y: 0.000000089830266, z: -0.09581775, w: 0.99539894}
  m_LocalPosition: {x: 0.005050278, y: -0.031543914, z: -0.0000000021330198}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3737338140398046859}
  m_Father: {fileID: 5386465769804823862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2205850419991937183
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1176758174716318068}
  m_Layer: 0
  m_Name: + Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1176758174716318068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2205850419991937183}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999973, y: 0.50000006, z: 0.50000024, w: 0.50000006}
  m_LocalPosition: {x: 1.2133555, y: 0.014542067, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4900495723326438496}
  - {fileID: 7058432097059874994}
  - {fileID: 511190143707987843}
  m_Father: {fileID: 3737338140398046859}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2220337323705402557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4430709471759480351}
  m_Layer: 8
  m_Name: anchorBottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4430709471759480351
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2220337323705402557}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2317872165495878411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2300289683223987218}
  m_Layer: 0
  m_Name: RigLForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2300289683223987218
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2317872165495878411}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006054405, y: 0.032705873, z: -0.18547376, w: 0.9820861}
  m_LocalPosition: {x: -0.27621734, y: 0, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2282179686785320678}
  m_Father: {fileID: 8036811787054292943}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2467371166286266352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1254829350512662572}
  m_Layer: 0
  m_Name: RigLToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1254829350512662572
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2467371166286266352}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014887555, y: -0.00571105, z: -0.3575502, w: 0.9337578}
  m_LocalPosition: {x: -0.13313864, y: -0.000051312447, z: -0.0000045490265}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6100381333260652993}
  m_Father: {fileID: 4327193136134257688}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2551280636513234064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4483444774701656802}
  - component: {fileID: 3609319709790083929}
  - component: {fileID: 6031948843061592685}
  - component: {fileID: 1988646359491888938}
  - component: {fileID: 2046731362911111711}
  - component: {fileID: 5694649299156480384}
  - component: {fileID: 1404007713783516881}
  m_Layer: 0
  m_Name: jinzhan01_bing
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4483444774701656802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.94602877, z: 0, w: 0.32408264}
  m_LocalPosition: {x: 8.6, y: 0.55, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3732360563317968262}
  - {fileID: 3249082682398596635}
  - {fileID: 5929236920278261956}
  - {fileID: 7382681456510555809}
  - {fileID: 5303038770870329880}
  - {fileID: 4430709471759480351}
  - {fileID: 6684447855019501089}
  - {fileID: 6778382159651056996}
  - {fileID: 1046125495182932641}
  - {fileID: 544966118840842029}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 142.18, z: 0}
--- !u!95 &3609319709790083929
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: f6605568d3cd20e4db1a0b19cfe83787, type: 3}
  m_Controller: {fileID: 9100000, guid: 349b08dac79b72b4096e1eafb4934e74, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &6031948843061592685
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0fe0c0abe69c93a4f81ad3fc643500a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_camp: 1
  m_targetValue: 100
  m_id: 0
  m_health: 0
  m_currentHealth: 0
  m_defense: 0
  m_attackDamage: 0
  m_attackPecent: 0
  m_healthPercent: 0
  m_attackSpeed: 0
  m_moveSpeed: 0
  m_coolingReduced: 0
  m_damageBonus: 0
  m_damageReduced: 0
  m_attackRange: 1
  m_raceEnum: 0
  m_attackSpeedChange: 1
  m_disableAction: 0
  m_Anchors:
  - {fileID: 6778382159651056996}
  - {fileID: 4430709471759480351}
  - {fileID: 6684447855019501089}
  - {fileID: 1046125495182932641}
  m_radius: 0.5
  m_normalAttackCount: 0
  m_StateMachine:
    m_destination: {x: 0, y: 0, z: 0}
    m_currentState: {fileID: 0}
    startLoopTime: 0
    startPosition: {x: 0, y: 0, z: 0}
    direction: {x: 0, y: 0, z: 0}
    distance: 0
    speed: 0
    beatBackFinish: 0
    isBeatBack: 0
    patrolPoints: []
    m_lastTime: 0
  m_idleState: {fileID: 11400000, guid: 3ceb064498b04784fb44b5598bac2ed4, type: 2}
  m_skillCastState: {fileID: 11400000, guid: 0417d335b5697f448bff0e41bde819fc, type: 2}
  m_runToSkillTargetState: {fileID: 11400000, guid: bf165ea46cb450a41b2ee8bd298edd87, type: 2}
  m_rushState: {fileID: 11400000, guid: e50da9d6eafea494287f398dc0c224a2, type: 2}
  m_guardState: {fileID: 0}
  m_deathState: {fileID: 11400000, guid: 0cc21a66f1586c64f9d6a732c22b2fc5, type: 2}
  m_beatBackState: {fileID: 0}
  m_polygonState: {fileID: 0}
  m_runToDestinationState: {fileID: 0}
  m_targetActor: {fileID: 0}
  horizontal: 0
  vertical: 0
  lastNormalizedTime: 0
--- !u!195 &1988646359491888938
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  m_Enabled: 0
  m_AgentTypeID: -334000983
  m_Radius: 0.6
  m_Speed: 3.5
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 0.5
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 1
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 0
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!136 &2046731362911111711
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 448
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 1, z: 0}
--- !u!54 &5694649299156480384
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &1404007713783516881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2551280636513234064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3b1a70ab69e5774eb62f9e4d3bac895, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_speed: 5
  m_stoppingDistance: 0.1
  m_rotationSpeed: 360
  m_autoBraking: 1
  m_pathStatus: 0
--- !u!1 &3034599539321066945
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2442908316582967699}
  m_Layer: 0
  m_Name: RigRFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2442908316582967699
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3034599539321066945}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0089416, y: 0.04077161, z: -0.035507806, w: 0.99849737}
  m_LocalPosition: {x: -0.13877112, y: 0.00067558285, z: 0.007317581}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5623375199711776576}
  m_Father: {fileID: 2680515366828099792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3371980788777216280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3628260238790785602}
  m_Layer: 0
  m_Name: RigLThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3628260238790785602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3371980788777216280}
  serializedVersion: 2
  m_LocalRotation: {x: -0.041628063, y: 0.99835473, z: -0.03812211, w: 0.010083431}
  m_LocalPosition: {x: 0.061012648, y: -0.0036894195, z: 0.11560108}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5968747928701185772}
  m_Father: {fileID: 7382681456510555809}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3839215563892278369
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1046125495182932641}
  m_Layer: 8
  m_Name: anchorBullet
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1046125495182932641
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3839215563892278369}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.84, z: 0.91}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4083027133452881231
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6684447855019501089}
  m_Layer: 8
  m_Name: anchorTop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6684447855019501089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4083027133452881231}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.905, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4293545752508561104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6032175136940797406}
  m_Layer: 0
  m_Name: RigLCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6032175136940797406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4293545752508561104}
  serializedVersion: 2
  m_LocalRotation: {x: -0.01381569, y: 0.82008797, z: 0.019317968, w: 0.5717445}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: 0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8036811787054292943}
  m_Father: {fileID: 5386465769804823862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4543874705868039742
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7382681456510555809}
  m_Layer: 0
  m_Name: RigPelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7382681456510555809
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543874705868039742}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: -0.00000013351438, y: 0.6149364, z: 0.0003728537}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3628260238790785602}
  - {fileID: 6962508546659384233}
  - {fileID: 7571107305310978017}
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4603757687079301541
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7058432097059874994}
  - component: {fileID: 8724445105049991879}
  - component: {fileID: 1252957911622611379}
  m_Layer: 0
  m_Name: Hair 01 Orange
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7058432097059874994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4603757687079301541}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1176758174716318068}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8724445105049991879
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4603757687079301541}
  m_Mesh: {fileID: 4300000, guid: 88a9ae2cd3294804680a9ff3e0bb265b, type: 3}
--- !u!23 &1252957911622611379
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4603757687079301541}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 82c8df39bd8ec2d4dba967e9e4bcff3a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4634270162961352717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8357614424556312989}
  - component: {fileID: 2318448784633408586}
  - component: {fileID: 6655343873764490522}
  m_Layer: 0
  m_Name: Shield Crude
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8357614424556312989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4634270162961352717}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 235036623629828968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2318448784633408586
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4634270162961352717}
  m_Mesh: {fileID: 4300000, guid: 6e6c2873930483d4aa033e7734014606, type: 3}
--- !u!23 &6655343873764490522
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4634270162961352717}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4672707958228949851
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7571107305310978017}
  m_Layer: 0
  m_Name: RigSpine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7571107305310978017
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4672707958228949851}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000014107537, y: 7.023184e-10, z: 0.022114862, w: 0.99975544}
  m_LocalPosition: {x: -0.10970802, y: -0.0049213637, z: 7.3929185e-10}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8135939416289821632}
  m_Father: {fileID: 7382681456510555809}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4981166104476887867
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3732360563317968262}
  - component: {fileID: 1053628697027053868}
  m_Layer: 0
  m_Name: Male Knight 01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3732360563317968262
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4981166104476887867}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1053628697027053868
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4981166104476887867}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ad5da9942a8613c4d8942c4e218c8a4b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 55ecab66a6436c74aa6baddc793db5a1, type: 3}
  m_Bones:
  - {fileID: 6784987070229855686}
  - {fileID: 5386465769804823862}
  - {fileID: 1924627084976057791}
  - {fileID: 3737338140398046859}
  - {fileID: 7900616672080264374}
  - {fileID: 8135939416289821632}
  - {fileID: 7571107305310978017}
  - {fileID: 7382681456510555809}
  - {fileID: 8036811787054292943}
  - {fileID: 6032175136940797406}
  - {fileID: 2300289683223987218}
  - {fileID: 2282179686785320678}
  - {fileID: 3172233859579702835}
  - {fileID: 7433482410997369251}
  - {fileID: 2680515366828099792}
  - {fileID: 5181868671102997837}
  - {fileID: 7727587605223918558}
  - {fileID: 6853726607725732814}
  - {fileID: 5942303251257862145}
  - {fileID: 3939844322891453753}
  - {fileID: 8576634501204173538}
  - {fileID: 3284990588481877065}
  - {fileID: 2442908316582967699}
  - {fileID: 5623375199711776576}
  - {fileID: 8206968341267002544}
  - {fileID: 7292231463653147592}
  - {fileID: 3628260238790785602}
  - {fileID: 6962508546659384233}
  - {fileID: 5968747928701185772}
  - {fileID: 4327193136134257688}
  - {fileID: 1254829350512662572}
  - {fileID: 6100381333260652993}
  - {fileID: 2796054337041378905}
  - {fileID: 5093087307263594753}
  - {fileID: 3990952481822613143}
  - {fileID: 6892327548935515814}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7382681456510555809}
  m_AABB:
    m_Center: {x: 0.010013312, y: 0.03311962, z: 0.00000011920929}
    m_Extent: {x: 0.6623696, y: 0.23220491, z: 0.57913274}
  m_DirtyAABB: 0
--- !u!1 &5002146530193169579
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 235036623629828968}
  m_Layer: 0
  m_Name: + L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &235036623629828968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5002146530193169579}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6878298, y: 0.7256808, z: -0.013955743, w: -0.009100941}
  m_LocalPosition: {x: -0.103, y: -0.073, z: 0.004}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8357614424556312989}
  m_Father: {fileID: 2282179686785320678}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &5075179683815430562
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4900495723326438496}
  - component: {fileID: 5702889770540689480}
  - component: {fileID: 2479272561696677651}
  m_Layer: 0
  m_Name: Head 01 Fair
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4900495723326438496
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5075179683815430562}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1176758174716318068}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5702889770540689480
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5075179683815430562}
  m_Mesh: {fileID: 4300000, guid: a50e3f7f3e580c843a6a31d558faccc9, type: 3}
--- !u!23 &2479272561696677651
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5075179683815430562}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7d64267e5eb8ca74d8c9c25ffd8dcd35, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5078661675976291895
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4759032755052842699}
  - component: {fileID: 1803073544101156236}
  - component: {fileID: 5604729726440905048}
  m_Layer: 0
  m_Name: Timber Pack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4759032755052842699
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5078661675976291895}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8730843318347810037}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1803073544101156236
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5078661675976291895}
  m_Mesh: {fileID: 4300000, guid: 448330daae26ea34e94a86e2c0f68570, type: 3}
--- !u!23 &5604729726440905048
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5078661675976291895}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5084566889189222133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3284990588481877065}
  m_Layer: 0
  m_Name: RigRFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3284990588481877065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5084566889189222133}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0012680653, y: 0.017850362, z: 0.079025835, w: 0.99671197}
  m_LocalPosition: {x: -0.051383447, y: 0.000000076293944, z: -0.000000038146972}
  m_LocalScale: {x: 1.0000008, y: 0.99999994, z: 0.9999992}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8206968341267002544}
  m_Father: {fileID: 7433482410997369251}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5147420063276926276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6892327548935515814}
  m_Layer: 0
  m_Name: RigRToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6892327548935515814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5147420063276926276}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00061404094, y: 0.0014642945, z: 0.98400944, w: 0.17810921}
  m_LocalPosition: {x: -0.057583522, y: 0.0000000023841857, z: 0.000000009536743}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3990952481822613143}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5215803283358703548
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6784987070229855686}
  m_Layer: 0
  m_Name: RigRCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6784987070229855686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5215803283358703548}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013815175, y: -0.8200881, z: 0.019317467, w: 0.57174426}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: -0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1924627084976057791}
  m_Father: {fileID: 5386465769804823862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5439809931767463553
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4327193136134257688}
  m_Layer: 0
  m_Name: RigLFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4327193136134257688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5439809931767463553}
  serializedVersion: 2
  m_LocalRotation: {x: 0.025897823, y: 0.02066495, z: -0.4329802, w: 0.9007944}
  m_LocalPosition: {x: -0.20310444, y: 0, z: 0}
  m_LocalScale: {x: 1.0000017, y: 0.99999857, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1254829350512662572}
  m_Father: {fileID: 5968747928701185772}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5500477206292856125
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7900616672080264374}
  m_Layer: 0
  m_Name: RigSpine3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7900616672080264374
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5500477206292856125}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000004032032, y: -0.0000000021958935, z: -0.092958696, w: 0.99566996}
  m_LocalPosition: {x: -0.14642449, y: 0.0000000023841857, z: 9.094947e-15}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5386465769804823862}
  m_Father: {fileID: 8135939416289821632}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5587030967260394993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2796054337041378905}
  m_Layer: 0
  m_Name: RigRCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2796054337041378905
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5587030967260394993}
  serializedVersion: 2
  m_LocalRotation: {x: 0.061178524, y: -0.008315115, z: 0.06785108, w: 0.99578327}
  m_LocalPosition: {x: -0.2570285, y: 0.0000067472456, z: 0.0000004196167}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5093087307263594753}
  m_Father: {fileID: 6962508546659384233}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5620832860127087685
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5968747928701185772}
  m_Layer: 0
  m_Name: RigLCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5968747928701185772
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5620832860127087685}
  serializedVersion: 2
  m_LocalRotation: {x: -0.060566243, y: 0.009119451, z: 0.079170175, w: 0.9949778}
  m_LocalPosition: {x: -0.25720719, y: 0.0000071310997, z: -0.00000037193297}
  m_LocalScale: {x: 0.9999997, y: 1.0000001, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4327193136134257688}
  m_Father: {fileID: 3628260238790785602}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5811910881723860646
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6962508546659384233}
  m_Layer: 0
  m_Name: RigRThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6962508546659384233
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5811910881723860646}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037409574, y: 0.9985059, z: 0.03866678, w: -0.009564557}
  m_LocalPosition: {x: 0.06101242, y: 0.000443881, z: -0.115600996}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2796054337041378905}
  m_Father: {fileID: 7382681456510555809}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5919872993967960061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8036811787054292943}
  m_Layer: 0
  m_Name: RigLUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8036811787054292943
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5919872993967960061}
  serializedVersion: 2
  m_LocalRotation: {x: 0.021966672, y: 0.30736107, z: 0.0071289386, w: 0.95131266}
  m_LocalPosition: {x: -0.1466811, y: 0.00000004053116, z: -0.000000076293944}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2300289683223987218}
  m_Father: {fileID: 6032175136940797406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5999377083192955090
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3737338140398046859}
  m_Layer: 0
  m_Name: RigHead
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3737338140398046859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5999377083192955090}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007756447, y: -0.00000008983025, z: 0.09581774, w: 0.99539894}
  m_LocalPosition: {x: -0.050812453, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4163518614752448059}
  - {fileID: 1176758174716318068}
  m_Father: {fileID: 8647156247115061757}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6536157204350200949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8206968341267002544}
  m_Layer: 0
  m_Name: RigRFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8206968341267002544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536157204350200949}
  serializedVersion: 2
  m_LocalRotation: {x: -0.012240466, y: 0.004878981, z: 0.034112923, w: 0.9993311}
  m_LocalPosition: {x: -0.05349116, y: 0, z: 0}
  m_LocalScale: {x: 1.0000008, y: 1, z: 0.9999992}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3284990588481877065}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6701459620771123648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1211307262157179675}
  m_Layer: 0
  m_Name: + R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1211307262157179675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6701459620771123648}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6878299, y: 0.72568065, z: -0.01395571, w: -0.00910123}
  m_LocalPosition: {x: -0.121, y: -0.049, z: 0.023}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2625090459954365528}
  m_Father: {fileID: 2680515366828099792}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &6707360911791144581
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2625090459954365528}
  - component: {fileID: 509913431608612705}
  - component: {fileID: 2922380696725133967}
  m_Layer: 0
  m_Name: Sword Rookie
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2625090459954365528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6707360911791144581}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1211307262157179675}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &509913431608612705
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6707360911791144581}
  m_Mesh: {fileID: 4300000, guid: 35c14bf3ddde05e46aadcc6d8b499ef1, type: 3}
--- !u!23 &2922380696725133967
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6707360911791144581}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6735817100056239765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5181868671102997837}
  m_Layer: 0
  m_Name: RigRForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5181868671102997837
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6735817100056239765}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0060543283, y: -0.032705836, z: -0.18547425, w: 0.98208606}
  m_LocalPosition: {x: -0.27621734, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2680515366828099792}
  m_Father: {fileID: 1924627084976057791}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6987897945128629004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3249082682398596635}
  m_Layer: 0
  m_Name: Rig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3249082682398596635
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6987897945128629004}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7212684924957244729
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1924627084976057791}
  m_Layer: 0
  m_Name: RigRUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1924627084976057791
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212684924957244729}
  serializedVersion: 2
  m_LocalRotation: {x: -0.021966748, y: -0.30736113, z: 0.0071289064, w: 0.95131266}
  m_LocalPosition: {x: -0.14668107, y: -0.00000004529953, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5181868671102997837}
  m_Father: {fileID: 6784987070229855686}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7273365479040170159
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3990952481822613143}
  m_Layer: 0
  m_Name: RigRToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3990952481822613143
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7273365479040170159}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014476012, y: 0.005883099, z: -0.37644693, w: 0.9263064}
  m_LocalPosition: {x: -0.12765375, y: -0.00000377655, z: 0.0000016212463}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6892327548935515814}
  m_Father: {fileID: 5093087307263594753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7286050637004179448
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5386465769804823862}
  m_Layer: 0
  m_Name: RigRibcage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5386465769804823862
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7286050637004179448}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000035377564, y: -0.00000008137332, z: 0.07053914, w: 0.997509}
  m_LocalPosition: {x: -0.17131469, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6032175136940797406}
  - {fileID: 8647156247115061757}
  - {fileID: 6784987070229855686}
  - {fileID: 8730843318347810037}
  m_Father: {fileID: 7900616672080264374}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7474966529988669939
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7727587605223918558}
  m_Layer: 0
  m_Name: RigLFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7727587605223918558
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7474966529988669939}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001269221, y: -0.017850326, z: 0.07902574, w: 0.99671197}
  m_LocalPosition: {x: -0.05138335, y: 0, z: 0}
  m_LocalScale: {x: 0.9999987, y: 0.99999994, z: 1.0000013}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3939844322891453753}
  m_Father: {fileID: 3172233859579702835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7675808175975719843
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 544966118840842029}
  - component: {fileID: 9040872853893208707}
  - component: {fileID: 4940439226083878959}
  - component: {fileID: 3574389121239290245}
  m_Layer: 13
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &544966118840842029
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7675808175975719843}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &9040872853893208707
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7675808175975719843}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4940439226083878959
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7675808175975719843}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a1f798269267cd74e8ba30ecb7d68be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &3574389121239290245
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7675808175975719843}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &7679476904302580825
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 511190143707987843}
  - component: {fileID: 5572919262522166821}
  - component: {fileID: 3146576609069914384}
  m_Layer: 0
  m_Name: Face Male 01 Orange
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &511190143707987843
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7679476904302580825}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1176758174716318068}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5572919262522166821
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7679476904302580825}
  m_Mesh: {fileID: 4300000, guid: a91e2c8bbd0e53447adeb7d239346501, type: 3}
--- !u!23 &3146576609069914384
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7679476904302580825}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 75060dba742f2f94b8726af8a3b05781, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7724290925713919733
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6853726607725732814}
  m_Layer: 0
  m_Name: RigLFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6853726607725732814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7724290925713919733}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0089414045, y: -0.040772736, z: -0.035507914, w: 0.9984973}
  m_LocalPosition: {x: -0.13877101, y: 0.00067565916, z: -0.007317886}
  m_LocalScale: {x: 1.0000002, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5942303251257862145}
  m_Father: {fileID: 2282179686785320678}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8483818703399669292
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7433482410997369251}
  m_Layer: 0
  m_Name: RigRFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7433482410997369251
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8483818703399669292}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00091957033, y: -0.47066116, z: 0.0029010912, w: 0.88230884}
  m_LocalPosition: {x: -0.03561699, y: 0.00009330749, z: -0.052462365}
  m_LocalScale: {x: 1.0000008, y: 0.99999994, z: 0.9999992}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3284990588481877065}
  m_Father: {fileID: 2680515366828099792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8704483775460844611
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6778382159651056996}
  m_Layer: 8
  m_Name: anchorCenter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6778382159651056996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8704483775460844611}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.795, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4483444774701656802}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8754539555271310260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5093087307263594753}
  m_Layer: 0
  m_Name: RigRFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5093087307263594753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8754539555271310260}
  serializedVersion: 2
  m_LocalRotation: {x: -0.026420366, y: -0.020399032, z: -0.40813246, w: 0.9123123}
  m_LocalPosition: {x: -0.20291302, y: 5.9604643e-10, z: 0}
  m_LocalScale: {x: 0.9999984, y: 1.0000013, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3990952481822613143}
  m_Father: {fileID: 2796054337041378905}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8892178346515886248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5942303251257862145}
  m_Layer: 0
  m_Name: RigLFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5942303251257862145
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8892178346515886248}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0017843612, y: 0.038817294, z: 0.15180485, w: 0.9876464}
  m_LocalPosition: {x: -0.040451143, y: 0, z: -0.000000038146972}
  m_LocalScale: {x: 1.0000002, y: 0.9999997, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8576634501204173538}
  m_Father: {fileID: 6853726607725732814}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8949497277010958653
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2282179686785320678}
  m_Layer: 0
  m_Name: RigLPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2282179686785320678
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8949497277010958653}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7164266, y: 0.07365076, z: 0.045940056, w: 0.6922413}
  m_LocalPosition: {x: -0.20861335, y: -0.00000022888183, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3172233859579702835}
  - {fileID: 6853726607725732814}
  - {fileID: 235036623629828968}
  m_Father: {fileID: 2300289683223987218}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8971012385649814270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7292231463653147592}
  m_Layer: 0
  m_Name: RigRFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7292231463653147592
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8971012385649814270}
  serializedVersion: 2
  m_LocalRotation: {x: -0.017786453, y: 0.017347906, z: 0.059156153, w: 0.9979395}
  m_LocalPosition: {x: -0.038318213, y: 0, z: 0.000000095367426}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5623375199711776576}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9122175945201667455
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5623375199711776576}
  m_Layer: 0
  m_Name: RigRFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5623375199711776576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9122175945201667455}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0017854307, y: -0.038816337, z: 0.15180461, w: 0.98764646}
  m_LocalPosition: {x: -0.04045116, y: -0.000000076293944, z: -0.000000038146972}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7292231463653147592}
  m_Father: {fileID: 2442908316582967699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

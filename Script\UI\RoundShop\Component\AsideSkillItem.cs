using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Excel.skill;
using Excel.shop;
using Excel.hero;
using TMPro;
using UnityEngine.EventSystems;
using UnityEngine.Events;
using System;
using Excel.lable;

public class AsideSkillItem : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    [SerializeField] GameObject KeyCodeIcon; // 按键图标
    [SerializeField] Image SkillIcon; // 技能图标
    [SerializeField] TMP_Text SkillName; // 技能名称
    [SerializeField] TMP_Text noGetSkill;  // 未获取技能
    [SerializeField] LabelIcon BeforeLabel; // 技能类型
    [SerializeField] LabelIcon AfterLabel; // 等级| 使用次数
    [SerializeField] TMP_Text sellPriceText; // 出售价格
    [SerializeField] GameObject Hover_window; // 技能描述
    [SerializeField] BtnEvent SelectItem; // 选中事件
    [SerializeField] GameObject SellBtn; // 出售按钮
    [SerializeField] BtnEvent SellEvent; // 出售按钮
    private List<heroSkillEntity> SourceData; // 源数据
    public heroSkillEntity skillEntity; // 技能数据
    public gecang_talismanEntity goodArtifactEntity; // 法宝的商品数据
    public UnityEvent<AsideSkillItem> SelectEvent; // 选中事件
    public UnityEvent<AsideSkillItem> SellEventClick; // 出售事件
    private float sellDiscount = shopConfig.sellDiscount; // 出售折扣
    public SkillTypeToCode skillType; // 技能类型
    public ItemsDetail itemsDetail; // 物品详情
    public int Index = 0; // 索引
    private bool select = false; // 选中状态
    public bool isInit = false; // 初始化状态
    private int sellPrice; // 出售价格
    public int SellPrice
    {
        get => sellPrice;
        set
        {
            sellPrice = value;
            sellPriceText.text = sellPrice.ToString();
        }
    }
    public bool Select 
    {
        get => select;
        set
        {
            select = value;
            SellBtn.SetActive(Select);
            if(Select) {
                transform.SetAsLastSibling();
            } else {
                transform.SetAsFirstSibling();
            }
        }
    }
    private int usage = 1;
    void Awake()
    {
        skillExcel excelData = Resources.Load<skillExcel>("Excel/SkillExcel");
        SourceData = ExcelData.Instance.HeroExcel.heroSkillList;
    }
    void Start()
    {
        SelectItem.OnClick.AddListener(onSelect);
        SellEvent.OnClick.AddListener(onSell);
    }
    // 鼠标移入
    public void OnPointerEnter(PointerEventData eventData)
    {
        if(itemsDetail != null && isInit) {
            itemsDetail.gameObject.SetActive(true);
        }
    }
    // 鼠标移出
    public void OnPointerExit(PointerEventData eventData)
    {
        if(itemsDetail != null) {
            itemsDetail.gameObject.SetActive(false);
        }
    }
    private void onSelect(PointerEventData eventData) 
    {
        if(isInit == false) return;
        Select = !Select;
        SelectEvent?.Invoke(this);
    }
    private void onSell(PointerEventData eventData) 
    {
        SellEventClick?.Invoke(this);
        ClearItem();
    }
    private void ClearItem() 
    {
        isInit = false;
        skillEntity = new heroSkillEntity();
        goodArtifactEntity = new gecang_talismanEntity();
        SellPrice = 0;
        Select = false;
        usage = 1;
        SetIcon(null);
        SetName(null);
        UpdateKeyCode();
        AfterLabel.RenderDefault("");
        AfterLabel.gameObject.SetActive(false);
        itemsDetail.resetData();
    }
    // 更新keycode展示
    private void UpdateKeyCode()
    {
        Transform _transform = KeyCodeIcon.transform;
        // 先禁用所有子物体
        for (int i = 0; i < _transform.childCount; i++)
        {
            _transform.GetChild(i).gameObject.SetActive(false);
        }
        bool hasKeyBinding = skillType == SkillTypeToCode.Treasure || skillType == SkillTypeToCode.Skill;
        // 有按键显示按键
        KeyCodeIcon.SetActive(hasKeyBinding);
        // 根据按键类型设置子物体激活
        if (hasKeyBinding)
        {
            int typeCurrent = Mathf.Clamp(Index - 2, 0, 3);
            List<string> indexToChildName = new List<string>() { "Space", "Z", "X" };
            Transform child = _transform.Find("KeyCode" + indexToChildName[typeCurrent]);
            if (child != null)
            {
                child.gameObject.SetActive(true);
            }
        }
    }

    private void SetSkillItemType() 
    {
        int index = Mathf.Clamp(Index, 0, 5);
        BeforeLabel.FindRender(index);
        BeforeLabel.gameObject.SetActive(true);
    }
    // 设置尾部label
    private void SetSkillAfterLabel() 
    {
        AfterLabel.gameObject.SetActive(true);
        // 消耗
        if(skillType == SkillTypeToCode.Treasure) {
            AfterLabel.RenderDefault(usage + "次");
            return;
        }
        // 技能
        AfterLabel.FindRender(skillEntity.skillLv);
    }
    private void setInfomation() 
    {
        if(itemsDetail == null || skillEntity == null) return;
        itemsDetail.resetData();
        bool isArtfact = skillType == SkillTypeToCode.Treasure;
        // 定义好符文的品质数据做转换
        itemsDetail.goodType = isArtfact ? (int)GoodsType.Artifact : (int)GoodsType.Skill;
        itemsDetail.Title = Language.GetText(skillEntity.name); // 名字
        if(isArtfact) {
            itemsDetail.useNumber = usage;
        } else {
            itemsDetail.level = skillEntity.skillLv; //等级
        }
        // 标签信息
        List<MutationsLabelData> labelsList = new List<MutationsLabelData>();
        string [] labelsSplit = skillEntity.label.Split('|');
        for(int i = 0; i < labelsSplit.Length; i++)
        {
            int labelId = int.Parse(labelsSplit[i]);
            lableEntity label = ExcelData.Instance.getLableEntity(labelId);
            MutationsLabelData mutationsLabelData = new MutationsLabelData();
            mutationsLabelData.color = (BorderColor)label.quality;
            mutationsLabelData.text = Language.GetText(label.textId);
            labelsList.Add(mutationsLabelData);
        }
        itemsDetail.labels = labelsList;
        itemsDetail.Content = Language.GetText(int.Parse(skillEntity.text));
        itemsDetail.render();
    }
    // 加载技能数据
    public void LoadItem(heroSkillEntity data)
    {
        if(data == null) return;
        skillEntity = data;
        SellPrice = (int)Math.Round(data.price * sellDiscount, MidpointRounding.AwayFromZero);
        SetIcon(skillEntity.icon);
        SetName(Language.GetText(skillEntity.name));
        UpdateKeyCode();
        SetSkillAfterLabel();
        isInit = true;
        setInfomation();
    }
    public void Init(int index) 
    {
        Index = index;
        int MaxIndex = (int)SkillTypeToCode.Treasure;
        int CurrentType = index + 1;
        skillType = (SkillTypeToCode)Mathf.Clamp(CurrentType, 0, MaxIndex);
        SetSkillItemType();
    }
    public void SetName(string name) 
    {
        string _name = "";
        bool isName = !string.IsNullOrEmpty(name);
        if(isName) {
            string findName = Language.GetText(skillEntity.name);
            _name = findName;
        }
        SkillName.text = _name;
        noGetSkill.gameObject.SetActive(!isName);
        SkillName.gameObject.SetActive(isName);
    }
    public void SetIcon(string url) 
    {
        // 清理旧数据
        SkillIcon.sprite = null; // 释放原有精灵引用
        SkillIcon.gameObject.SetActive(false); // 先禁用防止残留显示

        // 设置新数据
        if(!string.IsNullOrEmpty(url)) 
        {
            SkillIcon.sprite = Resources.Load<Sprite>(url);
            SkillIcon.gameObject.SetActive(true);
        }
    }
    public bool upLevel()
    {
        // 是否是一次性
        bool disposable = skillType == SkillTypeToCode.Treasure;
        if(disposable)
        {
            usage++;
            if(itemsDetail != null) {
                itemsDetail.useNumber = usage;
                itemsDetail.initLabels();
            }
        } 
        else 
        {    
            int nextId = skillEntity.nextLvSkill;
            if(nextId < 0) {
                Debug.Log("已满级----------");
                return false;
            }
            heroSkillEntity newSkillEntity = SourceData.Find(x=> x.id == nextId);
            if(newSkillEntity == null) 
            {
                Debug.Log("未检索到下一级数据---------");
                return false;
            }
            LoadItem(newSkillEntity);
        }
        SetSkillAfterLabel();
        return true;
    }
}
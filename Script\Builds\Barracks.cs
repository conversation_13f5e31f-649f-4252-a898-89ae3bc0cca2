using Excel.unit;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;

/// <summary>
/// 卫灵营
/// </summary>
public class Barracks : BuildingBase
{
    private float m_lastReviveTime;
    private List<Actor> m_actorList = new List<Actor>();
    private int m_actorID;
    private int m_changeActorID;
    [SerializeField]
    private int m_actorCount;
    private int m_extraQuantity;

    private List<int> actorAddSkillIds = new List<int>();
    // 额外数量
    private int? additionalQuantities;
    private float m_reviveDuration;
    private float m_reviveCount;
    private float m_upgradeRate;
    //属性提升百分比
    //private float m_attributeCoefficient = 0;
    public override void Start()
    {
        IdGroup = 402;
        m_currentLv = 0;
        editUiShowAttribute.showSummon = true; // 显示召唤
        editUiShowAttribute.showEssenceSlot = true; // 显示精华插槽
        SetData();

        base.Start();
    }

    public override void Renew()
    {
        summonedSoldiers();
        base.Renew();
    }

    public override void ExtendSet()
    {
        if (m_Effect == null) return;
        
        string[] array = m_Effect.effectParam1.Split(':');
        m_actorID = int.Parse(array[0]);
        m_actorCount = int.Parse(array[1]);

        //m_reviveDuration = int.Parse(m_Effect.effectParam3);
        //m_reviveCount = int.Parse(m_Effect.effectParam4);

        if( !string.IsNullOrEmpty(m_Effect.effectParam2))
            attributeCoefficient = float.Parse(m_Effect.effectParam2);

        summonedSoldiers();
    }

    // 改变阵营
    public override void changeActorCamp(CampEnum camp)
    {
        for (int i = 0; i < m_actorList.Count; i++)
        {
            GameMain.Instance.changeActorCamp(m_actorList[i], camp);
        }
    }

    public override void Build()
    {
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseBuildPopUp);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonBuildUI);
        Upgrade();
        base.Build();
    }

    public override void Upgrade()
    {
        m_currentLv++;
        SetData();
        base.Upgrade();
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseOptionsUI);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonUpgradeUI);
    }
    public override void OnActorDeath(GameEntity monster)
    {
        if(m_actorList.Exists( actor => actor == monster)) 
            m_actorList.Remove( (Actor)monster );
    }
    public override void Update()
    {
        //if (!m_isDestroyed)
        //{
        //    if (m_actorList.Count < m_actorCount && (Time.time - m_lastReviveTime) > m_reviveDuration)
        //    {
        //        m_lastReviveTime = Time.time;

        //        Transform trans = m_spawnPoint[m_actorList.Count].transform;

        //        Monster monster = GameMain.Instance.CreateMonster(m_actorID, CampEnum.Ally, trans.position);
        //        if (monster != null)
        //            m_actorList.Add(monster);

        //    }
        //}
        base.Update();
    }
    /// <summary>
    /// 获得没有Actor站立的出生点
    /// </summary>
    /// <returns></returns>
    private Transform GetEmptySpawnPoint()
    {
        for (int i = 0; i < m_spawnPoint.Length; i++)
        {
            if( m_spawnPoint[i].isEmpty)
                return m_spawnPoint[i].transform;
        }
        return null;
    }

    // public override void OpenResonanceUI()
    // {
    //     m_candidateElfList.Clear();

    //     Elf elf;
    //     for (int i = 0; i < GameMain.Instance.Role.elfList.Count; i++)
    //     {
    //         elf = (Elf)GameMain.Instance.Role.elfList[i];
    //         if (elf.ResonanceList.Exists(r => r.buildingID == IdGroup) && elf.elfActionType == ElfActionType.None)
    //             m_candidateElfList.Add(elf);
    //     }
    //     EventDispatcher.TriggerEvent(EventDispatcherType.OpenResonanceUI, this);
    // }



    /// <summary>
    /// 入驻兵营的灵兽将带领士兵
    /// </summary>
    /// <param name="index"></param>
    public override void EnterElf(int index)
    {
        if (m_candidateElfList.Count == 0) return;

        m_bindElf = m_candidateElfList[index];
        //GameMain.Instance.CurrentRole.RemoveElf(m_bindElf);
        m_bindElf.bindBuilding = this;
        m_bindElf.elfActionType = ElfActionType.Leader;
        for (int i = 0; i < m_actorList.Count; i++)
        {
            m_bindElf.AddFollower(m_actorList[i]);
        }
    }
    public override void ExitElf()
    {
        if (m_bindElf == null) return;
        for (int i = 0; i < m_actorList.Count; i++)
        {
            m_bindElf.RemoveFollower(m_actorList[i]);
        }
        //GameMain.Instance.CurrentRole.AddElf(m_bindElf);
        if (m_bindElf != null)
        {
            m_bindElf.bindBuilding = null;
            m_bindElf.elfActionType = ElfActionType.None;

            m_bindElf = null;
        }
    }
    public override void SetElementSkill(List<int> skills)
    {
        if (skills == null || skills.Count == 0) return;
        m_elementSkillList = skills;
        for (int i = 0; i < m_actorList.Count; i++)
        {
            m_actorList[i].ClearSkill();
            for (int j = 0; j < skills.Count; j++)
            {
                //m_actorList[i].Health = 9000000;
                //m_actorList[i].CurrentHealth = 9000000;
                if (skills[j] == 0) continue;
                m_actorList[i].AddSkill(skills[j]);
            }
        }

    }

    // public override void OnTriggerExit(Collider other)
    // {
    //     if (other.gameObject.layer != LayerMask.NameToLayer("Player")) return;
    //     if (SceneMain.Instance.CameraHeightController.IsSuperView || SceneMain.Instance.CameraHeightController.IsSwitchingView) return;

    //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseResonanceUI);
    //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseOptionsUI);

    //     base.OnTriggerExit(other);
    // }

    public void Spawn()
    {
        // Monster monster = null;
        // int offset = m_actorCount - m_actorList.Count;
        // int start = m_actorList.Count;
        // int end = m_actorList.Count + offset;
        // for (int i = start; i < end; i++)
        // {
        //     monster = GameMain.Instance.CreateMonster(m_actorID, this.Camp, m_spawnPoint[i].transform.position);

        //     if (monster != null)
        //     {
        //         monster.AttributeEnhancement(m_attributeCoefficient);
        //         m_actorList.Add(monster);
        //     }
        // }
    }
    // 添加宝石技能
    public override void setGemSkillToChild(string val, bool isAdd)
    {
        gemstoneHandlingSkills(val, isAdd);
    }

    // 添加或移除宝石技能
    public void gemstoneHandlingSkills(string val, bool isAdd) 
    {
        if (string.IsNullOrEmpty(val)) return;
        string[] gemSkills = val.Split("|");
        for (int i = 0; i < gemSkills.Length; i++)
        {
            int skillId = int.Parse(gemSkills[i]);
            applyOneSkillToActors(skillId, isAdd);
            // 缓存到列表 重新创建小兵时用
            if (isAdd)
                actorAddSkillIds.Add(skillId);
            else
                actorAddSkillIds.Remove(skillId);
        }
    }
    // 添加或移除技能
    private void applyOneSkillToActors(int skillId, bool isAdd)
    {
        for (int i = 0; i < m_actorList.Count; i++)
        {
            if (isAdd)
                m_actorList[i].AddSkill(skillId);
            else
                m_actorList[i].RemoveSkill(skillId);
        }
    }
    // 角色添加宝石技能
    private void addSkillsToActor(Actor actor)
    {
        for (int j = 0; j < actorAddSkillIds.Count; j++)
        {
            actor.AddSkill(actorAddSkillIds[j]);
        }
    }

    // 创建兵营的兵种
    private void summonedSoldiers()
    {
        Monster monster = null;
        int offset = m_actorCount + m_extraQuantity - m_actorList.Count; // 还需创建数量
        int start = m_actorList.Count; // 当前数量
        int end = m_actorList.Count + offset; // 创建数量
        int id = m_changeActorID > 0 ? m_changeActorID : m_actorID;
        for (int i = start; i < end; i++)
        {
            monster = GameMain.Instance.CreateMonster(id, Camp, m_spawnPoint[i].transform.position);

            if (monster != null)
            {
                monster.AttributeEnhancement(m_attributeCoefficient);
                addSkillsToActor(monster);
                m_actorList.Add(monster);
            }
        }
    }
    // 改变兵营的兵种
    public override void gemChangeMonsterId(int actorId)
    {
        m_changeActorID = actorId;
        clearActorList();
        summonedSoldiers();
    }
    // 改变兵营的数量
    public override void gemChangeMonsterCount(int actorCount)
    {
        m_extraQuantity = actorCount;
        clearActorList();
        summonedSoldiers();
    }
    // 销毁兵营
    public override void destroyBuilding()
    {
        clearActorList();
        base.destroyBuilding();
    }

    public void clearActorList()
    {
        for (int i = 0; i < m_actorList.Count; i++)
        {
            Destroy(m_actorList[i].gameObject);
        }
        m_actorList.Clear();
    }
}
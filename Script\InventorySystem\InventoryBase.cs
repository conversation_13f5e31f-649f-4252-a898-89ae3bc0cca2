using System;
using System.Collections.Generic;
using UnityEngine;
/// <summary>
/// 背包系统基类
/// </summary>

public class BaseGoodsData
{
    public int id = 0;
    public string icon;
    public string background;
    public float price = 0;
    public int sellPrice = 0;
    public float sellDiscount = 0;
    public int goodsType = 0;
}

[Serializable]
public class InventoryBase
{
    // 背包物品槽
    [Serializable]
    public class InventorySlot
    {
        public BaseGoodsData item; // 物品
        public int amount; // 数量

        public InventorySlot(BaseGoodsData item, int amount)
        {
            this.item = item;
            this.amount = amount;
        }
    }
    public int maxAmount = 1; // 每一格最大数量
    public int maxCount; // 最大数量
    public int extraCount; // 额外容量
    public bool allowRepeat = false; // 是否允许重复
    public bool allowSell = true; // 是否允许出售
    
    // 背包物品列表
    public List<InventorySlot> items = new List<InventorySlot>();
    /// <summary>
    /// 添加物品
    /// </summary>
    /// <param name="newItem">新物品</param>
    /// <param name="amount">数量</param>
    /// <returns>是否成功</returns>
    public bool AddItem(BaseGoodsData newItem, int amount = 1)
    {
        if (RemainingSpace <= 0) return false;
        // 添加的数量
        int addAmount = 0;
        // 尝试堆叠已有物品
        if (HasItem(newItem) && !allowRepeat) {
            InventorySlot slot = items.Find(x => x.item.id == newItem.id);
            // 剩余空间
            int spaceLeft = maxAmount - slot.amount;
            // 取可用值
            addAmount = Mathf.Min(spaceLeft, amount);
            slot.amount += addAmount;
            amount -= addAmount;
            return amount <= 0;
        }
        // 取可用空间
        addAmount = Mathf.Min(amount, maxAmount);
        // 创建新物品槽
        items.Add(new InventorySlot(newItem, addAmount));
        amount -= addAmount;
        return amount <= 0;
    }

    /// <summary>
    /// 修改物品
    /// </summary>
    /// <param name="oldData">旧物品</param>
    /// <param name="newData">新物品</param>
    /// <param name="amount">数量</param>
    /// <returns></returns>
    public bool ModifyItem(BaseGoodsData oldData, BaseGoodsData newData, int amount = 1)
    {
        InventorySlot find = FindItem(oldData);
        if(find == null)
        {
            Debug.Log("物品不存在！");
            return false;
        }
        find.item = newData;
        find.amount = amount;
        return true;
    }

    /// <summary>
    /// 移除物品
    /// </summary>
    /// <param name="data">物品</param>
    /// <param name="amount">数量</param>
    /// <returns>是否移除</returns>
    public bool RemoveItem(BaseGoodsData data, int amount = 1)
    {
        for (int i = items.Count - 1; i >= 0; i--)
        {
            if (items[i].item == data)
            {
                int removeAmount = Mathf.Min(amount, items[i].amount);
                items[i].amount -= removeAmount;
                amount -= removeAmount;

                if (items[i].amount <= 0)
                    items.RemoveAt(i);

                if (amount <= 0)
                {
                    return true;
                }
            }
        }
        return false;
    }
    /// <summary>
    /// 查询物品
    /// </summary>
    /// <param name="data">物品</param>
    /// <returns>物品和数量</returns>
    public InventorySlot FindItem(BaseGoodsData data)
    { 
        return items.Find(x => x.item.id == data.id);
    }
    /// <summary>
    /// 查询全部物品
    /// </summary>
    /// <returns>物品列表 item</returns>
    private List<BaseGoodsData> FindAllItem()
    { 
        List<BaseGoodsData> result = new List<BaseGoodsData>();
        for (int i = 0; i < items.Count; i++)
        {
            result.Add(items[i].item);
        }
        return result;
    }
    /// <summary>
    /// 检查物品是否存在
    /// </summary>
    /// <param name="data">物品</param>
    /// <returns>是否存在</returns>
    public bool HasItem(BaseGoodsData data)
    {
        InventorySlot slot = FindItem(data);
        return slot != null;
    }
    /// <summary>
    /// 检查物品是否可以添加
    /// </summary>
    /// <param name="data">物品</param>
    /// <returns>是否可以添加</returns>
    public bool CheckAdd(BaseGoodsData data)
    { 
        // 检查容量
        if (ItemCount >= maxCount) 
        {
            Debug.Log("背包空间不足！");
            EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, Language.GetText(1014));
            return false;
        };
        // 检查是否重复
        if (!allowRepeat && FindItem(data) != null) 
        {
            Debug.Log("物品已存在！");
            EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, Language.GetText(1048));
            return false;
        };

        return true;
    }
    /// <summary>
    /// 清空全部数据
    /// </summary>
    public void Clear()
    {
        items.Clear();
    }
    // 当前物品数量
    public int ItemCount => items.Count;
    // 剩余空间
    public int RemainingSpace => maxCount + extraCount - items.Count;
    // 获取所有物品
    public List<BaseGoodsData> AllItems => FindAllItem();
}
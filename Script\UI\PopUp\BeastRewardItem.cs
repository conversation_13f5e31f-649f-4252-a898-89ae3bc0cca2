using System;
using UnityEngine;
using UnityEngine.EventSystems;

public class BeastRewardItem : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    public Action<PointerEventData> onHoverIn;
    public Action<PointerEventData> onHoverOut;

    public void OnPointerEnter(PointerEventData eventData)
    {
        onHoverIn?.Invoke(eventData);
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        onHoverOut?.Invoke(eventData);
    }
}
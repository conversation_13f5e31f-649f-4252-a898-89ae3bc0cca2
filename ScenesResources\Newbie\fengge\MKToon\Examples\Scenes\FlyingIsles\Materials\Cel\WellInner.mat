%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: WellInner
  m_Shader: {fileID: 4800000, guid: df695d70c539c3d4c87b79de307ab7bb, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _MK_ARTISTIC_PROJECTION_SCREEN_SPACE
  - _MK_ENVIRONMENT_REFLECTIONS_ADVANCED
  - _MK_LIGHT_CEL
  - _MK_RECEIVE_SHADOWS
  - _MK_SPECULAR_ISOTROPIC
  m_InvalidKeywords:
  - _FRESNELHIGHLIGHTS_ON
  - _MK_ARTISTIC_ANIMATION_STUTTER
  - _MK_DETAIL_BLEND_MIX
  - _MK_FRESNEL_HIGHLIGHTS
  - _MK_WRAPPED_DIFFUSE
  - _RECEIVESHADOWS_ON
  - _WRAPPEDLIGHTING_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    IGNOREPROJECTOR: False
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DiffuseRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveBorderRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DrawnMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GoochBrightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GoochDarkMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GoochRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HatchingBrightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HatchingDarkMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightTransmissionRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap:
        m_Texture: {fileID: 2800000, guid: b376780a16f30e141824d5baae6b8f5e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SketchMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThresholdMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VertexAnimationMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AdditionalLightsFalloff: 1
    - _AdditionalLightsThreshold: 0.5
    - _AdvancedTab: 0
    - _AlbedoMapIntensity: 1
    - _AlembicMotionVectors: 0
    - _AlphaClipping: 0
    - _AlphaCutoff: 0.5
    - _Anisotropy: 0
    - _Artistic: 0
    - _ArtisticFrequency: 1
    - _ArtisticProjection: 1
    - _ArtisticShadowFilter: 0
    - _Blend: 0
    - _BlendDst: 0
    - _BlendDstAlpha: 0
    - _BlendSrc: 1
    - _BlendSrcAlpha: 1
    - _Brightness: 1
    - _BumpScale: 1
    - _ColorGrading: 0
    - _Contrast: 1
    - _Cutoff: 0.5
    - _DetailBlend: 0
    - _DetailMix: 0.5
    - _DetailNormalMapIntensity: 1
    - _DetailNormalMapScale: 1
    - _DetailUVSet: 0
    - _Diffuse: 0
    - _DiffuseSmoothness: 0
    - _DiffuseThresholdOffset: 0.25
    - _Dissolve: 0
    - _DissolveAmount: 0.5
    - _DissolveBorderSize: 0.25
    - _DissolveMapScale: 1
    - _DrawnClampMax: 1
    - _DrawnClampMin: 0
    - _DrawnMapScale: 1
    - _DstBlend: 0
    - _EnvironmentReflections: 2
    - _FresnelHighlights: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GoochRampIntensity: 0.5
    - _HatchingMapScale: 1
    - _Hue: 0
    - _IndirectFade: 1
    - _Initialized: 1
    - _InputTab: 1
    - _Iridescence: 0
    - _IridescenceSize: 1
    - _IridescenceSmoothness: 0.5
    - _IridescenceThresholdOffset: 0
    - _Light: 1
    - _LightBands: 4
    - _LightBandsScale: 0.5
    - _LightThreshold: 0.5
    - _LightTransmission: 0
    - _LightTransmissionDistortion: 0.25
    - _LightTransmissionIntensity: 1
    - _LightTransmissionSmoothness: 0.5
    - _LightTransmissionThresholdOffset: 0.25
    - _Metallic: 0
    - _Mode: 0
    - _NormalMapIntensity: 1
    - _OcclusionMapIntensity: 1
    - _OcclusionStrength: 1
    - _OptionsTab: 1
    - _Outline: 1
    - _OutlineClipOffset: 0
    - _OutlineData: 0
    - _OutlineFadeMax: 2
    - _OutlineFadeMin: 0.25
    - _OutlineNoise: 0
    - _OutlineSize: 35
    - _OutlineTab: 1
    - _Parallax: 0.02
    - _ReceiveShadows: 1
    - _RenderFace: 2
    - _RenderPriority: 0
    - _Rim: 0
    - _RimSize: 1
    - _RimSmoothness: 0.5
    - _RimThresholdOffset: 0.25
    - _Roughness: 0.5
    - _Saturation: 1
    - _SketchMapScale: 1
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _Specular: 1
    - _SpecularHighlights: 1
    - _SpecularIntensity: 1
    - _SpecularSmoothness: 0
    - _SpecularThresholdOffset: 0.25
    - _SrcBlend: 1
    - _Stencil: 1
    - _StencilComp: 8
    - _StencilFail: 0
    - _StencilPass: 0
    - _StencilReadMask: 255
    - _StencilRef: 0
    - _StencilWriteMask: 255
    - _StencilZFail: 0
    - _StylizeTab: 0
    - _Surface: 0
    - _ThresholdMapScale: 1
    - _UVSec: 0
    - _VertexAnimation: 0
    - _VertexAnimationIntensity: 0.05
    - _VertexAnimationStutter: 0
    - _Workflow: 0
    - _WrappedLighting: 1
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _AlbedoColor: {r: 0.253115, g: 0.37218082, b: 0.6792453, a: 1}
    - _Color: {r: 0.25311497, g: 0.37218076, b: 0.6792453, a: 1}
    - _DetailColor: {r: 1, g: 1, b: 1, a: 1}
    - _DissolveBorderColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GoochBrightColor: {r: 1, g: 1, b: 1, a: 1}
    - _GoochDarkColor: {r: 0, g: 0, b: 0, a: 1}
    - _IridescenceColor: {r: 1, g: 1, b: 1, a: 0.5}
    - _LightTransmissionColor: {r: 1, g: 0.65, b: 0, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimBrightColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimDarkColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecularColor: {r: 0.20312497, g: 0.20312497, b: 0.20312497, a: 1}
    - _VertexAnimationFrequency: {r: 2.5, g: 2.5, b: 2.5, a: 1}
  m_BuildTextureStacks: []

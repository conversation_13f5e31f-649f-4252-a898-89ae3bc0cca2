using Excel.rune;
using NUnit.Framework;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using static UnityEngine.EventSystems.EventTrigger;

public class Rune
{
    /// <summary>
    /// 符文获得的时间
    /// </summary>
    private float gainTime;

    public RuneTarget m_runeTarget = new RuneTarget();
    public RuneTarget runeTarget { get => m_runeTarget; }
    public int TargetType { get => m_runeTarget.TargetType; }

    private runeBaseEntity m_runeBaseEntity;
    public runeBaseEntity runeBaseEntity { get => m_runeBaseEntity; }

    private List<GameEntity> m_listTarget = new List<GameEntity>();

    public int ID { get => m_runeBaseEntity.id; }

    private List<Buff> m_listTargetBuff = new List<Buff>();

    private bool m_isQuit = false;
    public bool isQuit { get => m_isQuit; set => m_isQuit = value; }
    public void SetData(runeBaseEntity runeBaseEntity)
    {
        gainTime = Time.time;

        m_runeBaseEntity = runeBaseEntity;

        targetEntity target = ExcelData.Instance.RuneExcel.targetList.Find( t => t.id == runeBaseEntity.targetId);
        if (target == null)
        {
            Debug.LogError("Run Target not found for rune with ID: " + runeBaseEntity.id);
            return;
        }
        m_runeTarget.SetData(target);

        Start();
    }

    List<GameEntity> m_removeGameEntityList = new List<GameEntity>();
    List<GameEntity> m_newEntityList = new List<GameEntity>();//确定是新的被选中单位

    public void Update()
    {
        if (m_runeBaseEntity.update == 0) return;
        if (m_isQuit) return;

        List<GameEntity> lastList = m_listTarget;

        List<GameEntity> inRangeList = m_runeTarget.getTarget();
        if (inRangeList == null || inRangeList.Count == 0) return;

        m_newEntityList.Clear();
        m_removeGameEntityList.Clear();

        //不在新的里面，说明已经不在范围内了
        for (int i = 0; i < lastList.Count; i++)
        {
            //不在新的里面.要删除
            if (!inRangeList.Exists(n => n == lastList[i]))
            {
                m_removeGameEntityList.Add(lastList[i]);
            }
        }
        //不在老的里面，说明是新的
        for (int i = 0; i < inRangeList.Count; i++)
        {
            //不存在，是新的
            if (!lastList.Exists(n => n == inRangeList[i]))
                m_newEntityList.Add(inRangeList[i]);
        }
        //新的单位加入符文

        ApplyRune(m_newEntityList);

        //超出范围的单位移除符文
        for (int i = 0; i < m_removeGameEntityList.Count; i++)
        {
            RemoveRuneFormGameEntity(m_removeGameEntityList[i]);
        }
        m_listTarget.Clear();
        m_listTarget.AddRange(inRangeList);

    }

    public void ApplyRune(List<GameEntity> newEntityList)
    {
        if (newEntityList == null || newEntityList.Count == 0) return;
        if (m_runeBaseEntity.effectType == 1)
        {
            //effectParam1
            //      属性ID1:改变参数|属性ID2:改变参数
            //effectParam2
            //      计算公式
            //      1 = 基础属性 *（1 + 改变参数）
            //      2 = 基础属性 + 改变参数

            int function = int.Parse(m_runeBaseEntity.effectParam2);
            string[] array = m_runeBaseEntity.effectParam1.Split(new char[] { '|', ':' });
            for (int i = 0; i < array.Length; i += 2)
            {
                int attributeID = int.Parse(array[i]);
                float coefficient = float.Parse(array[i + 1]);

                if (function == 1)
                {
                    for (int n = 0; n < newEntityList.Count; n++)
                    {
                        float baseValue = newEntityList[n].GetAttributeOriginalValue((AttributeEnum)attributeID);
                        float addValue = baseValue * (1 + coefficient);
                        newEntityList[n].AddAttributeChangeByRune((AttributeEnum)attributeID, addValue - baseValue, this);
                    }
                }
                else if (function == 2)
                {
                    for (int n = 0; n < newEntityList.Count; n++)
                    {
                        float baseValue = newEntityList[n].GetAttributeOriginalValue((AttributeEnum)attributeID);
                        float addValue = coefficient + baseValue;
                        newEntityList[n].AddAttributeChangeByRune((AttributeEnum)attributeID, coefficient, this);
                    }
                }
            }
        }
        else if (m_runeBaseEntity.effectType == 2)
        {
            for (int i = 0; i < newEntityList.Count; i++)
            {
                newEntityList[i].AddSkill(int.Parse(m_runeBaseEntity.effectParam1));
            }
        }
        //随机在属性加成区间内取一个数值作为属性加成的数值
        else if (m_runeBaseEntity.effectType == 5)
        {
            int attributeID = int.Parse(m_runeBaseEntity.effectParam1);
            string[] array = m_runeBaseEntity.effectParam1.Split(",");
            float min = float.Parse(array[0]);
            float max = float.Parse(array[1]);
            float cofficient = Random.Range(min, max);

            for (int i = 0; i < newEntityList.Count; i++)
            {
                float baseValue = newEntityList[i].GetAttributeOriginalValue((AttributeEnum)attributeID);
                float value = baseValue * (1 + cofficient);

                newEntityList[i].AddAttributeChangeByRune((AttributeEnum)attributeID, value - baseValue, this);
            }
        }
        //生命值增加10%，生命回复效果+20%
        else if (m_runeBaseEntity.effectType == 6)
        {
            float healthCoefficient = float.Parse(m_runeBaseEntity.effectParam1);
            float healthRecoverCoefficient = float.Parse(m_runeBaseEntity.effectParam2);
            for (int i = 0; i < newEntityList.Count; i++)
            {
                float baseValue = newEntityList[i].GetAttributeOriginalValue(AttributeEnum.Health);
                float value = baseValue * (1 + healthCoefficient);
                newEntityList[i].AddAttributeChangeByRune(AttributeEnum.Health, value - baseValue, this);
                newEntityList[i].IsHealthRecover = true;
                newEntityList[i].HealthRecoverCoefficient = healthRecoverCoefficient;
            }
        }
        //进入战斗回合后一定时间内获得受伤减免
        else if (m_runeBaseEntity.effectType == 7)
        {
            Buff buff;
            int buffID = int.Parse(m_runeBaseEntity.effectParam1);

            for (int i = 0; i < newEntityList.Count; i++)
            {
                buff = new Buff(buffID);
                newEntityList[i].buffSystem.AddBuff(buff);
            }
        }
        //你造成的伤害 + 40 %，每过1回合减少3 %，从第1回合开始计算
        else if (m_runeBaseEntity.effectType == 8)
        {
            m_listTargetBuff.Clear();
            for (int i = 0; i < newEntityList.Count; i++)
            {
                Buff buff = new Buff(int.Parse(m_runeBaseEntity.effectParam1));
                m_listTargetBuff.Add(buff);
                newEntityList[i].buffSystem.AddBuff(buff);
            }
        }

    }

    public void RemoveRuneFormGameEntity(GameEntity gameEntity)
    {
        if (m_runeBaseEntity.effectType == 1)
        {
            string[] array = m_runeBaseEntity.effectParam1.Split(new char[] { '|', ':' });
            for (int n = 0; n < array.Length; n += 2)
            {
                int attributeID = int.Parse(array[n]);
                gameEntity.DelAttributeChange((AttributeEnum)attributeID, this);
            }
        }
    }

    public void Start()
    {
        //这个放到Update处理
        if (m_runeBaseEntity.update == 1) return;

        List<GameEntity> list = m_runeTarget.getTarget();
        if (list == null || list.Count == 0) return;

        m_listTarget.Clear();
        m_listTarget.AddRange(list);

        if (m_runeBaseEntity.effectType == 1)
        {
            //effectParam1
            //      属性ID1:改变参数|属性ID2:改变参数
            //effectParam2
            //      计算公式
            //      1 = 基础属性 *（1 + 改变参数）
            //      2 = 基础属性 + 改变参数
            if (m_listTarget == null || m_listTarget.Count == 0) return;
            int function = int.Parse(m_runeBaseEntity.effectParam2);
            string[] array = m_runeBaseEntity.effectParam1.Split(new char[] { '|', ':' });
            for (int i = 0; i < array.Length; i += 2)
            {
                int attributeID = int.Parse(array[i]);
                float coefficient = float.Parse(array[i + 1]);

                if (function == 1)
                {
                    for (int n = 0; n < m_listTarget.Count; n++)
                    {
                        float baseValue = m_listTarget[n].GetAttributeOriginalValue((AttributeEnum)attributeID);
                        float addValue = baseValue * (1 + coefficient);
                        m_listTarget[n].AddAttributeChangeByRune((AttributeEnum)attributeID, addValue - baseValue, this);
                    }
                }
                else if (function == 2)
                {
                    for (int n = 0; n < m_listTarget.Count; n++)
                    {
                        float baseValue = m_listTarget[n].GetAttributeOriginalValue((AttributeEnum)attributeID);
                        float addValue = coefficient + baseValue;
                        m_listTarget[n].AddAttributeChangeByRune((AttributeEnum)attributeID, coefficient, this);
                    }
                }
            }
        }
        else if (m_runeBaseEntity.effectType == 2)
        {
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                m_listTarget[i].AddSkill(int.Parse(m_runeBaseEntity.effectParam1));
            }
        }
        //随机在属性加成区间内取一个数值作为属性加成的数值
        else if (m_runeBaseEntity.effectType == 5)
        {
            int attributeID = int.Parse(m_runeBaseEntity.effectParam1);
            string[] array = m_runeBaseEntity.effectParam1.Split(",");
            float min = float.Parse(array[0]);
            float max = float.Parse(array[1]);
            float cofficient = Random.Range(min, max);

            for (int i = 0; i < m_listTarget.Count; i++)
            {
                float baseValue = m_listTarget[i].GetAttributeOriginalValue((AttributeEnum)attributeID);
                float value = baseValue * (1 + cofficient);

                m_listTarget[i].AddAttributeChangeByRune((AttributeEnum)attributeID, value - baseValue, this);
            }
        }
        //生命值增加10%，生命回复效果+20%
        else if (m_runeBaseEntity.effectType == 6)
        {
            float healthCoefficient = float.Parse(m_runeBaseEntity.effectParam1);
            float healthRecoverCoefficient = float.Parse(m_runeBaseEntity.effectParam2);
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                float baseValue = m_listTarget[i].GetAttributeOriginalValue(AttributeEnum.Health);
                float value = baseValue * (1 + healthCoefficient);
                m_listTarget[i].AddAttributeChangeByRune(AttributeEnum.Health, value - baseValue, this);
                m_listTarget[i].IsHealthRecover = true;
                m_listTarget[i].HealthRecoverCoefficient = healthRecoverCoefficient;
            }
        }
        //进入战斗回合后一定时间内获得受伤减免
        else if (m_runeBaseEntity.effectType == 7)
        {
            Buff buff;
            int buffID = int.Parse(m_runeBaseEntity.effectParam1);

            for (int i = 0; i < m_listTarget.Count; i++)
            {
                buff = new Buff(buffID);
                m_listTarget[i].buffSystem.AddBuff(buff);
            }
        }
        //你造成的伤害 + 40 %，每过1回合减少3 %，从第1回合开始计算
        else  if (m_runeBaseEntity.effectType == 8)
        {
            m_listTargetBuff.Clear();
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                Buff buff = new Buff(int.Parse(m_runeBaseEntity.effectParam1));
                m_listTargetBuff.Add(buff);
                m_listTarget[i].buffSystem.AddBuff(buff);
            }
        }

    }

    public void Quit()
    {
        m_isQuit = true;
        if (m_runeBaseEntity.effectType == 1)
        {
            //effectParam1
            //      属性ID1:改变参数|属性ID2:改变参数
            //effectParam2
            //      计算公式
            //      1 = 基础属性 *（1 + 改变参数）
            //      2 = 基础属性 + 改变参数

            string[] array = m_runeBaseEntity.effectParam1.Split(new char[] { '|', ':' });
            for (int n = 0; n < m_listTarget.Count; n++)
            {
                for (int i = 0; i < array.Length; i += 2)
                {
                    int attributeID = int.Parse(array[i]);
                    m_listTarget[i].DelAttributeChange((AttributeEnum)attributeID, this);
                }
            }
        }
        else if (m_runeBaseEntity.effectType == 2)
        {
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                m_listTarget[i].RemoveSkill(int.Parse(m_runeBaseEntity.effectParam1));
            }
        }
        else if( m_runeBaseEntity.effectType == 5 )
        {
            int attributeID = int.Parse(m_runeBaseEntity.effectParam1);
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                m_listTarget[i].DelAttributeChange((AttributeEnum)attributeID,this);
            }
        }
        else if (m_runeBaseEntity.effectType == 6)
        {
            float healthCoefficient = float.Parse(m_runeBaseEntity.effectParam1);
            float healthRecoverCoefficient = float.Parse(m_runeBaseEntity.effectParam2);
            for (int i = 0; i < m_listTarget.Count; i++)
            {
                float baseValue = m_listTarget[i].GetAttributeOriginalValue(AttributeEnum.Health);
                float value = baseValue * (1 + healthCoefficient);
                m_listTarget[i].DelAttributeChange(AttributeEnum.Health, this);
                m_listTarget[i].IsHealthRecover = false;
            }
        }
    }

    public void NextTurn()
    {
        if (m_runeBaseEntity.effectType == 8)
        {
            for (int i = 0; i < m_listTargetBuff.Count; i++)
            {
                m_listTargetBuff[i].BuffData.buffParams2  -= 0.03f;
            }
        }
        //灵能塔攻击+80，回合结束有20%的概率摧毁该符文
        if (m_runeBaseEntity.id == 1002)
        {
            int rand = Random.Range(0, 100);
            if (rand < 20)
            {
                Quit();
            }
        }
    }
}

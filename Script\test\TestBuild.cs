using Excel.map;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
 using UnityEngine.UI;

 public class TestBuild : MonoBehaviour
 {
     public GameObject img;
    private void Start()
    {
    }


    private void OnGUI()
    {
        if (G<PERSON>.Button(new Rect(10, 10, 100, 50), "Change"))
        {
            List<beastAreaEntity> _list = Resources.Load<mapExcel>("Excel/mapExcel").beastAreaList;
            beastAreaEntity entity = _list.Find(x => x.id == 11);
            entity.challengeTime = 9999;
        }
    }


}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &113157308049454547
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6269549572405393509}
  m_Layer: 0
  m_Name: RigRFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6269549572405393509
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 113157308049454547}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00091990764, y: -0.47066152, z: 0.0029012582, w: 0.8823086}
  m_LocalPosition: {x: -0.03561676, y: 0.00009338379, z: -0.052462384}
  m_LocalScale: {x: 0.9999995, y: 1.0000001, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8950955188167691875}
  m_Father: {fileID: 8691439336607041269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &538950270735848770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1190061269854303211}
  - component: {fileID: 2330270026665372915}
  - component: {fileID: 4413123524078829265}
  m_Layer: 0
  m_Name: Head 02 Fair
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1190061269854303211
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 538950270735848770}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5113583013485168559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2330270026665372915
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 538950270735848770}
  m_Mesh: {fileID: 4300000, guid: db280b8e73afaef4abdcbc7e2ef8cbac, type: 3}
--- !u!23 &4413123524078829265
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 538950270735848770}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7d64267e5eb8ca74d8c9c25ffd8dcd35, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &755269675258051766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 295569113655174502}
  m_Layer: 8
  m_Name: anchorTop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &295569113655174502
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755269675258051766}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.905, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &927114749695379460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5242342320514803323}
  m_Layer: 0
  m_Name: RigLFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5242342320514803323
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 927114749695379460}
  serializedVersion: 2
  m_LocalRotation: {x: 0.017786296, y: -0.017348098, z: 0.059156023, w: 0.9979395}
  m_LocalPosition: {x: -0.038318176, y: 0.000000038146972, z: -0.000000114440915}
  m_LocalScale: {x: 1, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6755285124888694971}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1103699191906976962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3111113486291444172}
  m_Layer: 0
  m_Name: RigRFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3111113486291444172
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1103699191906976962}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02642484, y: -0.020392401, z: -0.40790778, w: 0.9124128}
  m_LocalPosition: {x: -0.20291305, y: 5.9604643e-10, z: 0}
  m_LocalScale: {x: 1.0000015, y: 0.99999845, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8550666580729463979}
  m_Father: {fileID: 6194655140269133038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1115324667111586485
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2097923503849461849}
  m_Layer: 0
  m_Name: RigRFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2097923503849461849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115324667111586485}
  serializedVersion: 2
  m_LocalRotation: {x: -0.012239516, y: 0.004879776, z: 0.03411459, w: 0.99933106}
  m_LocalPosition: {x: -0.053491116, y: 0, z: 0.000000038146972}
  m_LocalScale: {x: 0.99999946, y: 1.0000002, z: 1.0000008}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8950955188167691875}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1121080506109900639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4618921469035730191}
  m_Layer: 0
  m_Name: RigLCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4618921469035730191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1121080506109900639}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0138151795, y: 0.8200881, z: 0.01931747, w: 0.5717443}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: 0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7238376247073351156}
  m_Father: {fileID: 2632064763224742885}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1170601110203506640
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8676392338970737899}
  - component: {fileID: 495929931514732470}
  - component: {fileID: 1466071329851645023}
  - component: {fileID: 6265658069105075542}
  m_Layer: 13
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8676392338970737899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1170601110203506640}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &495929931514732470
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1170601110203506640}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1466071329851645023
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1170601110203506640}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a1f798269267cd74e8ba30ecb7d68be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &6265658069105075542
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1170601110203506640}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1265612474093323394
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1410494619004922576}
  m_Layer: 8
  m_Name: anchorBullet
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1410494619004922576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265612474093323394}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.84, z: 0.91}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1554351713507777956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8691439336607041269}
  m_Layer: 0
  m_Name: RigRPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8691439336607041269
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1554351713507777956}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7164266, y: -0.07365103, z: 0.04593978, w: 0.6922413}
  m_LocalPosition: {x: -0.2086134, y: -0.000000038146972, z: -0.000000076293944}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6269549572405393509}
  - {fileID: 7068594450990861369}
  - {fileID: 244423694849428021}
  m_Father: {fileID: 2045172227656849720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1756800004759142085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3430870904285218414}
  m_Layer: 0
  m_Name: RigRCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3430870904285218414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1756800004759142085}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013815688, y: -0.8200879, z: 0.019317966, w: 0.5717445}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: -0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6874668544133824682}
  m_Father: {fileID: 2632064763224742885}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1825267778334479134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5497476009276564993}
  m_Layer: 8
  m_Name: anchorBottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5497476009276564993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1825267778334479134}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1896048805753182820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6070050083226152298}
  m_Layer: 0
  m_Name: RigRThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6070050083226152298
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1896048805753182820}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037409604, y: 0.9985059, z: 0.038666774, w: -0.00956456}
  m_LocalPosition: {x: 0.06101242, y: 0.000443881, z: -0.115600996}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6194655140269133038}
  m_Father: {fileID: 5015917167947896466}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2190688952824190952
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1583894736544094608}
  m_Layer: 0
  m_Name: RigLForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1583894736544094608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2190688952824190952}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0060543455, y: 0.032705806, z: -0.1854741, w: 0.98208606}
  m_LocalPosition: {x: -0.27621725, y: 0.0000000023841857, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6756546818788904825}
  m_Father: {fileID: 7238376247073351156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2417795498133710765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5367236597256907990}
  m_Layer: 0
  m_Name: RigLCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5367236597256907990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2417795498133710765}
  serializedVersion: 2
  m_LocalRotation: {x: -0.060566172, y: 0.009119939, z: 0.07917449, w: 0.9949774}
  m_LocalPosition: {x: -0.25720724, y: 0.000007133484, z: -0.00000037193297}
  m_LocalScale: {x: 1.0000004, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2169329435467796841}
  m_Father: {fileID: 7148488571813472073}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2815130610844852117
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5411753583386173619}
  m_Layer: 0
  m_Name: RigLFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5411753583386173619
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2815130610844852117}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0089415265, y: -0.04077185, z: -0.035507936, w: 0.99849737}
  m_LocalPosition: {x: -0.13877107, y: 0.0006755066, z: -0.007317638}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6755285124888694971}
  m_Father: {fileID: 6756546818788904825}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2986684155399281974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6406799549480148476}
  m_Layer: 0
  m_Name: RigSpine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6406799549480148476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2986684155399281974}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000003637839, y: 0.0000000053254277, z: 0.00037867192, w: 0.99999994}
  m_LocalPosition: {x: -0.12812133, y: 0, z: 0}
  m_LocalScale: {x: 1.0000004, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6364845418998597028}
  m_Father: {fileID: 6235380907936037334}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3016126405102220221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7607134804476061133}
  m_Layer: 8
  m_Name: anchorCenter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7607134804476061133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3016126405102220221}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.795, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3513295051863366214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6755285124888694971}
  m_Layer: 0
  m_Name: RigLFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6755285124888694971
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3513295051863366214}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0017855264, y: 0.038816083, z: 0.15180448, w: 0.9876465}
  m_LocalPosition: {x: -0.04045116, y: 0, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5242342320514803323}
  m_Father: {fileID: 5411753583386173619}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3695232704959509312
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7414772606610352582}
  m_Layer: 0
  m_Name: RigLFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7414772606610352582
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3695232704959509312}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0012682214, y: -0.017850274, z: 0.079025865, w: 0.99671197}
  m_LocalPosition: {x: -0.051383436, y: 0, z: 0}
  m_LocalScale: {x: 1.0000005, y: 0.9999998, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3174508437655552580}
  m_Father: {fileID: 9049920574586945692}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3751605742668818938
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4099550123486994822}
  - component: {fileID: 5112388354551215042}
  - component: {fileID: 3323312287941740415}
  m_Layer: 0
  m_Name: Quiver Dawn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4099550123486994822
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3751605742668818938}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4434095048016561554}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5112388354551215042
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3751605742668818938}
  m_Mesh: {fileID: 4300000, guid: a5805c30764a9ea468f1885c2f3ed240, type: 3}
--- !u!23 &3323312287941740415
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3751605742668818938}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3756641468381376832
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5056185573447276063}
  - component: {fileID: 6480620737853433333}
  - component: {fileID: 3752397565692685895}
  m_Layer: 0
  m_Name: Face Male 01 Blonde
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5056185573447276063
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3756641468381376832}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5113583013485168559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6480620737853433333
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3756641468381376832}
  m_Mesh: {fileID: 4300000, guid: a91e2c8bbd0e53447adeb7d239346501, type: 3}
--- !u!23 &3752397565692685895
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3756641468381376832}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 00f1926fd45a9a642a30331ba3a02f5b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3907572257731209568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4518825237357660771}
  m_Layer: 0
  m_Name: RigNeck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4518825237357660771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3907572257731209568}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000007756446, y: 0.000000089830266, z: -0.09581775, w: 0.99539894}
  m_LocalPosition: {x: 0.005050278, y: -0.031543914, z: -0.0000000021330198}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8504563635844144672}
  m_Father: {fileID: 2632064763224742885}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4346154124010367043
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4434095048016561554}
  m_Layer: 0
  m_Name: + Back
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4434095048016561554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4346154124010367043}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999985, y: 0.50000006, z: 0.5000001, w: 0.5}
  m_LocalPosition: {x: 0.13352661, y: -0.18530917, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4099550123486994822}
  m_Father: {fileID: 2632064763224742885}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4640483365168697884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7148488571813472073}
  m_Layer: 0
  m_Name: RigLThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7148488571813472073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4640483365168697884}
  serializedVersion: 2
  m_LocalRotation: {x: -0.04162947, y: 0.9983547, z: -0.038122125, w: 0.010083623}
  m_LocalPosition: {x: 0.061012648, y: -0.0036894195, z: 0.11560108}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5367236597256907990}
  m_Father: {fileID: 5015917167947896466}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4649761333020360445
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6482871913675957078}
  m_Layer: 0
  m_Name: RigRPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6482871913675957078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4649761333020360445}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: 0.117125824, y: 0.004488783, z: 0.09778633}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4930639688385952682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1317220672411294277}
  m_Layer: 0
  m_Name: RigLPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1317220672411294277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4930639688385952682}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0.11712611, y: 0.0044879722, z: 0.100568734}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5525792278895923036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2045172227656849720}
  m_Layer: 0
  m_Name: RigRForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2045172227656849720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5525792278895923036}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0060543595, y: -0.032705795, z: -0.18547401, w: 0.9820861}
  m_LocalPosition: {x: -0.27621725, y: -0.0000000023841857, z: 0}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8691439336607041269}
  m_Father: {fileID: 6874668544133824682}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5616197381162885571
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8950955188167691875}
  m_Layer: 0
  m_Name: RigRFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8950955188167691875
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5616197381162885571}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0012692144, y: 0.017850406, z: 0.07902567, w: 0.99671197}
  m_LocalPosition: {x: -0.05138341, y: 0, z: 0}
  m_LocalScale: {x: 0.99999946, y: 1.0000002, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2097923503849461849}
  m_Father: {fileID: 6269549572405393509}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5770618636492179472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5015917167947896466}
  m_Layer: 0
  m_Name: RigPelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5015917167947896466
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5770618636492179472}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: -0.00000013351438, y: 0.6149364, z: 0.0003728537}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7148488571813472073}
  - {fileID: 6070050083226152298}
  - {fileID: 6235380907936037334}
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5828190645444463834
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1632785092581759681}
  m_Layer: 0
  m_Name: RigLToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1632785092581759681
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5828190645444463834}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014892296, y: -0.005698782, z: -0.3574828, w: 0.9337836}
  m_LocalPosition: {x: -0.13313861, y: -0.000051302908, z: -0.000004529953}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1477990536905639251}
  m_Father: {fileID: 2169329435467796841}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5978988110941338267
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1477990536905639251}
  m_Layer: 0
  m_Name: RigLToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1477990536905639251
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5978988110941338267}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0005379391, y: -0.0016754807, z: 0.9954929, w: 0.09481989}
  m_LocalPosition: {x: -0.057331752, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1632785092581759681}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6089478948357209016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6756546818788904825}
  m_Layer: 0
  m_Name: RigLPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6756546818788904825
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6089478948357209016}
  serializedVersion: 2
  m_LocalRotation: {x: -0.71642655, y: 0.07365139, z: 0.04593959, w: 0.6922413}
  m_LocalPosition: {x: -0.20861335, y: 0.000000076293944, z: 0}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9049920574586945692}
  - {fileID: 5411753583386173619}
  - {fileID: 632111375652626799}
  m_Father: {fileID: 1583894736544094608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6162583027548454694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2632064763224742885}
  m_Layer: 0
  m_Name: RigRibcage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2632064763224742885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6162583027548454694}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000035377568, y: -0.00000008137332, z: 0.07053914, w: 0.997509}
  m_LocalPosition: {x: -0.17131469, y: 0, z: -9.094947e-15}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4618921469035730191}
  - {fileID: 4518825237357660771}
  - {fileID: 3430870904285218414}
  - {fileID: 4434095048016561554}
  m_Father: {fileID: 6364845418998597028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6216465144487096008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7768393083851705230}
  - component: {fileID: 2503007378285046531}
  m_Layer: 0
  m_Name: Male Archer 01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7768393083851705230
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6216465144487096008}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2503007378285046531
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6216465144487096008}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d027e556141828245a28e5cffcfd23d3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: a60c8560f2c3ca146ab18375000a1150, type: 3}
  m_Bones:
  - {fileID: 8504563635844144672}
  - {fileID: 2632064763224742885}
  - {fileID: 6874668544133824682}
  - {fileID: 3430870904285218414}
  - {fileID: 6406799549480148476}
  - {fileID: 8691439336607041269}
  - {fileID: 2045172227656849720}
  - {fileID: 6269549572405393509}
  - {fileID: 6364845418998597028}
  - {fileID: 7238376247073351156}
  - {fileID: 4618921469035730191}
  - {fileID: 1583894736544094608}
  - {fileID: 6235380907936037334}
  - {fileID: 5015917167947896466}
  - {fileID: 6070050083226152298}
  - {fileID: 7148488571813472073}
  - {fileID: 9049920574586945692}
  - {fileID: 6756546818788904825}
  - {fileID: 7414772606610352582}
  - {fileID: 5411753583386173619}
  - {fileID: 6755285124888694971}
  - {fileID: 5242342320514803323}
  - {fileID: 3174508437655552580}
  - {fileID: 6194655140269133038}
  - {fileID: 3111113486291444172}
  - {fileID: 8550666580729463979}
  - {fileID: 7692707579735750502}
  - {fileID: 5367236597256907990}
  - {fileID: 2169329435467796841}
  - {fileID: 1632785092581759681}
  - {fileID: 1477990536905639251}
  - {fileID: 8950955188167691875}
  - {fileID: 7068594450990861369}
  - {fileID: 5695935183353041334}
  - {fileID: 5173043996505468575}
  - {fileID: 2097923503849461849}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 5015917167947896466}
  m_AABB:
    m_Center: {x: 0.008555263, y: 0.029676192, z: -0.00000023841858}
    m_Extent: {x: 0.66091156, y: 0.2357266, z: 0.5714463}
  m_DirtyAABB: 0
--- !u!1 &6402216891725044718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2169329435467796841}
  m_Layer: 0
  m_Name: RigLFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2169329435467796841
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6402216891725044718}
  serializedVersion: 2
  m_LocalRotation: {x: 0.025890388, y: 0.020653779, z: -0.43305016, w: 0.9007612}
  m_LocalPosition: {x: -0.20310444, y: 5.9604643e-10, z: -0.000000019073486}
  m_LocalScale: {x: 0.999998, y: 1.0000018, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1632785092581759681}
  m_Father: {fileID: 5367236597256907990}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6703780345907746567
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5695935183353041334}
  m_Layer: 0
  m_Name: RigRFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5695935183353041334
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6703780345907746567}
  serializedVersion: 2
  m_LocalRotation: {x: -0.017789682, y: 0.017345075, z: 0.059156768, w: 0.99793947}
  m_LocalPosition: {x: -0.03831825, y: -0.000000038146972, z: -0.000000114440915}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5173043996505468575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6870415488117974011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 632111375652626799}
  m_Layer: 0
  m_Name: + L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &632111375652626799
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6870415488117974011}
  serializedVersion: 2
  m_LocalRotation: {x: 0.68783003, y: 0.72568053, z: -0.01395577, w: -0.009100388}
  m_LocalPosition: {x: -0.102999955, y: -0.0729997, z: 0.0040003215}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7463266777263166803}
  m_Father: {fileID: 6756546818788904825}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &6965581296053520122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7068594450990861369}
  m_Layer: 0
  m_Name: RigRFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7068594450990861369
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6965581296053520122}
  serializedVersion: 2
  m_LocalRotation: {x: -0.008941437, y: 0.040772367, z: -0.035507932, w: 0.9984973}
  m_LocalPosition: {x: -0.13877101, y: 0.00067565916, z: 0.0073177717}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5173043996505468575}
  m_Father: {fileID: 8691439336607041269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7098757886244343524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6364845418998597028}
  m_Layer: 0
  m_Name: RigSpine3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6364845418998597028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7098757886244343524}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000004032032, y: -0.000000002195892, z: -0.092958696, w: 0.99566996}
  m_LocalPosition: {x: -0.14642449, y: 0.0000000023841857, z: 0}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2632064763224742885}
  m_Father: {fileID: 6406799549480148476}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7175583380569109808
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6874668544133824682}
  m_Layer: 0
  m_Name: RigRUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6874668544133824682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7175583380569109808}
  serializedVersion: 2
  m_LocalRotation: {x: -0.021966802, y: -0.30736104, z: 0.0071287793, w: 0.9513127}
  m_LocalPosition: {x: -0.14668107, y: 0.00000004053116, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2045172227656849720}
  m_Father: {fileID: 3430870904285218414}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7475015479493604382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7238376247073351156}
  m_Layer: 0
  m_Name: RigLUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7238376247073351156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7475015479493604382}
  serializedVersion: 2
  m_LocalRotation: {x: 0.021966634, y: 0.30736107, z: 0.00712906, w: 0.9513127}
  m_LocalPosition: {x: -0.14668113, y: -0.00000004529953, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1583894736544094608}
  m_Father: {fileID: 4618921469035730191}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7533486338359946765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6913317755540481424}
  m_Layer: 0
  m_Name: Rig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6913317755540481424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7533486338359946765}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6610757601180919292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7646160347441764280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 244423694849428021}
  m_Layer: 0
  m_Name: + R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &244423694849428021
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7646160347441764280}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6878294, y: 0.7256813, z: -0.013955621, w: -0.009101084}
  m_LocalPosition: {x: -0.12100025, y: -0.049000643, z: 0.023000555}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8691439336607041269}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &7863169961089962226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6235380907936037334}
  m_Layer: 0
  m_Name: RigSpine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6235380907936037334
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7863169961089962226}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000001410755, y: 7.022983e-10, z: 0.022114862, w: 0.99975544}
  m_LocalPosition: {x: -0.10970802, y: -0.0049213637, z: 7.3929185e-10}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6406799549480148476}
  m_Father: {fileID: 5015917167947896466}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8030756948122902027
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7463266777263166803}
  - component: {fileID: 8819088428850267826}
  - component: {fileID: 2688635544244563583}
  m_Layer: 0
  m_Name: Bow Apprentice
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7463266777263166803
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8030756948122902027}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 632111375652626799}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8819088428850267826
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8030756948122902027}
  m_Mesh: {fileID: 4300000, guid: 91860ad15450ed64bad8034d8379b18c, type: 3}
--- !u!23 &2688635544244563583
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8030756948122902027}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8067957504736381227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7692707579735750502}
  m_Layer: 0
  m_Name: RigRToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7692707579735750502
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8067957504736381227}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0006140368, y: 0.0014643085, z: 0.98400944, w: 0.17810918}
  m_LocalPosition: {x: -0.057583522, y: -0.0000000023841857, z: 0.000000019073486}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8550666580729463979}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8431682911571375522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5113583013485168559}
  m_Layer: 0
  m_Name: + Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5113583013485168559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8431682911571375522}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999958, y: 0.5, z: 0.50000036, w: 0.5000002}
  m_LocalPosition: {x: 1.2133555, y: 0.014542067, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1190061269854303211}
  - {fileID: 5056185573447276063}
  - {fileID: 2280177047636751899}
  m_Father: {fileID: 8504563635844144672}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8448317038555626526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5173043996505468575}
  m_Layer: 0
  m_Name: RigRFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5173043996505468575
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8448317038555626526}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0017842915, y: -0.038817655, z: 0.15180488, w: 0.9876464}
  m_LocalPosition: {x: -0.040451184, y: 0, z: 0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5695935183353041334}
  m_Father: {fileID: 7068594450990861369}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8609655556975797754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8504563635844144672}
  m_Layer: 0
  m_Name: RigHead
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8504563635844144672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609655556975797754}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007756447, y: -0.00000008983025, z: 0.09581774, w: 0.99539894}
  m_LocalPosition: {x: -0.050812453, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6304612419207720838}
  - {fileID: 5113583013485168559}
  m_Father: {fileID: 4518825237357660771}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8616557601770102029
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9049920574586945692}
  m_Layer: 0
  m_Name: RigLFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9049920574586945692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8616557601770102029}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0009197159, y: 0.47066122, z: 0.0029011806, w: 0.8823088}
  m_LocalPosition: {x: -0.035616912, y: 0.00009330749, z: 0.052462384}
  m_LocalScale: {x: 1.0000004, y: 0.9999999, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7414772606610352582}
  m_Father: {fileID: 6756546818788904825}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8685142677070757561
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8550666580729463979}
  m_Layer: 0
  m_Name: RigRToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8550666580729463979
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8685142677070757561}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014477435, y: 0.0058796154, z: -0.3766731, w: 0.92621446}
  m_LocalPosition: {x: -0.12765375, y: -0.000003786087, z: 0.0000016212463}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7692707579735750502}
  m_Father: {fileID: 3111113486291444172}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8692429609925985419
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3174508437655552580}
  m_Layer: 0
  m_Name: RigLFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3174508437655552580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8692429609925985419}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012240344, y: -0.004878867, z: 0.034112927, w: 0.9993311}
  m_LocalPosition: {x: -0.05349117, y: -0.000000076293944, z: 0.000000038146972}
  m_LocalScale: {x: 1.0000006, y: 0.99999994, z: 0.9999996}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7414772606610352582}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8772578778099804994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6194655140269133038}
  m_Layer: 0
  m_Name: RigRCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6194655140269133038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8772578778099804994}
  serializedVersion: 2
  m_LocalRotation: {x: 0.061178077, y: -0.008315105, z: 0.06785075, w: 0.9957833}
  m_LocalPosition: {x: -0.2570285, y: 0.000006752014, z: 0.0000004196167}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3111113486291444172}
  m_Father: {fileID: 6070050083226152298}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8933659267920548780
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6610757601180919292}
  - component: {fileID: 8975842255960833652}
  - component: {fileID: 6980834515091883044}
  - component: {fileID: 1979771805226749234}
  - component: {fileID: 6167850700294898363}
  - component: {fileID: 6190728943483687678}
  - component: {fileID: 632367135987804214}
  m_Layer: 0
  m_Name: Male Archer 01moren
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6610757601180919292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: 0}
  m_LocalPosition: {x: 1.5245788, y: -0.00000047683716, z: 0.6525438}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7768393083851705230}
  - {fileID: 6913317755540481424}
  - {fileID: 1317220672411294277}
  - {fileID: 5015917167947896466}
  - {fileID: 6482871913675957078}
  - {fileID: 5497476009276564993}
  - {fileID: 295569113655174502}
  - {fileID: 7607134804476061133}
  - {fileID: 1410494619004922576}
  - {fileID: 8676392338970737899}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!95 &8975842255960833652
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: f6605568d3cd20e4db1a0b19cfe83787, type: 3}
  m_Controller: {fileID: 9100000, guid: 376c521d7c8c8d947ad7945dffc139e4, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &6980834515091883044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0fe0c0abe69c93a4f81ad3fc643500a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_camp: 1
  m_targetValue: 100
  m_id: 0
  m_health: 0
  m_currentHealth: 0
  m_defense: 0
  m_attackDamage: 0
  m_attackPecent: 0
  m_healthPercent: 0
  m_attackSpeed: 0
  m_moveSpeed: 0
  m_coolingReduced: 0
  m_damageBonus: 0
  m_damageReduced: 0
  m_attackRange: 1
  m_raceEnum: 0
  m_attackSpeedChange: 1
  m_disableAction: 0
  m_Anchors:
  - {fileID: 7607134804476061133}
  - {fileID: 5497476009276564993}
  - {fileID: 295569113655174502}
  - {fileID: 1410494619004922576}
  m_radius: 0.5
  m_normalAttackCount: 0
  m_StateMachine:
    m_destination: {x: 0, y: 0, z: 0}
    m_currentState: {fileID: 0}
    startLoopTime: 0
    startPosition: {x: 0, y: 0, z: 0}
    direction: {x: 0, y: 0, z: 0}
    distance: 0
    speed: 0
    beatBackFinish: 0
    isBeatBack: 0
    patrolPoints: []
    m_lastTime: 0
  m_idleState: {fileID: 11400000, guid: 3ceb064498b04784fb44b5598bac2ed4, type: 2}
  m_skillCastState: {fileID: 11400000, guid: 0417d335b5697f448bff0e41bde819fc, type: 2}
  m_runToSkillTargetState: {fileID: 11400000, guid: bf165ea46cb450a41b2ee8bd298edd87, type: 2}
  m_rushState: {fileID: 11400000, guid: e50da9d6eafea494287f398dc0c224a2, type: 2}
  m_guardState: {fileID: 0}
  m_deathState: {fileID: 11400000, guid: 0cc21a66f1586c64f9d6a732c22b2fc5, type: 2}
  m_beatBackState: {fileID: 0}
  m_polygonState: {fileID: 0}
  m_runToDestinationState: {fileID: 0}
  m_targetActor: {fileID: 0}
  horizontal: 0
  vertical: 0
  lastNormalizedTime: 0
--- !u!195 &1979771805226749234
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  m_Enabled: 0
  m_AgentTypeID: -334000983
  m_Radius: 0.6
  m_Speed: 3.5
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 0.5
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 1
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 0
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!136 &6167850700294898363
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 448
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 1, z: 0}
--- !u!54 &6190728943483687678
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &632367135987804214
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8933659267920548780}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3b1a70ab69e5774eb62f9e4d3bac895, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_speed: 5
  m_stoppingDistance: 0.1
  m_rotationSpeed: 360
  m_autoBraking: 1
  m_pathStatus: 0
--- !u!1 &9097495541545213113
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6304612419207720838}
  m_Layer: 0
  m_Name: RigHeadBone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6304612419207720838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9097495541545213113}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000006664002, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.49453285, y: 0.09610081, z: 0.00000008530383}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8504563635844144672}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &8455835678404317359
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5113583013485168559}
    m_Modifications:
    - target: {fileID: 7709592250652146324, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_Name
      value: Hair 03 Black
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
--- !u!4 &2280177047636751899 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7709592250652436148, guid: 0b0247b82f9c521439015e6db4902c78, type: 3}
  m_PrefabInstance: {fileID: 8455835678404317359}
  m_PrefabAsset: {fileID: 0}

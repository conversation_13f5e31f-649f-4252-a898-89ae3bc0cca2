using UnityEngine;
using UnityEngine.EventSystems;
using TMPro;
using Excel.shop;
using System.Collections.Generic;
using Excel.rune;
public class GiftIdToWeight : MonoBehaviour
{
    public int id;
    public int weight;
}
public class ShowGift : MonoBehaviour
{
    [SerializeField] List<GoodsToPrefab> prefabList;
    [SerializeField] TMP_Text titletText;
    [SerializeField] TMP_Text tipsText;
    [SerializeField] GameObject ListWrapper;
    [SerializeField] BtnEvent CloseButtonm;
    public treasurePoolEntity poolEntity;
    private List<BaseGoodsItem> childList = new List<BaseGoodsItem>();
    private int isSelected = 0;
    private void Awake()
    {
        if(CloseButtonm!= null) {
            CloseButtonm.OnClick.AddListener((PointerEventData eventData)=> {
                closeView();
            });
        }
    }
    private void renderUI() {
        titletText.text = Language.GetText(poolEntity.name)??"";
        string unityText = Language.GetText(1039)??""; // 单位文本
        string typeId = poolEntity.refType.ToDescription<GoodsType>(); // 获取类型
        string typeToString = Language.GetText(int.Parse(typeId))??""; // 获取类型文本
        string textTpl = Language.GetText(1035)??""; // 模板文本
        // 可选数量
        int optionalQuantity = poolEntity.selectNum - isSelected;
        string formatText = string.Format(textTpl, optionalQuantity, unityText, typeToString); // 拼接字符串
        tipsText.text = formatText;
    }
    /// <summary>
    /// 根据 treasureGroup 获取对应的权重数据
    /// </summary>
    /// <param name="treasureGroup"></param>
    /// <returns></returns>
    private List<GiftIdToWeight> getGiftWeightData(string treasureGroup) {
        List<GiftIdToWeight> WeightData = new List<GiftIdToWeight>();
        string [] itemData = treasureGroup.Split("|"); // 截取到<id：权重>的字符串
        if(itemData.Length == 0) return WeightData;
        for (int i = 0; i < itemData.Length; i++) {
            string [] itemIdAndWeight = itemData[i].Split(":");
            GiftIdToWeight weightItem = new GiftIdToWeight();
            weightItem.id = int.Parse(itemIdAndWeight[0]);
            weightItem.weight = int.Parse(itemIdAndWeight[1]);
            WeightData.Add(weightItem);
        }
        return WeightData;
    }
    /// <summary>
    /// 获取权重列表
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private List<int> getListWeight(List<GiftIdToWeight> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    /// <summary>
    /// 过滤掉已购买的符文
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private List<GiftIdToWeight> filterRune(List<GiftIdToWeight> val) {
        List<BaseGoodsData> isPayRune = RuneInventory.Instance.buyList;
        if(val.Count <= 0 || isPayRune.Count <= 0) {
            return val;
        }
        List<GiftIdToWeight> result = new List<GiftIdToWeight>();
        for (int i = 0; i < val.Count; i++)
        {
            // 判断是否已购买
            bool isPay = isPayRune.Exists(x=> x.id == val[i].id);
            if(!isPay) {
                result.Add(val[i]);
            }
            
        }
        return result;
    }
    public void LoadGifts(treasurePoolEntity valdata) {
        childList.Clear();
        ClearListWrapper(); // 先清除旧内容
        if(valdata == null) return;
        poolEntity = valdata;
        isSelected = 0;
        renderUI();
        GameObject _prefabe = prefabList?.Find(x=> (int)x.goodsType == valdata.refType)?.prefab;
        if(_prefabe == null) return;
        List<GiftIdToWeight> WeightData = getGiftWeightData(valdata.treasureGroup);
        if(valdata.refType == (int)GoodsType.Rune) {
            WeightData = filterRune(WeightData);
        }
        List<int> weightList = getListWeight(WeightData);
        int renderNumber = valdata.extractNum;
        for (int i = 0; i < renderNumber; i++) 
        {
            int index = WeightCalculator.GetWeightedRandomIndex(weightList); // 获取随机索引
            int itemId = WeightData[i].id;
            GameObject prefabItem = Instantiate(_prefabe, ListWrapper.transform); // 创建对应的实体
            BaseGoodsItem script = prefabItem.GetComponent<BaseGoodsItem>();
            script.isGift = true;
            script.shopOpen = true;
            script.onChoose(handleChoose);
            childList.Add(script);
            script.LoadItem(itemId);
        }
    }

    private void handleChoose(BaseGoodsItem valObject) 
    {
        isSelected++;
        renderUI();
        childList.Remove(valObject);
        valObject.GetComponent<BaseGoodsItem>().DestroyItem();
        if(isSelected >= poolEntity.selectNum) {
            closeView();
            isSelected = 0;
        }
    }

    void closeView() {
        childList.Clear();
        ClearListWrapper(); // 先清除旧内容
        transform.gameObject.SetActive(false);
    }


    // 新增：清除 ListWrapper 下的所有子节点
    private void ClearListWrapper()
    {
        if (ListWrapper == null) return;

        // 从后往前遍历销毁子对象
        int childCount = ListWrapper.transform.childCount;
        for (int i = childCount - 1; i >= 0; i--)
        {
            Destroy(ListWrapper.transform.GetChild(i).gameObject);
        }
    }

}
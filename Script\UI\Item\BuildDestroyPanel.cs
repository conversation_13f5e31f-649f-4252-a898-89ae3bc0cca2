using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class BuildDestroyPanel : MonoBehaviour
{
    private BuildingBase buildingBase; // 当前建筑
    [SerializeField] private TMP_Text NameText; // 名称
    [SerializeField] private TMP_Text Desc; // 描述
    [SerializeField] private TMP_Text RecyclePrice; // 回收价格
    [SerializeField] private TMP_Text RecycleRatio; // 回收比例
    [SerializeField] private TMP_Text FunctionName; // 功能名称 "拆除建筑"
    [SerializeField] private TMP_Text get_Text; // 文本"获得"
    [SerializeField] private TMP_Text FunctionText; // 功能
    [SerializeField] private UIRingLoader ringLoader; // 加载进度条

    [SerializeField] private GameObject SlotPrefab; // 插槽预制体
    [SerializeField] private Transform SlotContainer; // 插槽容器
    public Action<BuildingBase> buildingFinish; // 建筑完成
    private int recyclePrice = 0;
    public void SetBuildingData(BuildingBase buildingBase) 
    {
        if(buildingBase == null) return;
        this.buildingBase = buildingBase;
        clearContent();
        string buildingName = Language.GetText(buildingBase.ArchitectureEntity.name);
        NameText.text = buildingName;
        FunctionName.text = Language.GetText(1016);
        Desc.text = Language.GetText(buildingBase.ArchitectureLvEntity.desc);
        get_Text.text = Language.GetText(1017);
        float ratio = getRecycleRatioByLv();
        // 如果recyclePrice是小数 则向上取整
        recyclePrice = (int)Mathf.Ceil(buildingBase.CostSpend * ratio);
        RecyclePrice.text = $"{recyclePrice}";
        RecycleRatio.text = $"({ratio * 100}%)";
        updateFunctionText();
        initSlotItem();
        ringLoader.TriggerKeyCodes = new List<KeyCode>() { KeyCode.G };
        ringLoader.onEnd(DestroyBuilding);
    }

    public void onBuildingFinishing(Action<BuildingBase> action)
    {
        buildingFinish = action;
    }

    // 拆除功能实际实现
    private void DestroyBuilding() {
        if(buildingBase == null) return;
        // 重置到0级
        buildingBase.destroyBuilding();
        // 编辑完成
        buildingBase.EditOver = true;
        // 返还建造经济
        int cost = recyclePrice;
        EventDispatcher.TriggerEvent<int>(EventDispatcherType.JadeIncome, cost);
        // 通知到编辑建筑的主界面
        buildingFinish?.Invoke(buildingBase);
    }

    // 根据lv获取回收比例
    private float getRecycleRatioByLv() {
        int index = Mathf.Clamp(buildingBase.CurrentLv, 1, 3);
        switch (index)
        {
            case 1:
                return unitConfig.coinReturnLv1;
            case 2:
                return unitConfig.coinReturnLv2;
            case 3:
                return unitConfig.coinReturnLv3;
            default:
                return 0;
        }
    }
     
    // 拆除功能检测
    void updateFunctionText() 
    {
        ringLoader.DisableRing = false;
        // 背包剩余空间
        int leftSpace = InventoryManager.Other.RemainingSpace;
        // 满仓
        if (leftSpace < 1)
        {
            FunctionText.text = Language.GetText(1013);
            ringLoader.DisableRing = true;
            return;
        }
        // 背包剩余空间不足
        if(buildingBase.SlotList.Count > leftSpace) 
        {
            FunctionText.text = Language.GetText(1014);
            ringLoader.DisableRing = true;
            return;
        }
        FunctionText.text = Language.GetText(1015);
    }
    // clear 
    private void clearContent()
    {
        recyclePrice = 0;
        NameText.text = "";
        RecyclePrice.text = "0";
        RecycleRatio.text = "";
        Desc.text = "";
    }

    private void initSlotItem() 
    {
        if (buildingBase == null || SlotPrefab == null) return;
        var config = buildingBase.editUiShowAttribute;
        if(!config.showEssenceSlot && !config.showGemSlot) return;
        SlotContainer.gameObject.SetActive(true);
        int slotNumber = buildingBase.MaxSlotNum;
        var insertedList = buildingBase.SlotList;
        var buildLv = buildingBase.CurrentLv;
        for (int i = 0; i < slotNumber; i++) 
        {
            GameObject slotItem = Instantiate(SlotPrefab, SlotContainer);
            var script = slotItem.GetComponent<BuildEditTabItem>();
            script.IsTab = false; // 是否顶部tab
            script.IsInserted = true; // 是否插槽
            script.Index = i; // 索引
            script.LockSlot = i >= buildLv; // 锁定
            GemGoods initialData = insertedList.Count > i ? insertedList[i] : null;
            script.SetData(initialData);
        }
    }
}
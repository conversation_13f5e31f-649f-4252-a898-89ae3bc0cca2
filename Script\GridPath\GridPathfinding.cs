using UnityEngine;
using System.Collections.Generic;
using System.IO;
using UnityEngine.AI;
using UnityEngine.SceneManagement;
using System.Linq;
using System;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using System.Runtime.InteropServices;


public enum GridType
{
    Unwalkable  = 0,
    Walkable    = 1,
    Water       = 2,
    Door        = 4,
}
public class ObstacleRecord
{
    public byte GridType;
    public Vector2Int gridPositions;
}
public partial class GridPathfinding : SceneSingleton<GridPathfinding>
{

    private static byte[,]                      m_walkableGrid;
    private static float[,]                     m_heightGrid;
    private static Vector2Int                   m_gridDimensions;
    private static Vector3                      m_worldOrigin;
    private static float                        m_gridSize;
    public static float gridSize { get => m_gridSize; }
    public static byte[,] walkableGrid { get => m_walkableGrid; }

    //建筑对应的障碍
    private Dictionary<BuildingBase, List<ObstacleRecord>> m_buildingBasesObstacle = new Dictionary<BuildingBase, List<ObstacleRecord>>();

    private static List<GridAgent> m_gridAgentList = new List<GridAgent>();
    public static List<GridAgent> gridAgentList { get => m_gridAgentList; }

    // 存储网格原始值的字典
    private static Dictionary<Vector2Int, byte> m_originalGridValues = new Dictionary<Vector2Int, byte>();


    protected override void Awake()
    {
        SceneManager.sceneLoaded += SceneLoadedCallback;
        SceneManager.sceneUnloaded += SceneUnloadedCallback;
        base.Awake();

        pathResultCallback = OnPathResult;

    }

    private void OnEnable()
    {
        EventDispatcher.AddEventListener<BuildingBase>(EventDispatcherType.BuildUpgrade, OnBuildingUpgrade);
        EventDispatcher.AddEventListener<BuildingBase>(EventDispatcherType.BuildComplete, OnBuildComplete);
        EventDispatcher.AddEventListener<BuildingBase>(EventDispatcherType.BuildingDestroy, OnBuildingDestroy);
    }

    public void OnDisable()
    {
        EventDispatcher.RemoveEventListener<BuildingBase>(EventDispatcherType.BuildUpgrade, OnBuildingUpgrade);
        EventDispatcher.RemoveEventListener<BuildingBase>(EventDispatcherType.BuildComplete, OnBuildComplete);
        EventDispatcher.RemoveEventListener<BuildingBase>(EventDispatcherType.BuildingDestroy, OnBuildingDestroy);
    }

    private void SceneLoadedCallback(Scene scene, LoadSceneMode mode)
    {
        Load(scene.name);
    }
    private void SceneUnloadedCallback(Scene scene)
    {
    }
    public void Load(string sceneName)
    {
        if (!LoadGridData(sceneName))
            return;
    }

    private void OnBuildingUpgrade(BuildingBase building)
    {
        SetBuildingObstacle(building);
    }

    //建筑建造完成事件
    private void OnBuildComplete(BuildingBase building)
    {
        SetBuildingObstacle(building);
    }
    //建筑被摧毁
    private void OnBuildingDestroy(BuildingBase building)
    {
        RestoreBuildingGridValues(building);
    }

    private void SetBuildingObstacle(BuildingBase building)
    {
        if (!m_buildingBasesObstacle.ContainsKey(building))
            m_buildingBasesObstacle.Add(building, new List<ObstacleRecord>());


        NavMeshObstacle obstacle = building.navMeshObstacle;
        if (obstacle == null) return;
        if (!obstacle.enabled) return;

        // 获取障碍物的变换信息
        Transform obstacleTransform = obstacle.transform;
        Vector3 worldCenter         = obstacleTransform.TransformPoint(obstacle.center);
        Vector3 worldSize           = Vector3.Scale(obstacle.size, obstacleTransform.lossyScale);
        Quaternion rotation         = obstacleTransform.rotation;

        // 计算障碍物的八个顶点（考虑旋转）
        Vector3[] corners = new Vector3[8];
        Vector3 extents = Vector3.Scale(obstacle.size, obstacleTransform.lossyScale) * 0.5f;

        corners[0] = worldCenter + rotation * new Vector3(-extents.x, -extents.y, -extents.z);
        corners[1] = worldCenter + rotation * new Vector3(extents.x, -extents.y, -extents.z);
        corners[2] = worldCenter + rotation * new Vector3(-extents.x, -extents.y, extents.z);
        corners[3] = worldCenter + rotation * new Vector3(extents.x, -extents.y, extents.z);
        corners[4] = worldCenter + rotation * new Vector3(-extents.x, extents.y, -extents.z);
        corners[5] = worldCenter + rotation * new Vector3(extents.x, extents.y, -extents.z);
        corners[6] = worldCenter + rotation * new Vector3(-extents.x, extents.y, extents.z);
        corners[7] = worldCenter + rotation * new Vector3(extents.x, extents.y, extents.z);

        // 找出障碍物在网格中的范围
        Vector2Int minGrid = WorldToGrid(corners[0]);
        Vector2Int maxGrid = minGrid;

        for (int i = 1; i < corners.Length; i++)
        {
            Vector2Int gridPos = WorldToGrid(corners[i]);
            minGrid.x = Mathf.Min(minGrid.x, gridPos.x);
            minGrid.y = Mathf.Min(minGrid.y, gridPos.y);
            maxGrid.x = Mathf.Max(maxGrid.x, gridPos.x);
            maxGrid.y = Mathf.Max(maxGrid.y, gridPos.y);
        }

        // 确保网格范围在有效边界内
        minGrid.x = Mathf.Max(0, minGrid.x);
        minGrid.y = Mathf.Max(0, minGrid.y);
        maxGrid.x = Mathf.Min(m_gridDimensions.x - 1, maxGrid.x);
        maxGrid.y = Mathf.Min(m_gridDimensions.y - 1, maxGrid.y);

        // 遍历可能被障碍物占用的所有网格
        for (int x = minGrid.x; x <= maxGrid.x; x++)
        {
            for (int y = minGrid.y; y <= maxGrid.y; y++)
            {
                Vector2Int gridPos = new Vector2Int(x, y);
                Vector3 worldPos = GridToWorld(gridPos);

                worldPos.y = m_heightGrid[x, y];

                // 检查网格中心点是否在旋转后的障碍物范围内
                Vector3 localPoint = Quaternion.Inverse(rotation) * (worldPos - worldCenter);
                if (Mathf.Abs(localPoint.x) <= extents.x &&
                    Mathf.Abs(localPoint.y) <= extents.y &&
                    Mathf.Abs(localPoint.z) <= extents.z)
                {
                    if (IsValidGridPosition(gridPos))
                    {
                        m_buildingBasesObstacle[building].Add(new ObstacleRecord
                        {
                            GridType = m_walkableGrid[gridPos.x, gridPos.y],
                            gridPositions = gridPos
                        });
                        m_walkableGrid[gridPos.x, gridPos.y] = (int)GridType.Unwalkable;
                    }
                }
            }
        }

    }

    private void RestoreBuildingGridValues(BuildingBase building)
    {
        if(!m_buildingBasesObstacle.ContainsKey(building) )
            return;
        // 如果已存在记录，先恢复原来的值
        for (int i = 0; i < m_buildingBasesObstacle[building].Count; i++)
        {
            ObstacleRecord record = m_buildingBasesObstacle[building][i];
            Vector2Int gridPos = record.gridPositions;
            if (IsValidGridPosition(gridPos))
            {
                m_walkableGrid[gridPos.x, gridPos.y] = record.GridType;
            }
        }
        m_buildingBasesObstacle[building].Clear();
        m_buildingBasesObstacle.Remove(building);
    }

    private bool LoadGridData(string sceneName)
    {
        TextAsset gridDataAsset = Resources.Load<TextAsset>($"Scene/{sceneName}/GridData");
        if (gridDataAsset == null)
        {
            Debug.LogWarning($"Failed to load grid data for scene: {sceneName}");
            return false;
        }

        using (MemoryStream stream = new MemoryStream(gridDataAsset.bytes))
        using (BinaryReader reader = new BinaryReader(stream))
        {
            // 读取版本号
            int version = reader.ReadInt32();
            if (version != 1)
            {
                throw new System.Exception("Unsupported data version");
            }

            // 读取基本数据
            m_gridDimensions.x = reader.ReadInt32();
            m_gridDimensions.y = reader.ReadInt32();
            m_worldOrigin.x = reader.ReadSingle();
            m_worldOrigin.y = reader.ReadSingle();
            m_worldOrigin.z = reader.ReadSingle();
            m_gridSize = reader.ReadSingle();

            // 读取场景边界（跳过）
            for (int i = 0; i < 6; i++) // 跳过 boundsCenter 和 boundsSize
            {
                reader.ReadSingle();
            }

            // 初始化并读取网格数据
            m_walkableGrid = new byte[m_gridDimensions.x, m_gridDimensions.y];
            m_heightGrid = new float[m_gridDimensions.x, m_gridDimensions.y];

            int index = 0;
            for (int x = 0; x < m_gridDimensions.x; x++)
            {
                for (int z = 0; z < m_gridDimensions.y; z++)
                {
                    m_walkableGrid[x, z] = reader.ReadByte();
                    m_heightGrid[x, z] = reader.ReadSingle();
                }
            }
        }

        return true;
    }

    // 将世界坐标转换为网格坐标
    public static Vector2Int WorldToGrid(Vector3 worldPosition)
    {
        int x = Mathf.RoundToInt((worldPosition.x - m_worldOrigin.x) / m_gridSize);
        int z = Mathf.RoundToInt((worldPosition.z - m_worldOrigin.z) / m_gridSize);
        return new Vector2Int(x, z);
    }

    // 将网格坐标转换为世界坐标
    public static Vector3 GridToWorld(Vector2Int gridPosition)
    {
        float x = m_worldOrigin.x + gridPosition.x * m_gridSize;
        float z = m_worldOrigin.z + gridPosition.y * m_gridSize;
        float y = m_heightGrid[gridPosition.x, gridPosition.y];
        return new Vector3(x, y, z);
    }


    // 设置网格值
    public static void SetGridValue(Vector2Int position, byte value)
    {
        if (Instance == null || !IsValidGridPosition(position)) return;
        
        // 保存原始值（如果还没有保存过）
        if (!m_originalGridValues.ContainsKey(position))
        {
            m_originalGridValues[position] = m_walkableGrid[position.x, position.y];
        }
        
        // 设置新值
        m_walkableGrid[position.x, position.y] = value;
    }

    // 恢复网格原始值
    public static void RestoreGridValue(Vector2Int position)
    {
        if (Instance == null || !IsValidGridPosition(position)) return;

        // 如果有保存原始值，则恢复
        if (m_originalGridValues.ContainsKey(position))
        {
            m_walkableGrid[position.x, position.y] = m_originalGridValues[position];
            m_originalGridValues.Remove(position);
        }
    }

    // 检查位置是否有效
    public static bool IsValidGridPosition(Vector2Int position)
    {
        return position.x >= 0 && position.x < m_gridDimensions.x &&
               position.y >= 0 && position.y < m_gridDimensions.y;
    }

    // 获取网格值
    public static int GetGridValue(Vector2Int position)
    {
        if (!IsValidGridPosition(position)) return (int)GridType.Unwalkable;
        return m_walkableGrid[position.x, position.y];
    }

    /// <summary>
    /// 获取网格位置的原始值
    /// </summary>
    /// <param name="position">网格坐标</param>
    /// <returns>返回该位置的原始网格值，如果没有记录原始值则返回当前值</returns>
    public static int GetOriginalGridValue(Vector2Int position)
    {
        if (Instance == null || !IsValidGridPosition(position))
            return (int)GridType.Unwalkable;

        // 检查是否有保存的原始值
        if (m_originalGridValues.ContainsKey(position))
        {
            return m_originalGridValues[position];
        }

        // 如果没有保存原始值，返回当前值
        return m_walkableGrid[position.x, position.y];
    }

    /// <summary>
    /// 静态方法用于全局寻路调用
    /// </summary>
    /// <param name="start">起点世界坐标</param>
    /// <param name="end">终点世界坐标</param>
    /// <returns>返回路径点列表，如果无法到达则返回null</returns>
    public static List<Vector3> GetPath(Vector3 startWorld, Vector3 endWorld)
    {
        Vector2Int startGrid = WorldToGrid(startWorld);
        Vector2Int endGrid = WorldToGrid(endWorld);
        //Debug.Log("FindPathEnhance start  " + Time.realtimeSinceStartup);
        byte[] gridData = new byte[m_gridDimensions.x * m_gridDimensions.y];
        for (int x = 0; x < m_gridDimensions.x; x++)
        {
            for (int y = 0; y < m_gridDimensions.y; y++)
            {
                // 使用行优先顺序填充一维数组
                gridData[y * m_gridDimensions.x + x] = m_walkableGrid[x, y];
            }
        }
        IntPtr resultPtr = CreatePathJPS(
            m_gridDimensions.x, m_gridDimensions.y,
            gridData,
            startGrid.x, startGrid.y,
            endGrid.x, endGrid.y, 1);

        if (resultPtr != IntPtr.Zero)
        {
            // 解析结果
            PathResult result = Marshal.PtrToStructure<PathResult>(resultPtr);

            // 创建路径点列表
            List<Vector3> path = new List<Vector3>(result.length);

            // 读取路径坐标
            int[] pathX = new int[result.length];
            int[] pathY = new int[result.length];

            Marshal.Copy(result.pathX, pathX, 0, result.length);
            Marshal.Copy(result.pathY, pathY, 0, result.length);

            // 转换为世界坐标
            for (int i = 0; i < result.length; i++)
            {
                Vector3 worldPos = new Vector3(
                    pathX[i] * gridSize + m_worldOrigin.x,
                    m_heightGrid[pathX[i], pathY[i]],
                    pathY[i] * gridSize + m_worldOrigin.z
                );
                path.Add(worldPos);
            }
            path.RemoveAt(0);
            // 释放C++分配的内存
            DestroyPath(resultPtr);
            //Debug.Log("FindPathEnhance end  " + Time.realtimeSinceStartup);
            return path;
        }
        else
        {
            Debug.LogWarning($"Failed to find path {startWorld} {endWorld}  {Time.realtimeSinceStartup}");
            return null;
        }
    }

    /// <summary>
    /// 重建并简化从起点到终点的路径
    /// </summary>
    private List<Vector3> ReconstructPath(Dictionary<Vector2Int, Vector2Int> cameFrom, Vector2Int end)
    {
        // 1. 构建初始网格路径
        var gridPath = new List<Vector2Int>();
        Vector2Int current = end;

        while (cameFrom.ContainsKey(current))
        {
            gridPath.Add(current);
            current = cameFrom[current];
        }
        gridPath.Add(current); // 添加起点
        gridPath.Reverse();

        // 2. 如果路径点太少，直接返回
        if (gridPath.Count <= 2)
        {
            var simplePath = new List<Vector3>();
            foreach (var point in gridPath)
            {
                simplePath.Add(GridToWorld(point));
            }
            return simplePath;
        }

        // 3. 简化路径
        var optimizedPath = SimplifyPath(gridPath);

        // 4. 转换为世界坐标
        var worldPath = new List<Vector3>();
        foreach (var point in optimizedPath)
        {
            worldPath.Add(GridToWorld(point));
        }

        return worldPath;
    }

    /// <summary>
    /// 简化路径，将在同一直线上的点简化为线段的起点和终点
    /// </summary>
    private List<Vector2Int> SimplifyPath(List<Vector2Int> path)
    {
        if (path.Count <= 2) return path;

        var simplified = new List<Vector2Int>();
        simplified.Add(path[0]); // 添加起点

        int i = 0;
        while (i < path.Count - 1)
        {
            int lineEnd = FindLineEnd(path, i);
            
            // 如果找到的终点不是下一个点，说明有一条直线
            if (lineEnd > i + 1)
            {
                i = lineEnd - 1; // 下一轮从终点的前一个点开始检查
            }
            
            // 添加当前线段的终点
            simplified.Add(path[lineEnd]);
            i++;
        }

        return simplified;
    }

    /// <summary>
    /// 找到从起点开始的一条直线的终点
    /// </summary>
    private int FindLineEnd(List<Vector2Int> path, int startIndex)
    {
        if (startIndex >= path.Count - 1) return startIndex;

        Vector2Int startPoint = path[startIndex];
        Vector2Int direction = path[startIndex + 1] - startPoint;
        
        int endIndex = startIndex + 1;
        
        // 检查后续点是否在同一直线上
        for (int i = startIndex + 2; i < path.Count; i++)
        {
            Vector2Int currentDirection = path[i] - startPoint;
            
            // 检查是否共线：使用叉积为0判断
            // 对于2D向量，叉积为0表示它们平行（在同一直线上）
            if (Cross2D(direction, currentDirection) == 0)
            {
                // 确保方向一致（点在同一方向上）
                if (Vector2.Dot(direction, currentDirection) > 0)
                {
                    endIndex = i;
                }
                else
                {
                    break;
                }
            }
            else
            {
                break;
            }
        }

        return endIndex;
    }

    /// <summary>
    /// 计算两个2D向量的叉积
    /// </summary>
    private static int Cross2D(Vector2Int a, Vector2Int b)
    {
        return a.x * b.y - a.y * b.x;
    }

    private struct PathNode
    {
        public Vector2Int Position;
        public float F;  // F = G + H
        public float H;  // 启发式估计值（到终点的距离）
        public Vector2Int Direction;  // 添加方向信息
    }

    public static bool IsWalkable(Vector2Int position)
    {
        return m_walkableGrid[position.x, position.y] != (int)GridType.Unwalkable;
    }

    /// <summary>
    /// 检查世界坐标位置是否可行走
    /// </summary>
    /// <param name="worldPosition">要检查的世界坐标</param>
    /// <returns>如果位置可行走返回true，否则返回false</returns>
    public static bool IsWalkableAtPosition(Vector3 worldPosition)
    {
        if (Instance == null) return false;

        // 将世界坐标转换为网格坐标
        Vector2Int gridPosition = WorldToGrid(worldPosition);

        // 检查该网格位置是否可行走
        return IsWalkable(gridPosition);
    }
    /// <summary>
    /// 检查网格位置是否原始可行走，优先检查原始值
    /// </summary>
    /// <param name="position">网格坐标</param>
    /// <returns>如果位置原始可行走返回true，否则返回false</returns>
    public static bool IsOriginalWalkable(Vector2Int position)
    {
        if (Instance == null )
            return false;

        // 优先检查原始值
        if (m_originalGridValues.ContainsKey(position))
        {
            return m_originalGridValues[position] != (int)GridType.Unwalkable;
        }

        // 如果没有原始值记录，检查当前值
        return m_walkableGrid[position.x, position.y] != (int)GridType.Unwalkable;
    }

    /// <summary>
    /// 检查世界坐标位置是否原始可行走
    /// </summary>
    /// <param name="worldPosition">要检查的世界坐标</param>
    /// <returns>如果位置原始可行走返回true，否则返回false</returns>
    public static bool IsOriginalWalkableAtPosition(Vector3 worldPosition)
    {
        if (Instance == null) return false;

        // 将世界坐标转换为网格坐标
        Vector2Int gridPosition = WorldToGrid(worldPosition);

        // 检查该网格位置是否原始可行走
        return IsOriginalWalkable(gridPosition);
    }


    /// <summary>
    /// 获取从起点到目标点之间可直线行走的最远点
    /// </summary>
    /// <param name="startPosition">起点世界坐标</param>
    /// <param name="targetPosition">目标世界坐标</param>
    /// <param name="farthestPoint">输出参数：可到达的最远点</param>
    /// <returns>如果可以直接到达目标点返回true，否则返回false</returns>
    public static bool GetFarthestReachablePoint(Vector3 startPosition, Vector3 targetPosition, bool useOriginal, out Vector3 farthestPoint)
    {
        if (Instance == null)
        {
            farthestPoint = startPosition;
            return false;
        }

        // 转换为网格坐标
        Vector2Int startGrid = WorldToGrid(startPosition);
        Vector2Int targetGrid = WorldToGrid(targetPosition);

        // 使用Bresenham算法检查路径
        List<Vector2Int> pathPoints = GetLinePoints(startGrid, targetGrid);

        // 检查每个点是否可行走
        Vector2Int lastWalkable = startGrid;
        for (int i = 0; i < pathPoints.Count; i++)
        {
            if (HasObstaclesInRadius(pathPoints[i], 0.5f, useOriginal))
            {
                // 找到第一个不可行走的点，返回前一个点
                farthestPoint = GridToWorld(lastWalkable);
            return false;
            }
            lastWalkable = pathPoints[i];
        }

        // 整条路径都可行走
        farthestPoint = targetPosition;
        return true;
    }

    /// <summary>
    /// 使用Bresenham算法获取两点之间的所有网格点
    /// </summary>
    public static List<Vector2Int> GetLinePoints(Vector2Int start, Vector2Int end)
    {
        List<Vector2Int> points = new List<Vector2Int>();

        int x0 = start.x;
        int y0 = start.y;
        int x1 = end.x;
        int y1 = end.y;

        bool steep = Mathf.Abs(y1 - y0) > Mathf.Abs(x1 - x0);
        if (steep)
        {
            // 交换 x 和 y
            int temp = x0;
            x0 = y0;
            y0 = temp;

            temp = x1;
            x1 = y1;
            y1 = temp;
        }

        if (x0 > x1)
        {
            // 交换起点和终点
            int temp = x0;
            x0 = x1;
            x1 = temp;

            temp = y0;
            y0 = y1;
            y1 = temp;
        }

        int dx = x1 - x0;
        int dy = Mathf.Abs(y1 - y0);
        int error = dx / 2;

        int ystep = (y0 < y1) ? 1 : -1;
        int y = y0;

        for (int x = x0; x <= x1; x++)
        {
            Vector2Int point = steep ? new Vector2Int(y, x) : new Vector2Int(x, y);
            points.Add(point);

            error -= dy;
            if (error < 0)
            {
                y += ystep;
                error += dx;
            }
        }

        return points;
    }


    /// <summary>
    /// 检查指定网格位置半径范围内是否有障碍物
    /// </summary>
    /// <param name="position">世界坐标</param>
    /// <param name="radius">检查半径（世界单位）</param>
    /// <returns>如果半径范围内有障碍物返回true，否则返回false</returns>
    public static bool HasObstaclesInRadius(Vector3 position, float radius, bool useOriginal = false)
    {
        return HasObstaclesInRadius(WorldToGrid(position), radius, useOriginal);
    }
    /// <summary>
    /// 检查指定网格位置半径范围内是否有障碍物
    /// </summary>
    /// <param name="position">网格坐标</param>
    /// <param name="radius">检查半径</param>
    /// <param name="useOriginal">是否使用原始网格值</param>
    /// <returns>如果矩形范围内有障碍物返回true，否则返回false</returns>
    public static bool HasObstaclesInRadius(Vector2Int position, float radius, bool useOriginal = false)
    {
        // 计算半径对应的网格数量
        int gridRadius = Mathf.CeilToInt(radius / m_gridSize);
        int gridRadiusSqr = gridRadius * gridRadius;

        Vector2Int gridPos = new Vector2Int();
        // 检查矩形范围内的网格
        for (int x = -gridRadius; x <= gridRadius; x++)
        {
            for (int z = -gridRadius; z <= gridRadius; z++)
            {
                // 计算网格坐标
                gridPos.x = position.x + x;
                gridPos.y = position.y + z;

                // 计算到中心的距离平方
                float distanceSq = x * x + z * z;
                
                // 检查是否在半径范围内 (使用距离平方比较避免开方)
                if (distanceSq <= gridRadiusSqr)
                {
                    // 检查网格位置是否有效且不可行走
                    if (!IsValidGridPosition(gridPos))
                    {
                        return true; // 发现障碍物
                    }
                    if (useOriginal)
                    {
                        if (!IsOriginalWalkable(gridPos))
            return true;
                    }
                    else
                    {
                        if (!IsWalkable(gridPos))
                            return true;
                    }
                }
            }
        }

        return false; // 没有发现障碍物
    }

    /// <summary>
    /// 以targetPosition为中心在指定半径上找到一个可行走的点，该点距离其他agent至少大于agent半径
    /// </summary>
    /// <param name="selfPosition">自身位置</param>
    /// <param name="targetPosition">目标位置</param>
    /// <param name="radius">圆的半径</param>
    /// <param name="pointOnRadius">输出参数：找到的半径上的点</param>
    /// <returns>如果找到合适的点返回true，否则返回false</returns>
    public static bool FindWalkableOnRadius(Transform self, Vector3 targetPosition, float radius, float actorRadius, out Vector3 pointOnRadius)
    {
        if (Instance == null)
        {
            pointOnRadius = self.position;
            return false;
        }

        // 默认返回值为自身位置
        pointOnRadius = self.position;

        // 计算从目标到自身的方向
        Vector3 directionToSelf = self.position - targetPosition;
        directionToSelf.y = 0; // 忽略高度差异

        // 如果方向为零向量，选择一个随机方向
        if (directionToSelf.sqrMagnitude < 0.001f)
        {
            directionToSelf = new Vector3(UnityEngine.Random.Range(-1f, 1f), 0, UnityEngine.Random.Range(-1f, 1f)).normalized;
        }
        else
        {
            directionToSelf.Normalize();
        }

        // 创建候选点列表
        List<Vector3> candidatePoints = new List<Vector3>();

        // 在半径上生成候选点
        int numSamples = 100; // 采样点数量，增加采样点以获得更精确的结果

        for (int i = 0; i < numSamples; i++)
        {
            // 计算角度
            float angle = 2 * Mathf.PI * i / numSamples;

            // 计算偏移方向
            Vector3 offset = new Vector3(
                Mathf.Cos(angle),
                0,
                Mathf.Sin(angle)
            );

            // 旋转偏移方向，使其相对于从目标到自身的方向
            Quaternion rotation = Quaternion.LookRotation(directionToSelf);
            offset = rotation * offset;

            // 计算候选点位置 - 精确在半径上
            Vector3 candidatePoint = targetPosition + offset * radius;

            // 检查点是否可行走
            if (IsWalkableAtPosition(candidatePoint))
            {
                // 检查点半径范围内是否有障碍物
                Vector2Int gridPos = WorldToGrid(candidatePoint);
                if (!HasObstaclesInRadius(gridPos, actorRadius)) // 使用较小的检查半径
                {
                    // 检查点是否与其他agent保持足够距离
                    bool isFarEnoughFromAgents = true;

                    for (int j = 0; j < gridAgentList.Count; j++)
                    {
                        var agent = gridAgentList[j];
                        if (agent.transform != self) // 排除自身
                        {
                            float minDistanceRequired = agent.Radius;
                            if (Vector3.SqrMagnitude(candidatePoint - agent.transform.position) < (minDistanceRequired * minDistanceRequired))
                            {
                                isFarEnoughFromAgents = false;
                                break;
                            }
                        }
                    }

                    if (isFarEnoughFromAgents)
                    {
                        candidatePoints.Add(candidatePoint);
                    }
                }
            }
        }

        // 如果没有找到候选点，返回false
        if (candidatePoints.Count == 0)
        {
            return false;
        }

        // 找到距离自身最近的候选点
        float minDistance = float.MaxValue;

        for (int i = 0; i < candidatePoints.Count; i++)
        {
            var point = candidatePoints[i];
            float distanceSqr = Vector3.SqrMagnitude(self.transform.position - point);
            if (distanceSqr < (minDistance* minDistance))
            {
                minDistance = distanceSqr;
                pointOnRadius = point;
            }
        }

        return true;
    }

    public void Update()
    {

    }
    /// <summary>
    /// 在指定方向上找到一个没有障碍物的点
    /// </summary>
    /// <param name="startPosition">起始位置</param>
    /// <param name="direction">方向</param>
    /// <param name="distance">距离</param>
    /// <param name="radius">检查半径,半径范围内没有障碍</param>
    /// <param name="result">找到的点</param>
    /// <returns>是否找到有效点</returns>
    public static bool FindWabkableInDirection(Vector3 startPosition, Vector3 direction, float distance, float radius, out Vector3 result)
    {
        if (Instance == null)
        {
            result = startPosition;
            return false;
        }

        direction.y = 0; // 忽略Y轴方向
        direction.Normalize();
        
        // 计算目标点
        Vector3 targetPosition = startPosition + direction * distance;
        
        // 检查目标点是否可行走
        if (IsWalkableAtPosition(targetPosition) && !HasObstaclesInRadius(WorldToGrid(targetPosition), radius))
        {
            result = targetPosition;
            return true;
        }
        
        // 如果目标点不可行走，尝试在周围寻找可行走点
        for (float offset = 1; offset <= 4 * 2; offset += 0.5f)
        {
            // 尝试左侧点
            Vector3 leftDir = Quaternion.Euler(0, -45, 0) * direction;
            Vector3 leftPos = startPosition + direction * (distance - offset) + leftDir * offset;
            
            if (IsWalkableAtPosition(leftPos) && !HasObstaclesInRadius(WorldToGrid(leftPos), radius))
            {
                result = leftPos;
            return true;
        }

            // 尝试右侧点
            Vector3 rightDir = Quaternion.Euler(0, 45, 0) * direction;
            Vector3 rightPos = startPosition + direction * (distance - offset) + rightDir * offset;
            
            if (IsWalkableAtPosition(rightPos) && !HasObstaclesInRadius(WorldToGrid(rightPos), radius))
            {
                result = rightPos;
                return true;
            }
            
            // 尝试更近的点
            Vector3 closerPos = startPosition + direction * (distance - offset);
            
            if (IsWalkableAtPosition(closerPos) && !HasObstaclesInRadius(WorldToGrid(closerPos), radius))
            {
                result = closerPos;
                return true;
            }
        }
        
        result = startPosition;
        return false;
    }

    /// <summary>
    /// 检查起点和终点是否可以直线到达，如果可以则生成直线路径
    /// </summary>
    /// <param name="start">起点世界坐标</param>
    /// <param name="end">终点世界坐标</param>
    /// <param name="radius">单位半径</param>
    /// <returns>如果可以直线到达则返回路径，否则返回null</returns>
    public static List<Vector3> GetDirectPath(Vector3 start, Vector3 end, float radius = 0)
    {
        if (Instance == null) return null;
        
        // 转换为网格坐标
        Vector2Int startGrid = WorldToGrid(start);
        Vector2Int endGrid = WorldToGrid(end);
        
        // 使用Bresenham算法获取路径上的所有点
        List<Vector2Int> pathPoints = GetLinePoints(startGrid, endGrid);
        
        // 检查路径上的每个点是否可行走
        for (int i = 0; i < pathPoints.Count; i++)
        {
            Vector2Int point = pathPoints[i];
            if (!IsWalkable(point))
            {
                return null; // 路径上有不可行走的点，返回null
            }
            
            // 如果需要考虑单位半径
            if (radius > 0 && HasObstaclesInRadius(point, radius))
            {
                return null; // 路径上有点的半径范围内有障碍物，返回null
            }
        }
        
        // 可以直线到达，生成简单路径
        List<Vector3> directPath = new List<Vector3>
        {
            start,  // 起点
            end     // 终点
        };
        
        return directPath;
    }


    /// <summary>
    /// 在起点和终点之间的60度扇形区域内找到一个可行走点
    /// </summary>
    /// <param name="start">起点世界坐标</param>
    /// <param name="end">终点世界坐标</param>
    /// <param name="minRadius">最小半径</param>
    /// <param name="maxRadius">最大半径</param>
    /// <param name="agentRadius">Agent半径</param>
    /// <param name="result">找到的可行走点</param>
    /// <returns>如果找到合适的点返回true，否则返回false</returns>
    public static bool FindWalkablePointInSector(Vector3 start, Vector3 end, float minRadius, float maxRadius, float agentRadius, out Vector3 result)
    {
        if (Instance == null)
        {
            result = start;
        return false;
        }

        // 计算方向和距离
        Vector3 direction = end - start;
        direction.y = 0; // 忽略Y轴
        float distance = direction.magnitude;
        
        // 如果距离太近，直接返回终点
        if (distance < minRadius)
        {
            if (IsWalkableAtPosition(end) && !HasObstaclesInRadius(WorldToGrid(end), agentRadius))
            {
                result = end;
                return true;
            }
        }
        
        direction.Normalize();
        
        // 扇形角度 (60度)
        float sectorAngle = 60f;
        float halfAngle = sectorAngle / 2f;
        
        // 角度采样间隔 (15度)
        float angleStep = 15f;
        
        // 创建候选点列表
        List<Vector3> candidates = new List<Vector3>();
        
        // 从最小半径开始，逐渐增加半径进行采样
        float currentRadius = minRadius;
        
        while (currentRadius <= maxRadius)
        {
            bool foundValidPoint = false;
            
            // 在当前半径上，按角度间隔采样
            for (float angle = -halfAngle; angle <= halfAngle; angle += angleStep)
            {
                // 计算旋转后的方向
                Vector3 rotatedDir = Quaternion.Euler(0, angle, 0) * direction;
                
                // 计算候选点位置
                Vector3 candidatePoint = start + rotatedDir * currentRadius;

                // 检查点是否可行走且半径范围内没有障碍物
                if (IsWalkableAtPosition(candidatePoint) && !HasObstaclesInRadius(WorldToGrid(candidatePoint), agentRadius))
                {
                    candidates.Add(candidatePoint);
                    foundValidPoint = true;
                }
            }
            
            // 如果在当前半径上找到了有效点，就不再增加半径
            if (foundValidPoint)
            {
                break;
            }
            
            // 增加半径并继续采样
            currentRadius += 1f;
        }
        
        // 如果没有找到候选点，尝试在直线上找点
        if (candidates.Count == 0)
        {
            // 在直线上均匀采样几个点
            float stepDistance = 1f;
            float currentDistance = minRadius;
            
            while (currentDistance <= Mathf.Min(maxRadius, distance))
            {
                Vector3 point = start + direction * currentDistance;
                
                if (IsWalkableAtPosition(point) && !HasObstaclesInRadius(WorldToGrid(point), agentRadius))
                {
                    candidates.Add(point);
                    break;
                }
                
                currentDistance += stepDistance;
            }
        }
        
        // 如果仍然没有找到候选点，返回失败
        if (candidates.Count == 0)
        {
            result = start;
            return false;
        }
        
        // 找到距离终点最近的候选点
        float minDistToEnd = float.MaxValue;
        int bestIndex = 0;
        
        for (int i = 0; i < candidates.Count; i++)
        {
            float dist = Vector3.Distance(candidates[i], end);
            if (dist < minDistToEnd)
            {
                minDistToEnd = dist;
                bestIndex = i;
            }
        }
        
        result = candidates[bestIndex];
        return true;
    }

    /// <summary>
    /// 获取指定世界坐标位置的高度
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <returns>该位置的高度值，如果位置无效则返回0</returns>
    public static float GetHeightAtPosition(Vector3 worldPosition)
    {
        if (Instance == null) return 0f;

        // 将世界坐标转换为网格坐标
        Vector2Int gridPosition = WorldToGrid(worldPosition);

        // 检查网格坐标是否有效
        if (!IsValidGridPosition(gridPosition))
            return 0f;

        // 返回该位置的高度值
        return m_heightGrid[gridPosition.x, gridPosition.y];
    }
}

/// <summary>
/// 寻路状态枚举
/// </summary>
public enum PathStatus
{
    /// <summary>
    /// 无路径
    /// </summary>
    NoPath,

    /// <summary>
    /// 正在寻路中
    /// </summary>
    Searching,

    /// <summary>
    /// 找到完整路径
    /// </summary>
    Complete,

    /// <summary>
    /// 寻路失败
    /// </summary>
    Failed
}

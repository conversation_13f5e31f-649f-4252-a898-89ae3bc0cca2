using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Excel.skill;
using Excel.skillDisplay;
using Excel.hero;
using Excel.monster;
using Excel.lable;
using Excel.text;
using Excel.WeaponAnimation;
using Excel.empower;

public class ExcelData : Singleton<ExcelData>
{
    #region skill excel
    [SerializeField]
    private skillExcel m_skillExcel;
    public skillExcel SkillExcel { get => m_skillExcel; }

    public skillbaseEntity GetSkillBase(int skillid)
    {
        return m_skillExcel.skillbaseList.Find(skill => skill.id == skillid);
    }

    public triggerEntity GetTrigger(int triggerID)
    {
        return m_skillExcel.triggerList.Find(skill => skill.id == triggerID);
    }


    public flightpropsEntity GetFlightProps(int flightid)
    {
        return m_skillExcel.flightpropsList.Find(element => element.id == flightid);
    }

    public effectEntity GetSkillEffect(int effectID)
    {
        return m_skillExcel.effectList.Find(element => element.id == effectID);
    }

    public rangeEntity GetSkillRange(int rangeID)
    {
        return m_skillExcel.rangeList.Find(element => element.id == rangeID);
    }
    public targetEntity GetSkillTarget(int targetID)
    {
        return m_skillExcel.targetList.Find(element => element.id == targetID);
    }
    public buffpoolEntity GetBuffPool(int buffPoolID)
    {
        return m_skillExcel.buffpoolList.Find(element => element.id == buffPoolID);
    }
    public buffEntity GetBuff(int buffID)
    {
        return m_skillExcel.buffList.Find(element => element.id == buffID);
    }
    #endregion

    #region skillDisplay
    [SerializeField]
    public skillDisplayExcel m_skillDisplayExcel;
    public skillshowEntity GetSkillShow(int id)
    {
        return m_skillDisplayExcel.skillshowList.Find(element => element.id == id);
    }

    public effectshowEntity GetEffectShow(int id)
    {
        return m_skillDisplayExcel.effectshowList.Find(element => element.id == id);
    }

    public buffshowEntity GetBuffShow(int id)
    {
        return m_skillDisplayExcel.buffshowList.Find(element => element.id == id);
    }
    #endregion

    #region hero
    [SerializeField]
    private heroExcel m_heroExcel;
    public heroExcel HeroExcel { get => m_heroExcel; }
    public herobaseEntity GetHeroBase(int id)
    {
        return m_heroExcel.herobaseList.Find(element => element.id == id);
    }
    public heroSkillEntity GetHeroSkill(int id)
    {
        return m_heroExcel.heroSkillList.Find(element => element.id == id);
    }
    #endregion

    #region monster
    [SerializeField]
    private monsterExcel m_monsterExcel;
    public monsterExcel MonsterExcel { get => m_monsterExcel; }

    public monsterBaseEntity GetMonster(int id)
    {
        return m_monsterExcel.monsterBaseList.Find(element => element.Id == id);
    }
    #endregion

    [SerializeField]
    private unitExcel m_unitExcel;
    /// <summary>
    /// unit#建造 表格
    /// </summary>
    public unitExcel UnitExcel { get => m_unitExcel; }

    [SerializeField]
    private mapExcel m_mapExcel;
    /// <summary>
    /// map#地图 表格
    /// </summary>
    public mapExcel MapExcel { get => m_mapExcel; }

    [SerializeField]
    private petExcel m_petExcel;
    /// <summary>
    /// pet#灵兽表格
    /// </summary>
    public petExcel PetExcel { get => m_petExcel; }

    [SerializeField]
    private runeExcel m_runeExcel;
    /// <summary>
    /// rune#符文 表格
    /// </summary>
    public runeExcel RuneExcel { get => m_runeExcel; }

    [SerializeField]
    private textExcel m_textExcel;
    /// <summary>
    /// text#文本 表格
    /// </summary>
    public textExcel TextExcel { get => m_textExcel; }

    [SerializeField]
    private fightExcel m_fightExcel;
    /// <summary>
    /// fight#战斗 表格
    /// </summary>
    public fightExcel FightExcel { get => m_fightExcel; }

    [SerializeField]
    private pointsExcel m_pointsExcel;
    /// <summary>
    /// points#分数 表格
    /// </summary>
    public pointsExcel PointsExcel { get => m_pointsExcel; }

    [SerializeField]
    private MiniMapExcel m_miniMapExcel;
    public MiniMapExcel MiniMapExcel { get => m_miniMapExcel; }

    [SerializeField] private lableExcel m_lableExcel;
    public lableExcel LableExcel { get => m_lableExcel; }
    public lableEntity getLableEntity(int id) 
    {
        return m_lableExcel?.lableList?.Find(element => element.id == id);
    }

    [SerializeField]
    private WeaponAnimationExcel m_weaponAnimationExcel;
    public WeaponAnimationExcel WeaponAnimationExcel { get => m_weaponAnimationExcel; }

    public WeaponAnimatioEntity GetWeaponAnimation(int id)
    {
        return m_weaponAnimationExcel.WeaponAnimatioList.Find(element => element.id == id);
    }

    #region shop Excel
    [SerializeField]
    private shopExcel m_shopExcel;
    public shopExcel ShopExcel { get => m_shopExcel; }
    #endregion

    #region empower Excel
    [SerializeField]
    private empowerExcel m_empowerExcel;
    public empowerExcel EmpowerExcel { get => m_empowerExcel; }
    public empowerItemEntity GetEmpowerItemEntity(int id)
    {
        return m_empowerExcel.empowerItemList.Find(element => element.Id == id);
    }
    public empowerEffectEntity GetEmpowerEffectEntity(int id)
    {
        return m_empowerExcel.empowerEffectList.Find(element => element.id == id);
    }
    #endregion

    public override void Awake()
    {
        base.Awake();
        if(m_skillExcel == null )
            m_skillExcel = Resources.Load<skillExcel>("Excel/SkillExcel");

        if (m_skillDisplayExcel == null)
            m_skillDisplayExcel = Resources.Load<skillDisplayExcel>("Excel/skillDisplayExcel");

        if( m_fightExcel == null)
            m_fightExcel = Resources.Load<fightExcel>("Excel/fightExcel");

        if (m_heroExcel == null)
            m_heroExcel = Resources.Load<heroExcel>("Excel/heroExcel");

        if (m_mapExcel == null)
            m_mapExcel = Resources.Load<mapExcel>("Excel/mapExcel");

        if (m_monsterExcel == null)
            m_monsterExcel = Resources.Load<monsterExcel>("Excel/monsterExcel");

        if (m_petExcel == null)
            m_petExcel = Resources.Load<petExcel>("Excel/petExcel");

        if (m_runeExcel == null)
            m_runeExcel = Resources.Load<runeExcel>("Excel/runeExcel");

        if (m_unitExcel == null)
            m_unitExcel = Resources.Load<unitExcel>("Excel/unitExcel");

        if( m_textExcel == null)
            m_textExcel = Resources.Load<textExcel>("Excel/textExcel");

        if( m_pointsExcel == null)
            m_pointsExcel = Resources.Load<pointsExcel>("Excel/pointsExcel");
        
        if( m_lableExcel == null)
            m_lableExcel = Resources.Load<lableExcel>("Excel/lableExcel");

        if (m_weaponAnimationExcel == null)
            m_weaponAnimationExcel = Resources.Load<WeaponAnimationExcel>("Excel/WeaponAnimationExcel");
        
        if (m_shopExcel == null)
            m_shopExcel = Resources.Load<shopExcel>("Excel/shopExcel");
        
        if (m_empowerExcel == null)
            m_empowerExcel = Resources.Load<empowerExcel>("Excel/empowerExcel");
    }


}

using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
public class RuneInventory : MonoBehaviour
{
    public static RuneInventory Instance;
    [SerializeField] GameObject ItemPrefab;
    public List<BaseGoodsData> buyList { get => store.AllItems; }
    private InventoryBase store = null; // 仓库
    private List<BaseGoodsItem> list = new List<BaseGoodsItem>();

    // 新增开关属性和位置存储
    private RectTransform rectTransform; // 获取UI组件
    private Vector2 originalAnchoredPos; // 存储初始UI位置
    float viewMove = 70f; // 列表移动距离
    void Awake()
    {
        store = store ?? InventoryManager.Rune;
        rectTransform = GetComponent<RectTransform>();
        originalAnchoredPos = rectTransform.anchoredPosition;
        Instance = this;
        // 监听商店是否打开
        EventDispatcher.AddEventListener<bool>(EventDispatcherType.ShopChange, ToggleInventoryPanel);
    }

    private void Start()
    {
        init();
    }
    private void init()
    {
        for(int i = 0; i < list.Count; i++)
        {
            Destroy(list[i].gameObject);
        }
        list.Clear();
        // 获取背包内的物品
        if(store.ItemCount <= 0) return;
        for (int i = 0; i < store.ItemCount; i++)
        {
            LoadItem(store.AllItems[i]);
        }
    }
    // 新增开关方法
    public void ToggleInventoryPanel(bool open)
    {
        // 更新Item的状态
        setItemShopState(open);

        float targetY = open ? originalAnchoredPos.y - viewMove : originalAnchoredPos.y;
        
        // 使用UI专用的锚点动画
        rectTransform.DOAnchorPosY(targetY, 0.3f)
            .SetEase(Ease.OutQuad)
            .SetUpdate(true); // 确保在Time.timeScale=0时仍能播放
    }
    // 设置Item的状态
    private void setItemShopState(bool open)
    {
        if(list.Count <= 0) return;
        for(int i = 0; i < list.Count; i++)
        {
            list[i].shopOpen = open;
        }
    }

    /// <summary>
    /// 加载单个Item
    /// </summary>
    /// <param name="data"></param>
    private BaseGoodsItem LoadItem(BaseGoodsData val) 
    {
        if(val == null || ItemPrefab == null) return null;
        GameObject itemPrefab = Instantiate(ItemPrefab, transform);
        BaseGoodsItem script = itemPrefab.GetComponent<BaseGoodsItem>();
        script.isGift = false;
        script.shopOpen = true;
        script.LoadItem(val.id);
        // script.setPrice(val.sellPrice);
        script.onSell(x=> { 
            store.RemoveItem(x); 
            EventDispatcher.TriggerEvent<int>(EventDispatcherType.SellRune, x.id);
        });
        script.setBackImg(val.background);
        return script;
    }

    public bool AddItem(BaseGoodsItem val) 
    {
        BaseGoodsData goods = val.baseGoodsData;
        // 校验
        if (!store.CheckAdd(goods)) return false;
        // 新增
        BaseGoodsItem newScript = LoadItem(goods);
        if(newScript == null) return false;
        bool res = store.AddItem(goods);
        list.Add(newScript);
        if(!res) 
        {
            Debug.Log("添加失败");
            newScript.DestroyItem();
            return false;
        };
        // 成功
        return true;
    }

}
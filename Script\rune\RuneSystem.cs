using Excel.rune;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class RuneSystem : SceneSingleton<RuneSystem>
{
    private List<Rune> m_runeList = new List<Rune>();
    private void OnEnable()
    {
        EventDispatcher.AddEventListener<BeastDomain>(EventDispatcherType.BeastGameWin, OnBeastGameWin);
        EventDispatcher.AddEventListener<int>(EventDispatcherType.BuyRune, OnBuyRune);
        EventDispatcher.AddEventListener<int>(EventDispatcherType.SellRune, OnSellRune);
    }
    private void OnDisable()
    {
        EventDispatcher.RemoveEventListener<BeastDomain>(EventDispatcherType.BeastGameWin, OnBeastGameWin);
        EventDispatcher.RemoveEventListener<int>(EventDispatcherType.BuyRune, OnBuyRune);
        EventDispatcher.RemoveEventListener<int>(EventDispatcherType.SellRune, OnSellRune);
    }

    public void OnBuyRune(int id)
    {
        Add(id);
    }
    public void OnSellRune(int id)
    {
        Remove(id);
    }
    public void Add(int id)
    {
        runeBaseEntity entity = ExcelData.Instance.RuneExcel.runeBaseList.Find(r => r.id == id);
        if(entity == null ) return;
        Rune rune = new Rune();
        rune.SetData(entity);
        m_runeList.Add(rune);

        rune.Start();
    }

    public void Remove(int id)
    {
        Rune rune = m_runeList.Find(r => r.runeBaseEntity.id == id);
        if (rune == null) return;
        rune.Quit();
        m_runeList.Remove(rune);
    }

    private void OnBeastGameWin(BeastDomain domain)
    {
        //BeastDomainBuilding bdb = new BeastDomainBuilding();
        //bdb.InstanceID = domain.GetInstanceID();
        //bdb.winTime = Time.time;
        //bdb.buildings.AddRange(domain.spawnScript.SpawnedBuildings);

        for (int i = 0; i < m_runeList.Count; i++)
        {
            //挑战完成后的野兽区域对应的建筑
            if (m_runeList[i].TargetType == 15)
            {
                m_runeList[i].runeTarget.listTarget.AddRange( domain.spawnScript.SpawnedBuildings);
            }
        }
    }

    public bool isGhostCanSelectUnit { get => m_runeList.Exists(rune => rune.runeBaseEntity.effectType == 9); }

    //野兽区域额外时间
    public int BeastDomainAdditionalTime
    {
        get
        {
            //12 表示 野兽区域挑战时间改变
            List<Rune> list = m_runeList.FindAll(rune => rune.runeBaseEntity.effectType == 12);
            if (list.Count == 0) return 0;
            return list.Sum(rune => int.Parse(rune.runeBaseEntity.effectParam1));

        }
    }
    //每个回合额外产生等级* x的金币
    public int AdditionalGoldPerTurn(int level)
    {

        //13 每个回合额外产生等级* x的金币
        List<Rune> list = m_runeList.FindAll(rune => rune.runeBaseEntity.effectType == 13);
        if (list.Count == 0) return 0;
        return list.Sum(rune => level * int.Parse(rune.runeBaseEntity.effectParam1));

    }


    /// <summary>
    /// 限定时间内完成回合挑战结算时额外获得金币
    /// </summary>
    /// <param name="fightTime">战斗时长</param>
    /// <returns></returns>
    public int getTimedTurnChallengeGold(float fightTime)
    {
        //effectParam1
        //      时间限制，单位：s
        //effectParam2
        //      额外获得的金币数量
        List<Rune> list = m_runeList.FindAll(rune => rune.runeBaseEntity.effectType == 14);
        int total = 0;
        for (int i = 0; i < list.Count; i++)
        {
            if (fightTime > int.Parse(list[i].runeBaseEntity.effectParam1))
                continue;
            total += int.Parse(list[i].runeBaseEntity.effectParam2);
        }
        return total;

    }
    //每过一回合，永久改变属性
    public void PermanentlyChangeAttributesEveryTurn()
    {
        List<Rune> listRune = m_runeList.FindAll(rune => rune.runeBaseEntity.effectType == 16);
        if (listRune.Count == 0) return;

        AttributeEnum attribute;
        float coefficient;
        for (int i = 0; i < listRune.Count; i++)
        {
            attribute = (AttributeEnum)int.Parse(listRune[i].runeBaseEntity.effectParam1);
            coefficient = float.Parse(listRune[i].runeBaseEntity.effectParam2);

            List<GameEntity> listTarget = listRune[i].runeTarget.getTarget();
            if (listTarget == null) continue;
            for (int n = 0; n < listTarget.Count; n++)
            {
                float original = listTarget[n].GetAttributeOriginalValue((AttributeEnum)attribute);
                listTarget[n].SetAttributeOriginalValue((AttributeEnum)attribute, original * (1 + coefficient));
            }
        }
    }

    public bool AlwaySprint { get=> m_runeList.Exists(rune => rune.runeBaseEntity.effectType == 18);}

    public void NextTurn()
    {
        PermanentlyChangeAttributesEveryTurn();

        for( int i = 0; i < m_runeList.Count; i++ )
        {
            m_runeList[i].NextTurn();
        }


        for (int i = m_runeList.Count - 1; i >= 0; i--)
        {
            if (m_runeList[i].isQuit)
                m_runeList.Remove(m_runeList[i]);
        }
    }

    public void Update()
    {
        for (int i = 0; i < m_runeList.Count; i++)
        {
            m_runeList[i].Update();
        }

    }
}

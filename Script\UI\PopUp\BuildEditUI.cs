using System.Collections.Generic;
using UnityEngine;
using System;
using Excel.monster;

[Serializable]
public class EnumToTabPrefab 
{
    public BuildEditUI.TabItemType type;
    public GameObject prefab;
}

public class BuildEditUI : MonoBehaviour
{
    // 单例
    public static BuildEditUI instance;
    public enum TabItemType
    {
        BuildOrUpgrade, // 升级或建造
        Gem, // 宝石
        FrameLand, // 农田
        Destroy // 拆除
    }
    
    [SerializeField] private Transform TabContainer; // Tab按钮的容器
    [SerializeField] private Transform TabContentContainer; // Tab内容的容器
    [SerializeField] private Sprite DestroyIcon; // 拆除的图标
    [SerializeField] private TabManager tabManager; // tab组件
    [SerializeField] private BuildEditTabItem TabPrefab; // Tab按钮的预制体
    [SerializeField] private List<EnumToTabPrefab> TabContentPrefab; // Tab内容的预制体
    [SerializeField] private UIHorizontalLayout TabUILayout; // tab的布局组件
    [SerializeField] private List<GameObject> QE;
    private BuildingBase buildingBase; // 当前建筑
    public BuildingBase EditBuild {
        get {
            return buildingBase;
        }
    }
    private GemGoods selectItem;
    private void Awake() {
        instance = this;
    }

    private void OnEnable() {
        // 避免开始战斗了不关闭界面问题
        EventDispatcher.AddEventListener(EventDispatcherType.StartFight, hide);
        EventDispatcher.AddEventListener<bool>(EventDispatcherType.ShopChange, closeView);
    }
    private void OnDisable() {
        EventDispatcher.RemoveEventListener(EventDispatcherType.StartFight, hide);
        EventDispatcher.RemoveEventListener<bool>(EventDispatcherType.ShopChange, closeView);
    }
    public void SetData(BuildingBase val)
    {
        if ( val == null) return;
        buildingBase = val;
        // FarmLand 建筑的额外展示逻辑
        if(val is FarmLand) {
            CreateCropTab();
            return;
        }
        CreateTab();
    }
    public void closeView(bool state)
    {
        if(state)
        {
            buildingBase.EditOver = true;
            hide();
        } 
    }
    public void hide() 
    {
        selectItem = null;
        tabManager.clear();
        buildingBase = null;
        gameObject.SetActive(false);
    }
    public void reload()
    {
        tabManager.clear();
        SetData(buildingBase);
    }
    // 创建建筑界面
    private void CreateBuildingTab() 
    {
        TabItemType type = TabItemType.BuildOrUpgrade;
        Sprite buildIcon = Resources.Load<Sprite>(buildingBase.ArchitectureEntity.icon);
        GameObject tab = CreateItem(buildIcon).gameObject;
        // 创建Content
        GameObject content = CreateContentItem(type);
        BuildOrUpPanel script = content.GetComponent<BuildOrUpPanel>();
        
        script.SetBuildingData(buildingBase);
        script.onBuildingFinishing(finishEdit);
        // 添加索引
        tabManager.AddItem(tab, content, TabItemType.BuildOrUpgrade);
    }
    // 创建拆除界面
    private void CreateDestroyTab() {
        TabItemType type = TabItemType.Destroy;
        GameObject tab = CreateItem(DestroyIcon).gameObject;
        GameObject content = CreateContentItem(type);
        BuildDestroyPanel script = content.GetComponent<BuildDestroyPanel>();
        script.SetBuildingData(buildingBase);
        script.onBuildingFinishing(finishEdit);
        // 添加索引
        tabManager.AddItem(tab, content, TabItemType.Destroy);
    }
    // 创建种植界面
    void CreateFrameLandTab(CropIdToCropBindFarmId data)
    {
        TabItemType type = TabItemType.FrameLand;
        // 灵植实体类
        monsterBaseEntity cropEntity = ExcelData.Instance.GetMonster(data.CropId);
        
        BuildEditTabItem Item = Instantiate(TabPrefab, TabContainer);
        GameObject content = CreateContentItem(type);
        
        // tab
        Item.CurrentBorder = 1;
        Item.Icon = Resources.Load<Sprite>(cropEntity.icon);
        // content
        EditFarmLandPanel script = content.GetComponent<EditFarmLandPanel>();
        FarmLand farmLand = (FarmLand)buildingBase;
        script.SetData(farmLand, data);
        script.onFinish(finishEdit);
        // 添加索引
        tabManager.AddItem(Item.gameObject, content, TabItemType.FrameLand);
    }
    /// <summary>
    /// 创建宝石和精粹页面
    /// </summary>
    /// <param name="type">tab的类型</param>
    /// <param name="val">数据源</param>
    /// <param name="inserted">是否嵌入</param>
    private void CreateItemByData(GoodsType type, BaseGoodsData val, bool inserted = false)
    {
        if(val == null) return;
        BuildEditTabItem Item = Instantiate(TabPrefab, TabContainer);
        GameObject content = CreateContentItem(TabItemType.Gem);
        int index = tabManager.AddItem(Item.gameObject, content, TabItemType.Gem);
        // tab 数据
        Item.IsTab = true; // tab
        Item.Index = index; // 索引
        Item.IsInserted = inserted; // 是否嵌入了
        Item.SetData(val);
        // 面板数据
        // 创建Content
        BuildInsertedUI script = content.GetComponent<BuildInsertedUI>();
        script.IsInserted = inserted;
        script.SetBuildingData(buildingBase, val);
        script.onRefresh(resIndex => {
            selectItem = resIndex; // 设置默认
            reload();
        });
    }
    // 执行结束关闭界面
    void finishEdit(BuildingBase val) 
    {
        if (val != null && val == buildingBase) {
            gameObject.SetActive(false);
        }
    }
    private void CreateCropTab()
    {
        if(buildingBase == null) return;
        tabManager.clear();
        // 初始化UI选项
        FarmLand farmLand = buildingBase as FarmLand;
        List<CropIdToCropBindFarmId> crops = farmLand.parent.AllCropList;
        if (crops == null) return;
        for(int i = 0; i < crops.Count; i++) 
        {
            CreateFrameLandTab(crops[i]);
        }
        // 启动默认渲染
        tabManager.StartRender();
        // 如果已种植 则取消QE切换
        if(farmLand.currentCrop != null) 
        {
            tabManager.DisableCheck = true;
            // 取消展示
            if(QE == null || QE.Count <= 0) return;
            for(int i = 0; i < QE.Count; i++)
            {
                QE[i].SetActive(false);
            }
        }
    }
    private void CreateTab()
    {
        if(buildingBase == null) return;
        tabManager.clear();
        // 建造/升级 tab
        CreateBuildingTab();
        // 当前建筑大于0级才支持展示宝石和拆除
        if(buildingBase.CurrentLv > 0) {
            // 宝石精华
            CreateTabItems();
            // 拆除
            if(buildingBase.ArchitectureEntity.remove == 1)
            {
                CreateDestroyTab();
            }
            tabManager.onChange(TabChange);
        }
        int startIndex = 0;
        if(selectItem != null)
        {
            startIndex = FindTabIndex(selectItem);
        }
        tabManager.StartRender(startIndex);
    }

    private int FindTabIndex(GemGoods data)
    {
        int res = 0;
        if(tabManager.TabDatas.Count > 0)
        {
            var filter = tabManager.TabDatas.FindAll(x=> x.type == TabItemType.Gem);
            var findItem = filter.Find(x => {
                var item = x.panel.GetComponent<BuildInsertedUI>().GoodsItem;
                return data.id == item.id;
            });
            if(findItem != null)
            {
                res = findItem.index;
            }
        }
        return res;
    }

    private void CreateTabItems()
    {
        // 非空判断
        var build = buildingBase;
        if(build == null) return;
        var config = build.editUiShowAttribute;
        if(!config.showEssenceSlot && !config.showGemSlot) return;
        GoodsType type = config.showGemSlot ? GoodsType.Gems : GoodsType.Essence;
        // 已嵌入数据初始化
        var dataInserted = build.SlotList;
        if(dataInserted != null && dataInserted.Count > 0) 
        {
            for (int i = 0; i < dataInserted.Count; i++)
            {
                CreateItemByData(type, dataInserted[i], true);
            }
        }
        // 背包数据初始化
        var store = InventoryManager.Other;
        // 获取建筑可嵌入类型的数据
        var storeData = store?.AllItems?.FindAll(x=> x.goodsType == (int)type);
        if(storeData != null && storeData.Count > 0) 
        {
            for (int i = 0; i < storeData.Count; i++)
            {
                CreateItemByData(type, storeData[i]);
            }
        }
    }
    // tab切换
    private void TabChange(int index)
    { 
        var data = tabManager.TabDatas;
        for(int i = 0; i < data.Count; i++) 
        {
            var script = data[i].button.GetComponent<BuildEditTabItem>();
            script.Active = i == index;
        }
    }

    // 创建升级或建造的tab
    private BuildEditTabItem CreateItem(Sprite icon)
    {
        BuildEditTabItem Item = Instantiate(TabPrefab, TabContainer);
        Item.CurrentBorder = 1;
        Item.Icon = icon;
        Item.IsTab = true; // tab
        return Item;
    }
    private GameObject CreateContentItem(TabItemType type)
    {
        GameObject prefab = TabContentPrefab.Find(x => x.type == type).prefab;;
        GameObject go = Instantiate(prefab, TabContentContainer);
        go.SetActive(false);
        return go;
    }
}

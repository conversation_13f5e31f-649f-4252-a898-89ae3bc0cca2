using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Excel.map;
using System.Linq;

[System.Serializable]
public class AreaData
{
    public List<SpawnRound> roundDataList;

    public AreaData()
    {
        roundDataList = new List<SpawnRound>();
    }
    public void Add(SpawnRound sr)
    {
        roundDataList.Add(sr);
    }
}

[System.Serializable]
public class AreaSpawner
{
    public int currentRoundIndex = 0;  // 当前波次索引
    public float elapsedTime = 0f;  // 控制波次间隔的时间
    public float groupTimeInterval = 0f;  // 用于控制组内怪物生成的时间间隔
    public float monsterGenerationTime = 0f;  // 用于控制怪物生成的时间
    public List<GroupMonster> currentGroupMonsters = new List<GroupMonster>();  // 当前选中的怪物组
    public Dictionary<string, int> enemyIdCounts = new Dictionary<string, int>();  // 记录每个 enemyId 已经生成的数量
    public int rangeCreateCount = 0;  // 当前波次怪物生成数量
    public int currentGenerationCount = 0;  // 当前组怪物生成数量
    public bool isGroupCompleted = false;  // 是否完成当前组的生成

    // 添加 enemyId 到 enemyIdCounts 中
    public void addEnemyIdCounts(string enemyId)
    {
        if (enemyIdCounts.ContainsKey(enemyId))
        {
            enemyIdCounts[enemyId]++;
        }
        else
        {
            enemyIdCounts[enemyId] = 1;
        }
    }
}

public class SpawnGroup : MonoBehaviour
{
    private mapExcel m_mapExcel;
    private List<roundMonsterEntity> m_mapData;
    public int MapId;
    private int currentRound = 0; // 当前回合数
    // 区域数据
    Dictionary<int, AreaData> areaData;

    private bool isStart = false; // 是否开始召唤

    // 每个区域的状态
    private Dictionary<int, AreaSpawner> areaSpawners;

    [SerializeField]
    private List<SpawnItem> m_spawnItems = new List<SpawnItem>();
    public List<SpawnItem> SpawnItems { get => m_spawnItems; }

    private int totalMonster = 0; // 总共需要生成怪物数量
    private int createedNum = 0; // 已经生成怪物数量
    // 
    GameObject m_indicator;

    private bool loopRound = false; // 是否循环

    private Material m_enemyMaterial;

    public GameObject nextRoundInfoUI;

    [SerializeField] MainRoundShop shopScript;
    // 最大回合数
    private int maxRound
    {
        get
        {
            return m_mapData.Count;
        }
    }
    // 当前回合数 对外的
    public int CurrentRound
    {
        get
        {
            return currentRound;
        }
        set
        {
            currentRound = value;
            shopScript.CurrentRound = value;
            CreateSpawn();
            if(CurrentRound >= maxRound - 1) {
                EventDispatcher.TriggerEvent(EventDispatcherType.LastRound);
            }
        }
    }
    void Awake()
    {
        m_mapExcel = ExcelData.Instance.MapExcel; //Resources.Load<mapExcel>("Excel/mapExcel");
        m_indicator = Resources.Load("Prefab/IndicatorItem") as GameObject;
        m_enemyMaterial = Resources.Load("Mtr/red") as Material;
    }
    // Start is called before the first frame update
    void Start()
    {
        if (MapId != 0)
        {
            m_mapData = getSpawnList(MapId);
            shopScript.MaxRound = maxRound;
        }
        //m_spawnItems = GetComponentsInChildren<SpawnItem>().ToList();
        m_spawnItems.RemoveAll( item => item == null );
        EventDispatcher.AddEventListener(EventDispatcherType.NextTurn, NextRound);
        EventDispatcher.AddEventListener(EventDispatcherType.StartFight, OnStartFight);

        CreateSpawn();
    }
    // 下一回合
    public void NextRound()
    {
        if(loopRound) return;

        isStart = false;
        if (CurrentRound >= maxRound - 1)
        {
            return;
        }
        CurrentRound++;
    }

    public void OnStartFight()
    {
        isStart = true;
    }
    // Update is called once per frame
    void Update()
    {
        if (isStart || loopRound)
        {
            HandleMonsterSpawning();
        }
    }
    // 随机生成一个范围内的整数
    public int RandomInRange(int f, int n)
    {
        return UnityEngine.Random.Range(f, n + 1);
    }
    // 获取List<GroupMonster> 内未完成刷新的怪物
    private List<GroupMonster> getGroupMonsterIsNotCreate(AreaSpawner areaSpawner, List<GroupMonster> val)
    {
        List<GroupMonster> res = new List<GroupMonster>();
        int length = val.Count;
        for (int i = 0; i < length; i++)
        {
            string key = val[i].getEnemyId().ToString() + val[i].getRate().ToString();
            // 没有生成过
            if (!areaSpawner.enemyIdCounts.ContainsKey(key))
            {
                res.Add(val[i]);
            }
            else
            {
                // 已经生成过未达到上限
                if (areaSpawner.enemyIdCounts[key] < val[i].getCount())
                {
                    // 继续返回
                    res.Add(val[i]);
                }
            }
        }
        return res;
    }
    /*
    * 获取GroupMonster列表中怪物总数
    * @param val: GroupMonster列表
    * @return: 怪物总数
    */
    private int getCountByGroupMonster(List<GroupMonster> val)
    {
        int res = 0;
        int length = val.Count;
        for (int i = 0; i < length; i++)
        {
            res += val[i].getCount();
        }
        return res;
    }
    /*
    * 获取GroupManager列表中怪物总数
    * @param val: GroupManager列表
    * @return: 怪物总数
    */
    private int getMonsterGroupTotal(GroupManager val)
    {
        int res = 0;
        if (val.getGroupMonsters1() != null)
        {
            res += getCountByGroupMonster(val.getGroupMonsters1());
        }
        if (val.getGroupMonsters2() != null)
        {
            res += getCountByGroupMonster(val.getGroupMonsters2());
        }
        if (val.getGroupMonsters3() != null)
        {
            res += getCountByGroupMonster(val.getGroupMonsters3());
        }
        if (val.getGroupMonsters3() != null)
        {
            res += getCountByGroupMonster(val.getGroupMonsters4());
        }
        return res;
    }
    /*
    *  根据数据召唤怪物
    */
    private void HandleMonsterSpawning()
    {
        // 遍历 区域数据
        foreach (int key in areaData.Keys)
        {

            AreaData area = areaData[key];
            AreaSpawner areaSpawner = areaSpawners[key];
            // 当前波的数据
            if (areaSpawner.currentRoundIndex < area.roundDataList.Count)
            {
                SpawnRound round = area.roundDataList[areaSpawner.currentRoundIndex];
                // 累积波次停顿时间
                areaSpawner.elapsedTime += Time.deltaTime;
                // 如果累计的时间超过当前波的时间间隔，则进行分组召唤
                if (areaSpawner.elapsedTime >= round.getRoundTime() / 1000f)
                {
                    GroupManager group = round.getGroups();
                    // 选择怪物组并召唤
                    StartNextGroup(areaSpawner, group, key);
                    // 当前波次召唤完成后开启下一波
                    if (areaSpawner.rangeCreateCount >= group.getAllMonstersCount())
                    {
                        Debug.Log(key + ": 开始下一波，上一波总计召唤: " + group.getAllMonstersCount() + "个");
                        areaSpawner.rangeCreateCount = 0; // 重置计数
                        areaSpawner.elapsedTime = 0f; // 重置时间
                        areaSpawner.enemyIdCounts.Clear();
                        areaSpawner.currentRoundIndex++; // 下一波
                    }
                }
            }

        }
    }

    /***
    * 按分组召唤怪物
    * @param areaSpawner: 区域状态
    * @param group: 怪物组
    * @param areaId: 区域ID
    */
    void StartNextGroup(AreaSpawner areaSpawner, GroupManager group, int areaId)
    {
        areaSpawner.groupTimeInterval += Time.deltaTime;  // 当前组生成计数器
        float interval = group.getTimeInterval();
        // 满足组刷新时间开始刷新怪物
        if (interval == -1 || (interval != -1 && areaSpawner.groupTimeInterval >= interval / 1000f))
        {
            if (areaSpawner.currentGroupMonsters.Count == 0)
            {
                // 获取当前波次的怪物组
                List<GroupMonster> allIndices = new List<GroupMonster>();
                if (group.getGroupMonsters1() != null) allIndices.AddRange(group.getGroupMonsters1());
                if (group.getGroupMonsters2() != null) allIndices.AddRange(group.getGroupMonsters2());
                if (group.getGroupMonsters3() != null) allIndices.AddRange(group.getGroupMonsters3());
                if (group.getGroupMonsters4() != null) allIndices.AddRange(group.getGroupMonsters4());
                // 筛选出所有未完成创建的怪物
                List<GroupMonster> notCreate = getGroupMonsterIsNotCreate(areaSpawner, allIndices);
                if (notCreate.Count > 0)
                {
                    // 需要创建的怪物组
                    List<GroupMonster> selectedGroups = new List<GroupMonster>();

                    for (int i = 0; i < group.getGroupNum(); i++)
                    {
                        int current = i;
                        if (group.getGetMethod() == 1)
                        {
                            current = i % notCreate.Count;
                        }
                        else
                        {
                            current = RandomInRange(0, notCreate.Count - 1);
                        }
                        selectedGroups.Add(notCreate[current]);
                    }
                    areaSpawner.currentGroupMonsters = selectedGroups;
                }
            }
            // 创建怪物
            StartGeneratingMonsters(areaSpawner, group, areaId);
            // 刷新完成
            if (areaSpawner.currentGenerationCount >= group.getGroupNum() * group.getGenerationNum())
            {
                //Debug.Log(areaId + ": 刷新分组完成, 上一组召唤:" + areaSpawner.currentGroupMonsters.Count + "个");
                areaSpawner.currentGroupMonsters.Clear();
                areaSpawner.currentGenerationCount = 0;
                areaSpawner.groupTimeInterval = 0;
                areaSpawner.monsterGenerationTime = 0;
            }
        }
    }

    /// <summary>
    /// 逐步生成怪物
    /// </summary>
    /// <param name="areaSpawner">区域状态</param>
    /// <param name="group">怪物组</param>
    /// <param name="areaId">区域ID</param>
    void StartGeneratingMonsters(AreaSpawner areaSpawner, GroupManager group, int areaId)
    {
        areaSpawner.monsterGenerationTime += Time.deltaTime;
        List<GroupMonster> data = areaSpawner.currentGroupMonsters;
        // 当前组有怪物创建
        if (data.Count > 0)
        {
            for (int i = 0; i < data.Count; i++)
            {
                GroupMonster monsterData = data[i];
                // 达到创建时间标准
                if (areaSpawner.monsterGenerationTime >= monsterData.getRate() / 1000f)
                {
                    // 创建怪物
                    SpawnMonster(monsterData, areaId);
                    // 记录enemyIdCounts 
                    // 注意：为了避免出现id 相同召唤频率不同的情况，key = enemyId + rate
                    string key = monsterData.getEnemyId().ToString() + monsterData.rate.ToString();
                    areaSpawner.addEnemyIdCounts(key);
                    // 记录总数
                    areaSpawner.rangeCreateCount++;
                    // 记录组创建数量
                    areaSpawner.currentGenerationCount++;
                    
                }
            }
        }
    }

    // 召唤单个怪物
    void SpawnMonster(GroupMonster monsterData, int areaId)
    {
        int enemyId = monsterData.getEnemyId();
        if (enemyId != 0 && areaId != 0)
        {
            // 获取所有子节点
            SpawnItem targetItem = m_spawnItems.Find(item => item.positionID == areaId);
            if (targetItem != null)
            {
                float radius = targetItem.Radius;
                // 坐标 targetItem的position，在 radius 范围内随机生成一个坐标
                Vector3 position = targetItem.transform.position + UnityEngine.Random.insideUnitSphere * radius;
                position.y = targetItem.transform.position.y; // 确保生成在地面上
        
                // 创建怪物
                Monster monster = GameMain.Instance.CreateMonster(enemyId, CampEnum.Invader, position);
                // 创建箭头Ui
                GameObject indicator = GameObject.Instantiate(m_indicator) as GameObject;
                MonsterIndicator indicatorScript = indicator.GetComponent<MonsterIndicator>();
                indicatorScript.setIndicatorTarget(monster.transform);
                Transform indicatorParent = GameObject.Find("IndicatorWrapper").transform;
                indicator.transform.SetParent(indicatorParent);
                if (m_enemyMaterial != null)
                {
                    // 查找怪物下miniMapSphere
                    GameObject miniMapSphere = monster.transform.Find("miniMapSphere").gameObject;
                    // 获取miniMapSphere下的material
                    Renderer sphereRender = miniMapSphere.GetComponent<Renderer>();
                    // 设置新的material
                    sphereRender.material = m_enemyMaterial;
                }
                // 统计生成的总数
                createedNum ++;
                //Debug.Log($"生成怪物第: {createedNum}/{totalMonster}");
                // 如果全部创建完成
                if(createedNum >= totalMonster) 
                {
                    if(loopRound) {
                        CreateSpawn();
                        return; 
                    }
                    isStart = false;
                    EventDispatcher.TriggerEvent(EventDispatcherType.SpawnComplete);
                }
                if (monster != null)
                {
                    //Debug.Log($"生成怪物: {monster.name} 在位置: {position}");
                }
            }
        }
    }

    public Dictionary<int, AreaData> getRoundMapData(string monsterParam) {
        Dictionary<int, AreaData> mapData = new Dictionary<int, AreaData>();
        List<monsterGenerationEntity> infos = getMonsterGenerationList(monsterParam.Split('|'));
        // 遍历回合信息
        foreach (monsterGenerationEntity info in infos)
        {
            // 获取位置
            int position = info.position;
            // rate
            string[] rateSplite = new string[] { "0", "0", "0", "0" };
            var rates = info.rate == "-1" ? rateSplite : info.rate.Split(',');
            // 怪物列表
            List<GroupMonster> lgm1 = info.monsterGroup1 == "" ? null : getGroupItem(info.monsterGroup1, rates.Length > 0 ? rates[0] : "0");
            List<GroupMonster> lgm2 = info.monsterGroup2 == "" ? null : getGroupItem(info.monsterGroup2, rates.Length > 1 ? rates[1] : "0");
            List<GroupMonster> lgm3 = info.monsterGroup3 == "" ? null : getGroupItem(info.monsterGroup3, rates.Length > 2 ? rates[2] : "0");
            List<GroupMonster> lgm4 = info.monsterGroup4 == "" ? null : getGroupItem(info.monsterGroup4, rates.Length > 4 ? rates[3] : "0");
            // 组管理器
            GroupManager gm = new GroupManager(
                info.groupNum,
                info.generationNum,
                int.Parse(info.timeInterval),
                info.order
            );
            int allMonstersCount;
            if (lgm1 != null)
            {
                gm.setGroupMonsters1(lgm1);
            }
            if (lgm2 != null)
            {
                gm.setGroupMonsters2(lgm2);
            }
            if (lgm3 != null)
            {
                gm.setGroupMonsters3(lgm3);
            }
            if (lgm4 != null)
            {
                gm.setGroupMonsters4(lgm4);
            }
            // 获取波次内所有怪物总数
            allMonstersCount = getMonsterGroupTotal(gm);
            gm.setAllMonstersCount(allMonstersCount);
            // 统计怪物总数
            totalMonster += allMonstersCount;
            // 波次信息
            SpawnRound sr = new SpawnRound(info.roundTime, gm);
            // 添加到字典
            if (isExistPosition(position, mapData))
            {
                // 如果存在区域，将波次信息添加进去
                mapData[position].Add(sr);
            }
            else
            {
                // 如果不存在区域，新建一个list，将波次信息添加进去
                AreaData srList = new AreaData();
                srList.Add(sr);
                mapData.Add(position, srList);
            }
        }
        return mapData;
    }

    private void CreateSpawn()
    {
        // 当前回合信息
        roundMonsterEntity nowSpawnData = m_mapData[CurrentRound];
        // 创建区域生成器
        areaSpawners = new Dictionary<int, AreaSpawner>();
        // 初始化生成总数
        createedNum = 0;
        totalMonster = 0;
        // 获取当前回合的怪物信息
        string monsterParam = nowSpawnData.monsterParam;
        areaData = getRoundMapData(monsterParam);
        // 初始化每个区域的状态
        foreach (int key in areaData.Keys)
        {
            areaSpawners[key] = new AreaSpawner();
        }
        // Debug.Log("怪物信息初始化完成，共计出生点: " + areaSpawners.Count + "个");
        // Debug.Log("怪物信息初始化完成，共计怪物: " + totalMonster + "个");
        // todo 创建怪物展示
        if(nextRoundInfoUI != null) {
            // Debug.Log("创建怪物展示");
            NextRoundInfoUI nextInfoUiScript = nextRoundInfoUI.GetComponent<NextRoundInfoUI>();
            nextInfoUiScript.SetData(areaData, SpawnItems);
        }
        if(shopScript != null) {
            shopScript.SetNextMonsterData(areaData);
        }
    }

    // 获取组怪物信息 
    private List<GroupMonster> getGroupItem(string val, string rate)
    {
        List<GroupMonster> list = new List<GroupMonster>();
        var groupSplit = val.Split('|');
        foreach (var item in groupSplit)
        {
            var groupItem = item.Split(':');
            GroupMonster gMonster = new GroupMonster();
            gMonster.setEnemyId(int.Parse(groupItem[0]));
            gMonster.setCount(int.Parse(groupItem[1]));
            gMonster.setRate(float.Parse(rate));
            list.Add(gMonster);
        }
        return list;
    }
    // 检索回合内是否存在position
    private bool isExistPosition(int position, Dictionary<int, AreaData> infoMap)
    {
        if (infoMap.ContainsKey(position))
        {
            return true;
        }
        return false;
    }

    // 获取当前地图的怪物列表
    List<roundMonsterEntity> getSpawnList(int id)
    {
        return m_mapExcel.roundMonsterList.FindAll(element => element.mapId == id);
    }

    private List<monsterGenerationEntity> getMonsterGenerationList(string[] infoIds)
    {
        List<monsterGenerationEntity> result = new List<monsterGenerationEntity>();
        for (int i = 0; i < infoIds.Length; i++)
        {
            monsterGenerationEntity infoData = m_mapExcel.monsterGenerationList.Find(element => element.id == int.Parse(infoIds[i]));
            if (infoData != null)
            {
                result.Add(infoData);
            }
        }
        return result;
    }

    private void repeatRound(bool val) {
        Debug.Log(val ? "开启重复回合" : "关闭重复回合");
        loopRound = val;
    }

    //private void OnGUI()
    //{
    //    // 绘制按钮，点击后重复当前回合
    //    if (GUI.Button(new Rect(10, 10, 100, 50), "Loop Round"))
    //    {
    //        repeatRound(true);
    //    }
    //    // 绘制按钮，点击后结束重复回合
    //    if (GUI.Button(new Rect(120, 10, 100, 50), "End Loop"))
    //    {
    //        repeatRound(false);
    //    }
    //}
}

using Excel.empower;
using Excel.skill;
using Excel.unit;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;


/// <summary>
/// 建筑类型
/// </summary>
public enum BuildingTypeEnum
{
    //主城
    Main    = 1,
    //经济类型建筑
    Economy = 2,
    //防御类型建筑
    Defense = 3,
    //单位类型建筑
    Unit    = 4,
    //城墙
    WALL    = 5,
}

public enum BuildStatusEnum
{
    Building,
}

public enum BuildingEffectType
{
    Pasture = 1,//牧场
    Farm = 2,//农场
    Mine = 3,//矿场
    FishingGround = 4,//渔场
    Wall = 5,//墙
    MainBase = 6,//主城
    PetHouse = 7,//灵兽所
    Barracks = 8,//兵营
    HolyPillar = 9,//圣柱
    Smelter = 10,//冶炼所
    ArcaneLaboratory = 11, //魔法研究所
    MagicalFormation = 12, //阵法
    ArrowTower = 101,
    EnergyTower = 102,
}
public enum BuildIconType
{
    Jade,
    Shovel,
}

/// <summary>
/// 建筑基类
/// </summary>
public class BuildingBase : GameEntity
{
    public class EditUiShowAttribute
    {
        public bool showHp = true;  // 血量展示
        public bool showAttcack = false; // 攻击展示
        public bool showSummon = false; // 召唤展示
        public bool showProfit = false; // 收益展示
        public bool showAsideAttribute = false; // 侧边属性
        public bool showGemSlot = false; // 插槽展示
        public bool showEssenceSlot = false; // 插槽展示
    }
    public int IdGroup { get => m_id; set => m_id = value; }


    [SerializeField]
    protected SpawnPoint[] m_spawnPoint;
    public SpawnPoint[] SpawnPoint { get => m_spawnPoint; }

    [SerializeField]
    protected GameObject[] m_BuildingLv;


    protected BuildingTypeEnum m_buildType;
    public BuildingTypeEnum buildType { get => m_buildType; set => m_buildType = value; }

    /// <summary>
    /// 建筑产生的灵玉
    /// </summary>
    protected int m_buildingJadeCount;

    [SerializeField]
    protected int m_mainbaseLevelAppear;
    /// <summary>
    /// 在主城某个等级出现
    /// </summary>
    public int MainbaseLevelAppear { get => m_mainbaseLevelAppear; set => m_mainbaseLevelAppear = value; }


    protected int m_cost;
    public int Cost { get => m_cost; }

    protected architectureBasicEffectEntity m_Effect;
    public architectureBasicEffectEntity Effect { get => m_Effect; }

    protected architectureBasicEffectEntity m_nextEffect;
    public architectureBasicEffectEntity nextEffect { get => m_nextEffect; }

    protected List<Elf> m_candidateElfList = new List<Elf>();
    /// <summary>
    /// 可以共鸣当前建筑的候选灵兽
    /// </summary>
    public List<Elf> candidateElfList { get => m_candidateElfList; set => m_candidateElfList = value; }

    protected Elf m_bindElf;
    public Elf bindElf { get => m_bindElf; set => m_bindElf = value; }

    [SerializeField]
    public Transform m_topTip;
    public Transform TopTip { get => m_topTip; }
    [SerializeField]
    protected int m_currentLv = 0;
    public int CurrentLv { get => m_currentLv; set => m_currentLv = value; }

    public virtual int nextLv { get => m_currentLv + 1; }


    protected architectureLvEntity m_architectureLvEntity;
    public architectureLvEntity ArchitectureLvEntity { get => m_architectureLvEntity; }

    protected architectureLvEntity m_nextLvArchitectureLvEntity;
    public architectureLvEntity NextLvArchitectureLvEntity { get => m_nextLvArchitectureLvEntity; }

    // protected EventDispatcherType m_OpenBuildUIEvent = EventDispatcherType.OpenCommonBuildUI;
    // public EventDispatcherType OpenBuildUIEvent { get => m_OpenBuildUIEvent; }

    // protected EventDispatcherType m_OpenUpgradeUIEvent = EventDispatcherType.OpenCommonUpgradeUI;
    // public EventDispatcherType OpenUpgradeUIEvent { get => m_OpenUpgradeUIEvent; }

    public Transform m_mark;

    protected NavMeshObstacle m_navMeshObstacle;
    public NavMeshObstacle navMeshObstacle { get => m_navMeshObstacle; }

    [SerializeField]
    protected GameObject m_destroyModel;

    //public bool m_isDestroyed = false;
    protected string m_introduceTxt;
    protected string m_buildNameTxt;
    protected string m_lvText;
    protected string m_hpTxt;
    private int m_stempCurrentLv = -1;
    public GameObject m_markArrow;

    /// <summary>
    /// 用于建设阶段检测玩家是否靠近
    /// </summary>
    public Collider m_colliderInBuildStage;
    /// <summary>
    /// 用于攻击检测
    /// </summary>
    protected BoxCollider m_boxColliderForAttackCheck;
    public BoxCollider boxColliderForAttackCheck { get => m_boxColliderForAttackCheck; }

    protected List<int> m_elementSkillList;

    // private bool m_showBuildEditUI = false;
    // 可交互参数
    protected architectureEntity m_architectureEntity;
    public architectureEntity ArchitectureEntity { get => m_architectureEntity; }
    // 嵌入的宝石
    protected List<GemGoods> m_Slot = new List<GemGoods>();
    public List<GemGoods> SlotList { get => m_Slot; }
    protected int m_maxSlotNum = 3; // 插槽数量
    public int MaxSlotNum { get => m_maxSlotNum; }
    protected int m_MainSlotNum = 1; // 主插槽数量
    public int MainSlotNum { get => m_MainSlotNum; }
    // 当前建筑的最大等级
    private int m_maxLv = 0;
    public int MaxLv { get => m_maxLv; }
    // 已花费的开销
    protected int m_costSpend = 0;
    public int CostSpend { get => m_costSpend; }

    protected bool m_editOver = false;
    public bool EditOver
    {
        get
        {
            return m_editOver;
        }
        set
        {
            m_editOver = value;
        }
    }

    public EditUiShowAttribute editUiShowAttribute = new EditUiShowAttribute();
    public List<int> skillIds = new List<int>();

    public override void Awake()
    {

        base.Awake();
        m_objectType = ObjectType.Building;
        m_camp = CampEnum.Ally;

        m_markArrow = SceneMain.Instance.MarkArrow;

        if (m_colliderInBuildStage == null)
            m_colliderInBuildStage = GetComponent<Collider>();
        m_colliderInBuildStage.enabled = false;

        if (m_boxColliderForAttackCheck == null)
            m_boxColliderForAttackCheck = GetComponent<BoxCollider>();
        m_boxColliderForAttackCheck.enabled = false;

        m_mark = transform.Find("Mark");
        if (m_mark != null)
            m_mark.gameObject.SetActive(false);

        EventDispatcher.AddEventListener(EventDispatcherType.NextTurn, OnTurnNext);
        EventDispatcher.AddEventListener(EventDispatcherType.ShowBuildingCostInSuperView, hideBuildEditUi);
    }
    public virtual void Start()
    {

    }

    public override void OnEnable()
    {
        if (m_BuildingLv != null)
        {
            for (int i = 0; i < m_BuildingLv.Length; i++)
            {
                if (m_BuildingLv[i] != null)
                    m_BuildingLv[i].SetActive(false);
            }
        }

        m_navMeshObstacle = GetComponent<NavMeshObstacle>();
        m_navMeshObstacle.enabled = false;
        base.OnEnable();
    }

    public override void OnDisable()
    {
        EventDispatcher.RemoveEventListener(EventDispatcherType.NextTurn, OnTurnNext);
        EventDispatcher.RemoveEventListener(EventDispatcherType.ShowBuildingCostInSuperView, hideBuildEditUi);
        base.OnDisable();
    }

    public override void Update()
    {
        if (isDeath)
        {
            OnDeath();
            return;
        }
        if (m_currentLv == 0 || m_isGhost) return;
        m_buffSystem.Update();
        m_skillEffectSystem.UpdateEffect();
        base.Update();

        if (GameMain.Instance.gameStage != GameStage.Fight) return;

        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (!m_skillList[i].IsCompleteSkillEffect && m_skillList[i].IsTriggerEffect)
            {
                m_skillList[i].UpdateEffect();
                if (m_skillList[i].IsCompleteSkillEffect)
                {
                    m_lastAttackTime = Time.time;
                }
            }
            else
            {
                if (!m_skillList[i].Enable) continue;
                m_skillList[i].CheckTrigger(this);
                m_skillList[i].Upate();
                if (m_skillList[i].isTrigger)
                {
                    GameEntity target = m_skillList[i].GetTarget();
                    if (target == null) continue;

                    if (Time.time - m_lastAttackTime < this.AttackSpeed)
                        return;
                    m_skillList[i].ApplyEffect();
                    m_skillList[i].IsCompleteSkillEffect = false;
                }
            }

        }
    }

    public void SetData()
    {
        BuildingShow();

        m_architectureLvEntity = ExcelData.Instance.UnitExcel.architectureLvList
                                        .Find(arch => arch.idGroup == IdGroup && arch.lv == m_currentLv);
        if (m_architectureLvEntity == null) return;
        // 下一级配置
        if (m_architectureLvEntity.cost >= 0)
        {
            m_nextLvArchitectureLvEntity = ExcelData.Instance.UnitExcel.architectureLvList.Find(arch => arch.idGroup == IdGroup && arch.lv == m_currentLv + 1);
        }
        else
        {
            m_nextLvArchitectureLvEntity = null;
        }

        m_maxLv = ExcelData.Instance.UnitExcel.architectureLvList.FindAll(arch => arch.idGroup == IdGroup).Count - 1;

        m_architectureEntity = ExcelData.Instance.UnitExcel.architectureList
                                        .Find(arch => arch.id == IdGroup);

        m_name = Language.GetText(m_architectureLvEntity.name);
        m_cost = m_architectureLvEntity.cost;
        this.Health       = m_architectureLvEntity.hp;
        this.AttackDamage = m_architectureLvEntity.atk;
        this.AttackRange  = m_architectureLvEntity.ar;
        this.AttackSpeed  = m_architectureLvEntity.asp;

        m_originalHealth       = m_architectureLvEntity.hp;
        m_originalAttackDamage = m_architectureLvEntity.atk;
        m_originalAttackRange  = m_architectureLvEntity.ar;
        m_originalAttackSpeed  = m_architectureLvEntity.asp;

        m_currentHealth = this.Health;

        m_buildType = (BuildingTypeEnum)m_architectureEntity.architectureType;

        m_buildingJadeCount = m_architectureLvEntity.produce;

        m_Effect = ExcelData.Instance.UnitExcel.architectureBasicEffectList.Find(arch => arch.id == m_architectureLvEntity.basicEffect);

        if (m_nextLvArchitectureLvEntity != null)
        {
            m_nextEffect = ExcelData.Instance.UnitExcel.architectureBasicEffectList.Find(arch => arch.id == m_nextLvArchitectureLvEntity.basicEffect);
        }

        resourcesShowEntity showEntity = ExcelData.Instance.UnitExcel.resourcesShowList
                                            .Find(show => show.architectureId == IdGroup && show.lv == m_currentLv);
        if (showEntity != null)
        {
            m_introduceTxt = Language.GetText(showEntity.txt);
            m_buildNameTxt = Language.GetText(showEntity.name);
            m_lvText = m_currentLv.ToString();

            m_introduceTxt = m_introduceTxt.Replace("\\n", "\n");
        }
        else
        {
            m_introduceTxt = "";
            m_buildNameTxt = "";
            m_lvText = "";
        }
        if (m_architectureLvEntity.skillEffect > 0)
        {
            AddSkill(m_architectureLvEntity.skillEffect);
        }
        m_normalSkill = null;
        if (m_architectureLvEntity.passive > 0)
        {
            m_normalSkill = AddSkill(m_architectureLvEntity.passive);
        }

        architectureLvEntity entity = ExcelData.Instance.UnitExcel.architectureLvList
                                .Find(arch => arch.idGroup == IdGroup && (arch.lv == nextLv));
        if (entity != null)
            m_hpTxt = entity.hp.ToString();
        else
            m_hpTxt = this.Health.ToString();

        ExtendSet();
        // 统计消耗
        getCostSpend();
    }

    private void applyEnabling(GemGoods val, bool isAdd = true) {
        if (val == null)
            return;
        string [] gemEffects = val.dataEntity.effect.Split("|");
        for (int i = 0; i < gemEffects.Length; i++)
        {
            int effectId = int.Parse(gemEffects[i]);
            empowerEffectEntity effectEntity = ExcelData.Instance.GetEmpowerEffectEntity(effectId);
            // 效果目标是当前建筑则 继续把效果加入进来
            if(effectEntity.tagBuild != IdGroup) continue; 
            // 作用于建筑本身
            if(effectEntity.effectTag == 1) 
            {
                // 新增技能
                if(effectEntity.effectType == 1)
                {
                    setGemSkill(effectEntity.effectParams1, isAdd);
                }
            }
            // 作用于附属单位
            else if(effectEntity.effectTag == 2)
            {
                // 附属单位新增技能
                if(effectEntity.effectType == 1)
                {
                    setGemSkillToChild(effectEntity.effectParams1, isAdd);
                }
                // 修改附属单位Id
                if(effectEntity.effectType == 2)
                {
                    int id = isAdd ? int.Parse(effectEntity.effectParams1) : 0;
                    gemChangeMonsterId(id);
                }
                // 改变附属单位数量
                if(effectEntity.effectType == 3)
                {
                    int count = isAdd ? int.Parse(effectEntity.effectParams1) : 0;
                    gemChangeMonsterCount(count);
                }
            }
        }
    }
    // 添加 | 移除 - 附属单位宝石技能
    public virtual void setGemSkillToChild(string val, bool isAdd){}
    
    // 添加 | 移除 - 宝石技能
    private void setGemSkill(string val, bool isAdd)
    {
        // 判断字符串是否为空
        if (string.IsNullOrEmpty(val)) return;
        string[] gemSkills = val.Split("|");
        for (int i = 0; i < gemSkills.Length; i++)
        {
            int skillId = int.Parse(gemSkills[i]);
            if(isAdd)
                AddSkill(skillId);
            else
                RemoveSkill(skillId);
        }
    }
    // 改变附属单位的id
    public virtual void gemChangeMonsterId(int id){}

    // 改变附属单位的数量
    public virtual void gemChangeMonsterCount(int count){}

    public void addGoodsToSlot(GemGoods goodsItem)
    {
        if (goodsItem == null)
            return;
        // 背包减去
        OtherInventory.Instance.UseItems(goodsItem); // 仓库减
        // 防御塔嵌入
        m_Slot.Add(goodsItem);
        // 应用效果
        applyEnabling(goodsItem);
    }
    // 移除宝石
    public void removeGoodsFromSlot(GemGoods goodsItem)
    {
        if (goodsItem == null)
            return;
        int index = m_Slot.FindIndex(val => val.id == goodsItem.id);
        removeGoodsFromSlot(index, goodsItem);
        OtherInventory.Instance.AddItems(goodsItem); // 仓库加
    }
    // 移除指定位置的宝石
    public void removeGoodsFromSlot(int index, GemGoods goodsItem)
    {
        if (m_Slot.Count <= index)
            return;
        applyEnabling(goodsItem, false);
        m_Slot.RemoveAt(index);
    }

    // 替换宝石
    public void updateGoods(int index, GemGoods goodsItem)
    {
        if (m_Slot.Count <= index)
            return;
        GemGoods newItem = m_Slot[index];
        OtherInventory.Instance.UseItems(goodsItem); // 仓库减
        OtherInventory.Instance.AddItems(newItem); // 仓库加
        removeGoodsFromSlot(index, newItem); // 塔拆
        m_Slot.Add(goodsItem); // 防御塔嵌入
        // 应用效果
        applyEnabling(goodsItem);
    }
    // 移除全部
    public void removeAllSlots()
    {
        for (int i = 0; i < m_Slot.Count; i++)
        {
            applyEnabling(m_Slot[i], false);
            OtherInventory.Instance.AddItems(m_Slot[i]);
        }
        m_Slot.Clear();
    }

    private void getCostSpend()
    {
        m_costSpend = 0;
        List<architectureLvEntity> _list = ExcelData.Instance.UnitExcel.architectureLvList.FindAll(val => val.idGroup == IdGroup).FindAll(val => val.lv <= CurrentLv);
        for (int i = 0; i < _list.Count; i++)
        {
            architectureLvEntity entity = _list[i];
            if (entity != null)
            {
                m_costSpend += entity.cost;
            }
        }
    }

    public virtual string GetIntroduceTxt()
    {
        return m_introduceTxt;
    }
    public virtual string GetBuildNameTxt()
    {
        return m_buildNameTxt;
    }
    public virtual string GetLvText()
    {
        return m_lvText;
    }
    public virtual string GetHpTxt()
    {
        return m_hpTxt;
    }

    /// <summary>
    /// 共鸣，灵兽进入建筑
    /// </summary>
    /// <param name="index"></param>
    public virtual void EnterElf(int index)
    {
        if (m_topTip != null)
            m_topTip.gameObject.SetActive(true);
        if (m_bindElf == m_candidateElfList[index]) return;
        m_bindElf = m_candidateElfList[index];
        m_bindElf.gameObject.SetActive(false);
        GameMain.Instance.Role.ElfEnterBuilding(m_bindElf);
    }
    /// <summary>
    /// 灵兽离开建筑
    /// </summary>
    public virtual void ExitElf()
    {
        if (m_topTip != null)
            m_topTip.gameObject.SetActive(false);
        if (m_bindElf == null) return;
        GameMain.Instance.Role.ElfExitBuilding(m_bindElf);

        m_bindElf.bindBuilding = null;
        m_bindElf.elfActionType = ElfActionType.None;
        m_bindElf.gameObject.SetActive(true);
        m_bindElf = null;
    }
    public virtual void OnTurnNext()
    {
        m_currentHealth = this.Health;
        //ExitElf();
        BuildingShow();
        if (m_destroyModel != null)
            m_destroyModel.SetActive(false);

        SpawnJade();
    }

    protected void SpawnJade()
    {
        if (m_buildingJadeCount == 0) return;
        Transform trans = GetAnchor(AnchorType.Center);
        GameObject prefab = Resources.Load("Building\\Prefab\\FlyJade") as GameObject;

        GameObject go = GameObject.Instantiate(prefab);
        go.transform.position = trans.position + new Vector3(Random.Range(-3.0f, 3.0f), 0, Random.Range(-3.0f, 3.0f));
        FlyJade jade = go.GetComponent<FlyJade>();
        jade.jadeCount = m_buildingJadeCount;
    }

    public virtual void showNextBuilding()
    {
        // 保存当前建筑等级
        int nextCurrentLv = m_currentLv + 1;
        if (nextCurrentLv < m_BuildingLv.Length)
        {
            m_stempCurrentLv = nextCurrentLv;
            // 修改下一级建筑的材质透明
            GameObject nextBuilding = m_BuildingLv[m_stempCurrentLv];
            GameObject currentBuilding = m_BuildingLv[m_currentLv];
            MeshRenderer[] meshRenderers = nextBuilding.GetComponentsInChildren<MeshRenderer>(); // 获取所有子对象中的 MeshRenderer 组件
            if (meshRenderers.Length > 0)
            {
                for (var i = 0; i < meshRenderers.Length; i++)
                {
                    materialSetOpcity(meshRenderers[i].material, 0.3f);
                }
            }
            // 隐藏当前建筑
            if (currentBuilding != null)
            {
                currentBuilding.SetActive(false);
            }
            nextBuilding.SetActive(true);
        }
    }

    public virtual void hideNextBuilding()
    {
        //Debug.Log("hideNextBuilding  进入------------------");
        if (m_stempCurrentLv < 0 || m_stempCurrentLv >= m_BuildingLv.Length) return;
        //Debug.Log("hideNextBuilding------------------");
        // 恢复当前建筑等级
        GameObject nextBuilding = m_BuildingLv[m_stempCurrentLv];
        GameObject currentBuilding = m_BuildingLv[m_currentLv];
        MeshRenderer[] meshRenderers = nextBuilding.GetComponentsInChildren<MeshRenderer>(); // 获取所有子对象中的 MeshRenderer 组件
        if (meshRenderers.Length > 0)
        {
            for (var i = 0; i < meshRenderers.Length; i++)
            {
                recoverMaterial(meshRenderers[i].material);
            }
        }
        nextBuilding.SetActive(false);
        if (currentBuilding != null)
        {
            currentBuilding.SetActive(m_currentLv != 0);
        }
        m_stempCurrentLv = -1;
    }

    public void setMarkArrow(bool isShow)
    {
        if (m_markArrow != null)
        {
            Vector3 pos = transform.position; // 获取当前游戏对象的坐标
            MarkScript markScript = m_markArrow.GetComponent<MarkScript>();
            if (isShow)
            {
                markScript.ShowMark(new Vector3(pos.x, pos.y + 8f, pos.z), this);
            }
            else
            {
                markScript.HideMark(this);
            }
        }
    }

    public virtual void showBuildEditUi()
    {
        // m_showBuildEditUI = true;

        EventDispatcher.TriggerEvent(EventDispatcherType.OpenBuildingDescUI, this);
        EventDispatcher.TriggerEvent(EventDispatcherType.onBuildEdit, this);
    }

    public virtual void hideBuildEditUi()
    {
        // m_showBuildEditUI = false;

        EventDispatcher.TriggerEvent(EventDispatcherType.CloseBuildingDescUI, this);
        EventDispatcher.TriggerEvent(EventDispatcherType.onBuildEditClose, this);
    }


    public virtual void OnTriggerStay(Collider other)
    {
        // 如果是战斗阶段，则不响应
        if (GameMain.Instance.gameStage == GameStage.Fight) return;
        // 野兽区域回合战斗时不响应
        if (GameMain.Instance.isBeastRound) return;
        // 非玩家单位不响应
        if (other.gameObject.layer != LayerMask.NameToLayer("Player")) return;
        // 超视角开启时 不响应
        if (SceneMain.Instance.CameraHeightController.IsSuperView || SceneMain.Instance.CameraHeightController.IsSwitchingView) return;
        // 当前建筑不是友好单位不响应
        if (Camp != CampEnum.Ally) return;
        GridAgent agent = other.GetComponent<GridAgent>();
        if (agent == null) return;
        float agentSpeed = agent.Velocity.magnitude;
        // 范围内停止了移动 && 没有执行建造或者升级 && 玩家也没有操作移动
        if (agentSpeed <= 0 && m_editOver == false && Role.Instance.horizontal == 0 && Role.Instance.vertical == 0)
        {
            // Debug.Log("OnTriggerStay  停止了移动--------");
            // 避免重复执行
            if (BuildEditUI.instance?.EditBuild == null)
            {
                showBuildEditUi();
            }
        }
    }
    // 条件
    public virtual bool IsTriggerCondition(Collider other)
    {
        // 如果是战斗阶段，则不响应
        if (GameMain.Instance.gameStage == GameStage.Fight) return false;
        // 野兽区域回合不响应
        if (GameMain.Instance.isBeastRound) return false;
        if (other.gameObject.layer != LayerMask.NameToLayer("Player")) return false;
        // 当前建筑不是友好单位不响应
        if (Camp != CampEnum.Ally) return false;
        return true;
    }
    public virtual void OnTriggerEnter(Collider other)
    {
        if (!IsTriggerCondition(other)) return;
        // 超视角开启时 不响应
        if (SceneMain.Instance.CameraHeightController.IsSuperView || SceneMain.Instance.CameraHeightController.IsSwitchingView) return;
        // 玩家靠近时展示选中的箭头
        setMarkArrow(true);
        // 展示虚影
        showNextBuilding();
        // 显示右侧说明UI
        // EventDispatcher.TriggerEvent(EventDispatcherType.OpenBuildingDescUI, this);
        // 已经建造了不再操作
        //if (m_buildingChange) return;
        // IsNearBuilding = true;

        // KeyStatusManager.Instance.RegisterKeyDownCallback(KeyCode.Space, this.gameObject, OnSpaceKeyDown);
        // 未建造继续执行d
        // this.showNextBuilding();
    }
    // public virtual void triggerSpaceAction()
    // {
    //     if(m_currentLv >= m_BuildingLv.Length - 1) return;
    //     if (m_currentLv == 0)
    //     {
    //         EventDispatcher.TriggerEvent(EventDispatcherType.OpenBuildPopUp, this);
    //     }
    //     else
    //     {
    //         EventDispatcher.TriggerEvent(EventDispatcherType.OpenOptionsUI, this);
    //         OpenUpgradeUI();
    //     }
    // }
    public virtual void OnTriggerExit(Collider other)
    {
        if (!IsTriggerCondition(other)) return;
        // if (SceneMain.Instance.CameraHeightController.IsSuperView || SceneMain.Instance.CameraHeightController.IsSwitchingView) return;
        EditOver = false;
        // KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.Space, this.gameObject);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseBuildingDescUI, this);
        this.setMarkArrow(false);
        hideNextBuilding();
        hideBuildEditUi();
        //if (m_buildingChange) return;

        // if (m_currentLv == 0)
        // {
        //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseBuildPopUp);
        //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonBuildUI2);
        // }
        // else
        // {
        //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseOptionsUI);
        //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonUpgradeUI);
        // }
        // IsNearBuilding = false;
        // this.hideNextBuilding();
    }
    // public virtual void OpenBuildUI()
    // {
    //     EventDispatcher.TriggerEvent(OpenBuildUIEvent, this);
    // }
    // public virtual void OpenUpgradeUI()
    // {

    //     architectureLvEntity nextEntity = ExcelData.Instance.UnitExcel.architectureLvList
    //                                     .Find(show => show.idGroup == IdGroup && show.lv == (CurrentLv + 1));
    //     if(nextEntity == null )
    //     {
    //         //已经是顶级，无法升级
    //         EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, Language.GetText(100022));
    //         return;
    //     }
    //     EventDispatcher.TriggerEvent(OpenUpgradeUIEvent, this);
    // }

    public virtual void OpenResonanceUI()
    {
        if (m_candidateElfList.Count == 0) return;

        // EventDispatcher.TriggerEvent(EventDispatcherType.OpenResonanceUI, this);
    }
    public virtual void Build()
    {
        m_boxColliderForAttackCheck.enabled = true;
        hideNextBuilding();
        m_navMeshObstacle.enabled = true;
        BuildingShow();
        EventDispatcher.TriggerEvent(EventDispatcherType.BuildComplete, this);
    }
    public virtual void ResetNominal()
    {
        CurrentLv = 0;
        m_boxColliderForAttackCheck.enabled = false;
        m_navMeshObstacle.enabled = false;
        m_isGhost = false;
        if (m_destroyModel != null)
        {
            m_destroyModel.SetActive(false);
        }
        BuildingShow();
        base.Renew();
    }
    public virtual void Upgrade()
    {
        // todu bug 不显示下一级的建筑
        hideNextBuilding();
        BuildingShow();
    }

    /// <summary>
    /// 共鸣
    /// </summary>
    public virtual void Resonance(int index)
    {
        if (m_bindElf == null)
            EnterElf(index);
        else
            ExitElf();
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseResonanceUI);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseOptionsUI);
    }

    /// <summary>
    /// 被SetData函数调用
    /// </summary>
    public virtual void ExtendSet() { }

    public virtual void BuildingShow()
    {
        for (int i = 0; i < m_BuildingLv.Length; i++)
        {
            if (m_BuildingLv[i] != null)
                m_BuildingLv[i].gameObject.SetActive(false);
        }
        int index = m_currentLv;
        if (index >= 0 && index < m_BuildingLv.Length)
        {
            if (m_BuildingLv[index] != null)
                m_BuildingLv[index].gameObject.SetActive(true);
        }
    }

    public virtual void ShowMark()
    {
        if (MainBase.Instance.CurrentLv < MainbaseLevelAppear)
        {
            m_mark.gameObject.SetActive(false);
            m_colliderInBuildStage.enabled = false;
            return;
        }

        m_mark.gameObject.SetActive(true);
        m_colliderInBuildStage.enabled = true;
    }
    public virtual void HideMark()
    {
        m_mark.gameObject.SetActive(false);
        m_colliderInBuildStage.enabled = false;
    }

    // public virtual void OnSpaceKeyDown()
    // {
    //     if (GameMain.Instance.gameStage == GameStage.Fight) return;
    //     // 监听空格
    //     triggerSpaceAction();
    // }

    public virtual BuildIconType GetBuildIconType()
    {
        return BuildIconType.Jade;
    }

    public override void OnDeath()
    {
        if (m_isGhost) return;
        ClearSkill();

        m_isGhost = true;
        if (m_destroyModel != null)
        {
            m_destroyModel.SetActive(true);
        }
        m_navMeshObstacle.enabled = false;
        for (int i = 0; i < m_BuildingLv.Length; i++)
        {
            if (m_BuildingLv[i] != null)
                m_BuildingLv[i].SetActive(false);
        }
        EventDispatcher.TriggerEvent(EventDispatcherType.BuildingDestroy, this);
    }

    public int getNextLvCost()
    {
        architectureLvEntity entity = ExcelData.Instance.UnitExcel.architectureLvList
                                .Find(arch => arch.idGroup == IdGroup && (arch.lv == (CurrentLv + 1)));
        if (entity != null)
            return entity.cost;
        else
            return 0;
    }

    public override void Renew()
    {
        m_isGhost = false;
        if (m_destroyModel != null)
        {
            m_destroyModel.SetActive(false);
        }
        if (m_navMeshObstacle != null)
            m_navMeshObstacle.enabled = true;
        BuildingShow();
        base.Renew();
    }

    public virtual void SetElementSkill(List<int> skills)
    {

    }

    public virtual void changeActorCamp(CampEnum camp)
    {
        //todo
    }

    public virtual void OnStartFight()
    {
        //玩家站在建筑边，碰撞体被关闭时，没有触发OnTriggerExit，在这里取消空格事件
        KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.Space, this.gameObject);
        HideMark();
        hideNextBuilding();
    }

    // 拆除 
    public virtual void destroyBuilding()
    {
        CurrentLv = 0;
        removeAllSlots();
        SetData();
    }

    void OnDrawGizmos()
    {
        if (SpawnPoint.Length > 0)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < SpawnPoint.Length; i++)
            {
                Gizmos.DrawSphere(SpawnPoint[i].transform.position, 0.2f);
            }
        }

        // 设置 Gizmos 颜色
        Gizmos.color = Color.green;

        // 绘制二维圆（XZ 平面）
        int segments = 36; // 圆的细分段数
        float angleStep = 360f / segments;

        Vector3 center = transform.position;
        Vector3 prevPoint = center + new Vector3(m_radius, 0, 0);

        for (int i = 1; i <= segments; i++)
        {
            float angle = angleStep * i;
            Vector3 nextPoint = center + new Vector3(
                Mathf.Cos(Mathf.Deg2Rad * angle) * m_radius,
                0,
                Mathf.Sin(Mathf.Deg2Rad * angle) * m_radius
            );

            Gizmos.DrawLine(prevPoint, nextPoint);
            prevPoint = nextPoint;
        }
    }
}
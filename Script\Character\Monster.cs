using Excel.monster;
using FSM;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using UnityEngine.AI;

public class Monster : Actor
{
    protected Animator m_Animator;
    public Animator Animator { get => m_Animator; }

    public override AnimationType AnimationType 
    {
        get { return m_AnimationType; }
        set
        {
            if (m_AnimationType == value) return;

            lastNormalizedTime = 0;
            m_AnimationType = value;
            m_Animator.SetInteger("AnimationType",(int)m_AnimationType);
        }
    }

    private float m_fireElementStartTime;
    public float fireElementStartTime
    {
        get { return m_fireElementStartTime; }
        set { m_fireElementStartTime = value; }
    }
    public float m_fireElementDuration;
    public float fireElementDuration
    {
        get { return m_fireElementDuration; }
        set { m_fireElementDuration = value; }
    }

    public override float AnimationSpeed 
    { 
        get => m_Animator.speed;
        set 
        {
            m_Animator.speed = value;
        }
    }

    public override float CastingRange
    {
        get 
        {
            float range = 0;
            if (m_currentSkill != null)
                range = m_currentSkill.CastingRange;
            if (m_normalSkill != null)
                range = m_normalSkill.CastingRange;
            return range;
        }
    }

    private monsterBaseEntity m_monsterBaseEntity;

    public override void Awake()
    {
        m_Animator = GetComponent<Animator>();
        base.Awake();
        LoadState();
        AnimatorController controller = m_Animator.runtimeAnimatorController as AnimatorController;
        if (controller == null)
        {
            Debug.LogError("Animator Controller not found!");
            return;
        }

        // 遍历所有状态
        ChildAnimatorState state;
        AnimatorControllerLayer layer = controller.layers[0];
        for (int i = 0; i < layer.stateMachine.states.Length; i++)
        {
            state = layer.stateMachine.states[i];
            AnimationClip clip = state.state.motion as AnimationClip;
            if( state.state.name == "idle")
            {
                m_idleAnimationLength = clip.length;
            }
            else if (state.state.name == "run")
            {
                m_runAnimationLength = clip.length;
            }
            else if( state.state.name == "attack")
            {
                m_attackAnimationLength = clip.length;
            }    
            else if( state.state.name == "PreSkillCast")
            {
                m_PreSkillCastAnimationLength = clip.length;
            }    
            else if( state.state.name == "SkillCastLoop")
            {
                m_SkillCastLoopAnimationLength = clip.length;
            }    
            else if( state.state.name == "PostSkillCast")
            {
                m_PostSkillCastAnimationLength = clip.length;
            }    
            else if( state.state.name == "damage")
            {
                m_damageAnimationLength = clip.length;
            }    
            else if( state.state.name == "death")
            {
                m_deathAnimationLength = clip.length;
            }    
        }
    }

    public override bool isCurrentAnimationFinish()
    {
        AnimatorStateInfo stateInfo = m_Animator.GetCurrentAnimatorStateInfo(0);

        float normalizedTime = stateInfo.normalizedTime % 1.0f;

        if (lastNormalizedTime > 0.9f && normalizedTime < 0.3f && !m_Animator.IsInTransition(0))
            return true;

        lastNormalizedTime = normalizedTime;

        return false;
    }
    public void SetData(monsterBaseEntity entity)
    {
        m_monsterBaseEntity = entity;
        this.Health     = entity.hp;
        m_currentHealth  = entity.hp;
        this.ArmorBreak = entity.brk;
        this.Defense    = entity.def;
        this.AttackDamage=entity.atk;
        this.AttackSpeed= entity.asp;
        this.MoveSpeed  = entity.spd;
        this.raceEnum = (RaceEnum)entity.race;

        m_threatTimeThreshold    = entity.searchTime/1000;

        m_originalHealth        = entity.hp;
        m_originalArmorBreak    = entity.brk;
        m_originalDefense       = entity.def;
        m_originalAttackDamage  = entity.atk;
        m_originalAttackSpeed   = entity.asp;
        m_originalMoveSpeed     = entity.spd;

        m_quality = (QualityEnum)entity.quality;

        m_fiveElements = (FiveElementEnum)entity.attributeType;
        m_movementType = (MovementType)entity.terrain;
        m_CombatType = (CombatType)entity.combat;

        if (entity.attack > 0)
            AddSkill(entity.attack);
        if (!string.IsNullOrEmpty(entity.skill))
        {
            string[] skillIDArray = entity.skill.Split('|');
            for (int i = 0; i < skillIDArray.Length; i++)
                AddSkill(int.Parse(skillIDArray[i]));
        }

        m_normalSkill = m_skillList.Find(skill =>skill.SkillID == entity.attack);
    }

    public override bool UseSkill()
    {
        m_currentSkill = null;
        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (m_skillList[i].CanUse() && m_skillList[i].Enable )
            {
                m_currentSkill = m_skillList[i];
                break;
            }
        }

        if (m_currentSkill != null)
        {
            return EnterSkillState();
        }
        return false;
    }

    /// <summary>
    /// 添加五行伤害
    /// </summary>
    /// <param name="attacker"></param>
    /// <param name="attackerType"></param>
    /// <param name="fiveElement"></param>
    /// <param name="damageNum"></param>
    public override void AddFiveElementDamage(GameEntity attacker, ObjectType attackerType, int fiveElement, int damageNum)
    {
        if( m_camp == CampEnum.Ally) return;
        if (m_AccumulateTime[fiveElement] + monsterConfig.accumulationCoolingTime > Time.time) 
            return;

        int type = 0;
        if (attackerType == ObjectType.Building)
            type = 1;
        else if (attackerType == ObjectType.Role)
            type = 2;
        else if( attackerType == ObjectType.Soldier)
            type = 3;
        else if( attackerType == ObjectType.Pet)
            type = 4;
        if (type == 0)
        {
            Debug.Log("AddFiveElementDamage:类型错误");
            return;
        }
        damgeActEntity entity = ExcelData.Instance.MonsterExcel.damgeActList.Find((item) =>
        {
            return item.type == type;
        });
        if(entity == null) return;
        int damage = damageNum == 1 ? entity.singleDamge : entity.manyDamge;
        //累积伤害
        m_AccumulateTotal[fiveElement] += damage;

        float max = 0;
        int targetIndex = 0;
        //选择最大的值
        for (int i = 1; i < m_AccumulateTotal.Length; i++)
        {
            if (m_AccumulateTotal[i] > max)
            {
                max = m_AccumulateTotal[i];
                targetIndex = i;                     
            }
        }

        if (targetIndex != 0 && m_quality != QualityEnum.None)
        {
            int _key = (int)m_quality;
            //判断是否有对应的品质
            if (!monsterConfig.MapAccumulatedValue.ContainsKey(_key))
                return;
            //品质对应的数值
            int maxDamage = monsterConfig.MapAccumulatedValue[ (int)m_quality ];

            EventDispatcher.TriggerEvent<GameEntity, float, float, int>(EventDispatcherType.UpdateFiveElementDamage, this, m_AccumulateTotal[targetIndex], maxDamage, targetIndex);

            if (max >= maxDamage)
            {
                string EffectID = monsterConfig.MapAccumulatedEffect[fiveElement];
                AddSkillEffect(null, EffectID, attacker);

                //重置
                for ( int i = 1; i < m_AccumulateTotal.Length; i++)
                {
                    m_AccumulateTotal[i] = 0;
                    m_AccumulateTime[i] = Time.time;
                }

                EventDispatcher.TriggerEvent<GameEntity>(EventDispatcherType.DisableFiveElementDamage, this);

                if( fiveElement == (int)FiveElementEnum.Fire && int.Parse(EffectID) == 12 )
                {
                    m_fireElementStartTime = Time.time;
                    m_fireElementDuration = 10;//10秒
                }
            }
        }
    }

    public bool IsInFireElement
    {
        get
        {
            if (m_fireElementStartTime > 0 && Time.time - m_fireElementStartTime < m_fireElementDuration)
                return true;
            return false;
        }
    }

}

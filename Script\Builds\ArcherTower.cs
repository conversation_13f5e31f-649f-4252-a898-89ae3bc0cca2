using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// 小屋
/// </summary>
public class ArcherTower : BuildingBase
{
    public override void Start()
    {
        IdGroup = 204;
        m_currentLv = 0;
        editUiShowAttribute.showAttcack = true;
        editUiShowAttribute.showProfit = true;
        SetData();
        base.Start();
    }


    //public override void EnterElf(int index)
    //{
    //    base.EnterElf(index);
    //    m_bindElf.bindBuilding = this;
    //    m_bindElf.elfActionType = ElfActionType.Resonance;
    //}

    public override void Build()
    {
        Upgrade();
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonBuildUI);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseBuildPopUp);
        base.Build();
    }

    public override void Upgrade()
    {
        m_currentLv++;
        SetData();

        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseOptionsUI);
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseCommonUpgradeUI);

        base.Upgrade();
    }

    //public override void OpenResonanceUI()
    //{
    //    m_candidateElfList.Clear();

    //    Elf elf;
    //    for (int i = 0; i < GameMain.Instance.Role.elfList.Count; i++)
    //    {
    //        elf = (Elf)GameMain.Instance.Role.elfList[i];
    //        if (elf.ResonanceList.Exists(r => r.buildingID == IdGroup) && elf.elfActionType == ElfActionType.None)
    //            m_candidateElfList.Add(elf);
    //    }
    //    EventDispatcher.TriggerEvent(EventDispatcherType.OpenResonanceUI, this);
    //}
    // public override void OnTriggerExit(Collider other)
    // {
    //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseResonanceUI);
    //     base.OnTriggerExit(other);
    // }
}

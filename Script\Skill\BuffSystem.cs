using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using static BuffData;
using static UnityEditor.Rendering.FilterWindow;

[Serializable]
public class BuffSystem
{
    private GameEntity m_bindActor;
    public GameEntity bindActor { get { return m_bindActor; } set { m_bindActor = value; } }

    public List<Buff> m_buffList = new List<Buff>();
    private List<Buff> m_buffRemoveList = new List<Buff>();

    public BuffSystem(GameEntity actor)
    {
        m_bindActor = actor;
    }

    public void AddBuffPool(int buffPoolID, Skill skill)
    {

        List<Buff> newBuffList = BuffPool.GetBuff(buffPoolID, skill);

        AddBuff(newBuffList);
    }

    //public void AddBuff(List<Buff> newBuffList)
    //{
    //    m_buffRemoveList.Clear();

    //    //检查新的BUFF有异常状态
    //    List<Buff> ignoreAbnormalList = m_buffList.FindAll(bf => bf.isIgnoreAbnormal);
    //    for (int i = 0; i < ignoreAbnormalList.Count; i++)
    //    {
    //        newBuffList.RemoveAll(element => ignoreAbnormalList[i].IgnoreAbnormal(element));
    //    }
    //    if (newBuffList.Count == 0) return;

    //    //找出已有并且可以被无视异常状态的BUFF
    //    for (int i = 0; i < newBuffList.Count; i++)
    //    {
    //        if (newBuffList[i].isIgnoreAbnormal)
    //        {
    //            for (int buffIndex = 0; buffIndex < m_buffList.Count; buffIndex++)
    //            {
    //                if (newBuffList[i].IgnoreAbnormal(m_buffList[buffIndex]))
    //                    m_buffRemoveList.Add(m_buffList[buffIndex]);
    //            }
    //        }
    //    }
    //    for (int i = 0; i < m_buffRemoveList.Count; i++)
    //    {
    //        m_buffRemoveList[i].LeaveActor();
    //        m_buffList.Remove(m_buffRemoveList[i]);
    //    }
    //    //处理无敌BUFF
    //    Buff buffUnbeatable = m_buffList.Find(bf => bf.isUnbeatableBuff);
    //    if (buffUnbeatable != null)
    //    {
    //        //无敌期间，无敌单位免疫的buff类型
    //        newBuffList.RemoveAll(element =>
    //        {
    //            switch ((int)buffUnbeatable.BuffData.buffParams1)
    //            {
    //                case 1:
    //                    if (element.BuffData.MainType == MainEnum.Control)
    //                        return true;
    //                    break;
    //                case 2:
    //                    if (element.BuffData.MainType == MainEnum.Abnormal)
    //                        return true;
    //                    break;
    //                case 3:
    //                    if (element.BuffData.MainType == MainEnum.Abnormal || element.BuffData.MainType == MainEnum.Control)
    //                        return true;
    //                    break;
    //            }
    //            return false;
    //        });
    //    }


    //    for (int i = 0; i < newBuffList.Count; i++)
    //    {
    //        Buff buff = m_buffList.Find((buff) => buff.BuffData.ID == newBuffList[i].BuffData.ID);
    //        if (buff != null)
    //        {
    //            if (buff.BuffData.Superposition == BuffData.SuperpositionEnum.Discard)
    //                continue;
    //            if (buff.BuffData.Superposition == BuffData.SuperpositionEnum.Renew)
    //            {
    //                buff.Reset();
    //                continue;
    //            }
    //            else if (buff.BuffData.Superposition == BuffData.SuperpositionEnum.Stack)
    //            {
    //                int count = m_buffList.FindAll((bf) => bf.BuffData.ID == buff.BuffData.ID).Count;
    //                if (count < buff.BuffData.MaxSupersition)
    //                    buff.Stack();
    //            }
    //        }
    //        else
    //        {
    //            if (newBuffList[i].isUnbeatableBuff)
    //            {
    //                //无敌期间，无敌单位免疫的buff类型,已存在的移除
    //                m_buffList.RemoveAll(element =>
    //                {
    //                    switch ((int)newBuffList[i].BuffData.buffParams1)
    //                    {
    //                        case 1:
    //                            if (element.BuffData.MainType == MainEnum.Control)
    //                                return true;
    //                            break;
    //                        case 2:
    //                            if (element.BuffData.MainType == MainEnum.Abnormal)
    //                                return true;
    //                            break;
    //                        case 3:
    //                            if (element.BuffData.MainType == MainEnum.Abnormal || element.BuffData.MainType == MainEnum.Control)
    //                                return true;
    //                            break;
    //                    }
    //                    return false;
    //                });
    //            }

    //            newBuffList[i].Attacher = m_bindActor;
    //            newBuffList[i].StartTime = Time.time;
    //            m_buffList.Add(newBuffList[i]);
    //        }
    //    }
    //}

    public void AddBuff(List<Buff> newBuffList)
    {
        for (int i = 0; i < newBuffList.Count; i++)
            AddBuff(newBuffList[i]);
    }

    public void AddBuff(Buff newBuff)
    {
        m_buffRemoveList.Clear();

        //检查新的BUFF是否有异常状态
        List<Buff> ignoreAbnormalList = m_buffList.FindAll(bf => bf.isIgnoreAbnormal);
        for (int i = 0; i < ignoreAbnormalList.Count; i++)
        {
            if (ignoreAbnormalList[i].IgnoreAbnormal(newBuff))
                return;
        }


        //找出已有并且可以被无视异常状态的BUFF
        if (newBuff.isIgnoreAbnormal)
        {
            for (int buffIndex = 0; buffIndex < m_buffList.Count; buffIndex++)
            {
                if (newBuff.IgnoreAbnormal(m_buffList[buffIndex]))
                    m_buffRemoveList.Add(m_buffList[buffIndex]);
            }
        }
        for (int i = 0; i < m_buffRemoveList.Count; i++)
        {
            m_buffRemoveList[i].LeaveActor();
            m_buffList.Remove(m_buffRemoveList[i]);
        }
        //处理无敌BUFF
        Buff buffUnbeatable = m_buffList.Find(bf => bf.isUnbeatableBuff);
        if (buffUnbeatable != null)
        {
            //无敌期间，无敌单位免疫的buff类型
            switch ((int)buffUnbeatable.BuffData.buffParams1)
            {
                case 1:
                    if (newBuff.BuffData.MainType == MainEnum.Control)
                        return;
                    break;
                case 2:
                    if (newBuff.BuffData.MainType == MainEnum.Abnormal)
                        return;
                    break;
                case 3:
                    if (newBuff.BuffData.MainType == MainEnum.Abnormal || newBuff.BuffData.MainType == MainEnum.Control)
                        return;
                    break;
            }
            return;
        }

        Buff currentBuff = m_buffList.Find((buff) => buff.BuffData.ID == buff.BuffData.ID);
        if (currentBuff != null)
        {
            if (currentBuff.BuffData.Superposition == BuffData.SuperpositionEnum.Renew)
            {
                currentBuff.Reset();
            }
            else if (currentBuff.BuffData.Superposition == BuffData.SuperpositionEnum.Stack)
            {
                int count = m_buffList.FindAll((bf) => bf.BuffData.ID == currentBuff.BuffData.ID).Count;
                if (count < newBuff.BuffData.MaxSupersition)
                    currentBuff.Stack();
            }
        }
        else
        {
            if (newBuff.isUnbeatableBuff)
            {
                //无敌期间，无敌单位免疫的buff类型,已存在的移除
                m_buffList.RemoveAll(element =>
                {
                    switch ((int)newBuff.BuffData.buffParams1)
                    {
                        case 1:
                            if (element.BuffData.MainType == MainEnum.Control)
                                return true;
                            break;
                        case 2:
                            if (element.BuffData.MainType == MainEnum.Abnormal)
                                return true;
                            break;
                        case 3:
                            if (element.BuffData.MainType == MainEnum.Abnormal || element.BuffData.MainType == MainEnum.Control)
                                return true;
                            break;
                    }
                    return false;
                });
            }

            newBuff.Attacher = m_bindActor;
            newBuff.StartTime = Time.time;
            m_buffList.Add(newBuff);
        }
    }

    public void RemoveBuff(Buff buff)
    {
        buff.LeaveActor();
        m_buffList.Remove(buff);
    }

    public void RemoveBuff(int skillID)
    {
        for (int i = m_buffList.Count; i > 0 ; i--)
        {
            if (m_buffList[i].bindSkill != null && m_buffList[i].bindSkill.SkillID == skillID)
            {
                m_buffList[i].LeaveActor();
                m_buffList.Remove(m_buffList[i]);
            }
        }
    }

    public void RemoveBuff(Skill skill)
    {
        for (int i = m_buffList.Count; i > 0; i--)
        {
            if (m_buffList[i].bindSkill != null && m_buffList[i].bindSkill == skill)
            {
                m_buffList[i].LeaveActor();
                m_buffList.Remove(m_buffList[i]);
            }
        }
    }

    public bool haveBuff(int buffID)
    {
        return m_buffList.Exists(buff => buff.BuffData.ID == buffID);
    }

    public void AttackResult(float damage)
    {
        for (int i = 0; i < m_buffList.Count; i++)
        {
            m_buffList[i].AttackResult(damage);
        }
    }

    public void Update()
    {
        m_buffRemoveList.Clear();
        for (int i = 0; i < m_buffList.Count; i++)
        {
            m_buffList[i].Update();
            if (m_buffList[i].isComplete)
                m_buffRemoveList.Add(m_buffList[i]);
        }
        for (int i = 0; i < m_buffRemoveList.Count; i++)
        {
            m_buffRemoveList[i].LeaveActor();
            m_buffList.Remove(m_buffRemoveList[i]);
        }

        RemoveUnbeatableBuff();

        RemoveShieldBuff();
    }

    public void RemoveBuff(int mainType, int subType)
    {
        //移除所有符合条件的BUFF
        for (int i = m_buffList.Count - 1; i > 0; i--)
        {
            if (mainType < 0)
            {
                m_buffList[i].LeaveActor();
                m_buffList.RemoveAt(i);
            }
            else if (mainType == (int)(m_buffList[i].BuffData.MainType))
            {
                if (subType < 0)
                {
                    m_buffList[i].LeaveActor();
                    m_buffList.RemoveAt(i);
                }
                else if (subType == (int)(m_buffList[i].BuffData.SubType))
                {
                    m_buffList[i].LeaveActor();
                    m_buffList.RemoveAt(i);
                }
            }
        }

    }

    private void RemoveUnbeatableBuff()
    {
        bool isRemove = false;
        int delType = 0;
        Buff unbeatableBuff = null; //无敌buff
        for (int i = 0; i < m_buffList.Count; i++)
        {
            if (m_buffList[i].isUnbeatableBuff && m_buffList[i].IsNeedRemoveBuff)
            {
                delType = (int)(m_buffList[i].BuffData.buffParams1);
                isRemove = true;
                unbeatableBuff = m_buffList[i];
                break;
            }
        }
        if (isRemove)
        {
            m_buffList.RemoveAll((buff) =>
            {
                if (buff == unbeatableBuff) return false;
                switch (delType)
                {
                    case 1:
                        if (buff.BuffData.MainType == MainEnum.Control)
                        {
                            buff.LeaveActor();
                            return true;
                        }
                        break;
                    case 2:
                        if (buff.BuffData.MainType == MainEnum.Abnormal)
                        {
                            buff.LeaveActor();
                            return true;
                        }
                        break;
                    case 3:
                        if (buff.BuffData.MainType == MainEnum.Abnormal || buff.BuffData.MainType == MainEnum.Control)
                        {
                            buff.LeaveActor();
                            return true;
                        }
                        break;
                }
                return false;
            });
        }
    }

    private void RemoveShieldBuff()
    {
        Buff buff = m_buffList.Find((buff) =>
        {
            return buff.isShield && !(buff.ShieldHealth > 0);
        });
        if (buff != null)
            m_buffList.Remove(buff);
    }

    /// <summary>
    /// 伤害反弹
    /// </summary>
    /// <param name="attacker"></param>
    /// <param name="damage"></param>
    public void ReboundDamage(GameEntity attacker, float damage)
    {
        for (int i = 0; i < m_buffList.Count; i++)
        {
            if (m_buffList[i].isRebound)
            {
                m_buffList[i].ReboundDamage(attacker, damage);
            }
        }
    }

    public bool CannotBeSelected
    {
        get => m_buffList.Exists(buff => buff.buffType == BuffData.BuffEnum.CannotBeSelected);
    }

    public bool haveShield
    {
        get => m_buffList.Exists(buff => buff.buffType == BuffData.BuffEnum.Shield);
    }
    public bool haveUnbeatable
    {
        get => m_buffList.Exists(buff => buff.isUnbeatableBuff);
    }

    /// <summary>
    /// 伤害加成
    /// </summary>
    public bool haveDamageBonus(GameEntity attacker)
    {
        Buff buff = m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.DamageBonus);

        if (buff == null) return false;

        int type = (int)buff.BuffData.buffParams1;
        if (type == 0)
            return true;

        if (attacker == null) return false;

        if (type == 1)
        {
            CombatType ct = (CombatType)buff.BuffData.buffParams2;
            return ct == attacker.CombatType;
        }
        return false;
    }

    public float DamageBonus
    {
        get
        {
            Buff buff = m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.DamageBonus);
            if (buff != null)
                return 1 + buff.BuffData.buffParams3;
            return 0;
        }
    }

    public bool haveDamageImmunity(Skill skill, int type, BuffData.MainEnum buffMain)
    {
        Buff buff = m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.DamageImmunity);
        if (buff == null)
            return false;
        bool attackCondition = false;
        if ((int)(buff.BuffData.buffParams1) == 1)//检测是否普通攻击
        {
            attackCondition = skill.Priority == Skill.PriorityEnum.Normal;
        }
        else if ((int)(buff.BuffData.buffParams1) == 2)//检测是否技能攻击
        {
            attackCondition = skill.Priority == Skill.PriorityEnum.Skill;
        }
        if (!attackCondition) return false;

        int rate = (int)(buff.BuffData.buffParams3 * 100);
        int randomValue = UnityEngine.Random.Range(0, 100);
        bool randomCondition = randomValue < rate;
        if (!randomCondition) return false;
        //1 = 伤害
        //2 = 异常效果
        //3 = 伤害 & 异常效果
        if (buff.BuffData.buffParams2 == 1)
            return type == 1;
        else if (buff.BuffData.buffParams2 == 2)
            return buffMain == MainEnum.Abnormal;
        else if (buff.BuffData.buffParams2 == 3)
            return (type == 1 || buffMain == MainEnum.Abnormal);

        return false;
    }



    public bool haveArmorBreak
    {
        get => m_buffList.Exists(buff => buff.buffType == BuffData.BuffEnum.ArmorBreak);
    }

    public float ArmorBreak
    {
        get
        {
            Buff buff = m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.ArmorBreak);
            if (buff != null)
                return 1 - buff.BuffData.buffParams3;
            return 0;
        }
    }
    /// <summary>
    /// 怪物种族伤害加成
    /// </summary>
    /// <returns></returns>
    public Buff getRaceDamageBonus()
    {
        return m_buffList.Find(b => b.buffType == BuffData.BuffEnum.RaceDamageBonus);
    }
    /// <summary>
    /// 生命值伤害加成
    /// </summary>
    /// <returns></returns>
    public Buff getHPDamageBonusBuff()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.HPDamageBonus);
    }
    /// <summary>
    /// 速度伤害加成
    /// </summary>
    /// <returns></returns>
    public Buff getSpeedDamageBonus()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.SpeedDamageBonus);
    }
    /// <summary>
    /// 伤害回血
    /// </summary>
    /// <returns></returns>
    public Buff getDamageLifesteal()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.DamageLifesteal);
    }

    public Buff GetShieldBuff()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.Shield);
    }
    /// <summary>
    /// 攻击对方增强自己
    /// </summary>
    /// <returns></returns>
    public Buff getAttackEnemiesToPowerUp()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.AttackEnemiesToPowerUp);
    }

    public Buff getPartDamageOverTime()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.PartDamageOverTime);
    }
    //攻击伤害加成
    public Buff getAttackDamageBonus()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.AttackDamageBonus);
    }

    public Buff getDamageReduction()
    {
        return m_buffList.Find(buff => buff.buffType == BuffData.BuffEnum.DamageReduction);
    }
    public void Clear()
    {
        for (int i = 0; i < m_buffList.Count; i++)
        {
            m_buffList[i].LeaveActor();
        }
        m_buffList.Clear();
        m_buffRemoveList.Clear();
    }


}

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SkillEffectBase
{
    protected int m_id;
    public int ID { get => m_id; set => m_id = value; }

    protected GameEntity m_bindActor;
    public GameEntity bindActor { get => m_bindActor; set => m_bindActor = value; }

    protected GameEntity m_releaser;
    public GameEntity Releaser { get => m_releaser; set => m_releaser = value; }

    protected float m_delay;
    public float Delay { get => m_delay; set => m_delay = value; }

    protected int m_targetDelay;
    public int TargetDelay { get => m_targetDelay; set => m_targetDelay = value; }

    protected SkillRange m_skillRange;
    public SkillRange skillRange { get => m_skillRange; set => m_skillRange = value; }

    protected SkillTarget m_skillTarget;
    public SkillTarget skillTarget { get => m_skillTarget; set => m_skillTarget = value; }

    public bool isTrigger = false;

    private float m_triggerTime;
    public float triggerTime { get => m_triggerTime; set => m_triggerTime = value; }

    private List<GameEntity> m_targetList = new List<GameEntity>();
    public List<GameEntity> targetList { get => m_targetList; }

    protected Skill m_bindSkill;
    public Skill bindSkill { get => m_bindSkill; set => m_bindSkill = value; }

    protected List<GameEntity> m_actorsTargetList = new List<GameEntity>();
    public List<GameEntity> ActorsTargetList { get => m_actorsTargetList; }

    //protected bool m_isFrozenEffect = false;
    //public bool isFrozenEffect { get => m_isFrozenEffect; set => m_isFrozenEffect = value; }
    public virtual void ApplyEffect( )
    {

    }

    public virtual void GetRangeTargets(GameEntity self, float CastingRange)
    { 
        m_actorsTargetList.Clear();
        m_actorsTargetList = m_skillRange.GetRangeTargets(self, m_skillTarget, CastingRange);
    }
    public virtual List<GameEntity> GetAllTarget() { return null;  }

}

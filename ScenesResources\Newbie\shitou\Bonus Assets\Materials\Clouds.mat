%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Clouds
  m_Shader: {fileID: 4800000, guid: 7633a2fd8a3bed44791c9e952ff86e5d, type: 3}
  m_Parent: {fileID: 2100000, guid: aea039e3a57ea064b93eac8e89356e4b, type: 2}
  m_ModifiedSerializedProperties: 20
  m_ValidKeywords:
  - _MK_ARTISTIC_HATCHING
  - _MK_ENVIRONMENT_REFLECTIONS_ADVANCED
  - _MK_LIGHT_CEL
  - _MK_OUTLINE_HULL_CLIP
  - _MK_RECEIVE_SHADOWS
  - _MK_SPECULAR_ISOTROPIC
  m_InvalidKeywords:
  - _ALPHAPREMULTIPLY_ON
  - _MK_ARTISTIC_ANIMATION_STUTTER
  - _MK_DETAIL_BLEND_MIX
  - _RECEIVESHADOWS_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    IGNOREPROJECTOR: False
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _GoochRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AdditionalLightsThreshold: 0.5
    - _AdvancedTab: 0
    - _ArtisticProjection: 0
    - _DstBlend: 10
    - _EnvironmentReflections: 2
    - _FresnelHighlights: 0
    - _GoochRampIntensity: 0.5
    - _Initialized: 0
    - _Light: 1
    - _LightBands: 4
    - _LightBandsScale: 0.5
    - _LightThreshold: 0.5
    - _LightTransmissionIntensity: 1
    - _LightTransmissionSmoothness: 0.5
    - _Mode: 3
    - _Outline: 3
    - _OutlineSize: 5
    - _OutlineTab: 0
    - _Smoothness: 0.5
    - _Specular: 1
    - _StylizeTab: 0
    m_Colors:
    - _AlbedoColor: {r: 0.8773585, g: 0.6493819, b: 0, a: 1}
    - _Color: {r: 0.8773585, g: 0.6493819, b: 0, a: 1}
    - _EmissionColor: {r: 0.074, g: 0.074, b: 0.074, a: 1}
    - _GoochBrightColor: {r: 1, g: 1, b: 1, a: 1}
    - _GoochDarkColor: {r: 0, g: 0, b: 0, a: 1}
    - _LightTransmissionColor: {r: 1, g: 0.65, b: 0, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecularColor: {r: 0.203125, g: 0.203125, b: 0.203125, a: 1}
    - _VertexAnimationFrequency: {r: 2.5, g: 2.5, b: 2.5, a: 1}
  m_BuildTextureStacks: []

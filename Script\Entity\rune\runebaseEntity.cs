using System;

namespace Excel.rune
{
	[Serializable]
	public class runeBaseEntity
	{
		/// <summary>
		/// 符文ID
		/// </summary>
		public int id;
		/// <summary>
		/// 名称
		/// </summary>
		public int name;
		/// <summary>
		/// 品质
		/// </summary>
		public int quality;
		/// <summary>
		/// 价格
		/// </summary>
		public int price;
		/// <summary>
		/// 标签
		/// </summary>
		public string label;
		/// <summary>
		/// 效果说明
		/// </summary>
		public int desc;
		/// <summary>
		/// 图标
		/// </summary>
		public string icon;
		/// <summary>
		/// 自动销毁类型
		/// </summary>
		public int destroyType;
		/// <summary>
		/// 销毁概率
		/// </summary>
		public int destroyPr;
		/// <summary>
		/// 是否可被复制
		/// </summary>
		public int copy;
		/// <summary>
		/// 是否实时刷新
		/// </summary>
		public int update;
		/// <summary>
		/// 效果目标ID
		/// </summary>
		public int targetId;
		/// <summary>
		/// 符文效果类型
		/// </summary>
		public int effectType;
		/// <summary>
		/// 效果参数1
		/// </summary>
		public string effectParam1;
		/// <summary>
		/// 效果参数2
		/// </summary>
		public string effectParam2;
		/// <summary>
		/// 效果参数3
		/// </summary>
		public string effectParam3;
		/// <summary>
		/// 效果参数4
		/// </summary>
		public string effectParam4;
	}
}

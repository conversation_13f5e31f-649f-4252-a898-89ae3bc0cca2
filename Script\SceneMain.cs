using System.Collections;
using System.Collections.Generic;
using Unity.AI.Navigation;
using Unity.Cinemachine;
using UnityEngine;
using UnityEngine.SceneManagement;

//public class BeastDomainBuilding
//{
//    public int InstanceID;
//    public float winTime;   
//    public List<BuildingBase> buildings = new List<BuildingBase>();
//}

public class SceneMain : SceneSingleton<SceneMain>
{
    [SerializeField]
    private NavMeshSurface m_navMeshSurfaceAlly;
    public NavMeshSurface NavMeshSurfaceAlly { get { return m_navMeshSurfaceAlly; } }
    [SerializeField]
    private NavMeshSurface m_navMeshSurfaceInvader;
    public NavMeshSurface NavMeshSurfaceInvader { get { return m_navMeshSurfaceInvader; } }

    [SerializeField]
    private NavMeshSurface m_navMeshSurfaceNeutral;
    public NavMeshSurface NavMeshSurfaceNeutral { get { return m_navMeshSurfaceNeutral; } }

    [SerializeField]
    private GameObject m_markArrow;
    public GameObject MarkArrow { get { return m_markArrow; } }

    [SerializeField]
    private CameraHeightControllerDOTween m_cameraHeightController;
    public CameraHeightControllerDOTween CameraHeightController { get { return m_cameraHeightController; } }

    private List<BeastDomain> m_beastDomainList = new List<BeastDomain>();
    public List<BeastDomain> beastDomainList { get { return m_beastDomainList; } }
    [SerializeField]
    private GameObject m_beastDomain;

    //private List<BeastDomainBuilding> m_beastDomainBuildingList = new List<BeastDomainBuilding>();

    protected override void Awake()
    {
        base.Awake();
        EventDispatcher.AddEventListener<BeastDomain>(EventDispatcherType.BeastGameWin, OnBeastGameWin);
    }

    private void OnEnable()
    {
        if(m_beastDomain != null)
        {
            m_beastDomainList.AddRange(m_beastDomain.GetComponentsInChildren<BeastDomain>());
            m_beastDomainList.RemoveAll(item => item == null);
        }
    }

    private void OnDisable()
    {
        EventDispatcher.RemoveEventListener<BeastDomain>(EventDispatcherType.BeastGameWin, OnBeastGameWin);
    }

    private void OnBeastGameWin(BeastDomain domain)
    {
        if (m_beastDomainList.Contains(domain))
        {
            m_beastDomainList.Remove(domain);
        }
    }


}

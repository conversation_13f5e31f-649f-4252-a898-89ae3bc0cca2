using Excel.hero;
using System.Collections;
using System.Collections.Generic;
using FSM;
using UnityEngine;
using UnityEngine.AI;
using TMPro;
using System;
using System.Linq;

public enum CampEnum
{
    Invader = 1, // 侵略者
    ///友好阵营
    Ally = 2,
    Neutral = 3, // 中立
}
/// <summary>
/// 1 = 停止移动
/// 2 = 停止所有技能与移动
/// 3 = 停止所有攻击与移动，包括普通攻击、技能、移动"
/// </summary>
public enum DisableActionEnum
{
    None = 0,
    DisableMove = 1,
    DisableSkill_Move= 2,
    DisableAttack_Skill_Move = 3,
}
public enum  State
{
    idle,
    walk,
    run,
    attack,
    death,
}

public enum AnimationType
{
    Idle    = 0,
    Walk    = 1,
    Run     = 2,
    Attack  = 3,
    Damage  = 4,
    Death   = 5,
    PreSkillCast    = 6,
    LoopSkillCast   = 7,
    PostSkillCast   = 8,
}

public class AnimationState
{
    public static int Idle = 0x1;
    public static int Walk = 0x2;
    public static int Run = 0x4;
    public static int Attack = 0x8;
    public static int Damage = 0x10;
    public static int Death = 0x20;
    public static int PreSkillCast = 0x40;
    public static int LoopSkillCast = 0x80;
    public static int PostSkillCast = 0x100;
}
public enum ActorEvent
{
    AttackFrame,
    AttackEnd,
    Collision
}


public enum CombatType
{
    All = -1,
    MeleeUnit = 1,
    RangedUnit = 2,
}

public enum FiveElementEnum
{
    None         = 0,
    Ice          = 1,
    Fire         = 2,
    Electricity  = 3,
    Wind         = 4,
    Darkness     = 5,
}

public enum MovementType
{
    None = 0,
    WaterUnit,
    GroundUnit,
    AirUnit,
}

public enum AnchorType
{
    /// <summary>
    /// 中心锚点
    /// </summary>
    Center = 1,
    /// <summary>
    /// 脚底锚点
    /// </summary>
    Buttom = 2,
    /// <summary>
    /// 头顶锚点
    /// </summary>
    Top = 3,
    /// <summary>
    /// 弹道锚点
    /// </summary>
    Bullet = 4,
}

public enum DamageType
{
    Normal,
    Skill,
    Buff,
}

public class Actor : GameEntity
{
    /// <summary>
    /// 可接受的距离误差
    /// </summary>
    public static float CastingRangeOffset = 0.1f;

    protected FiveElementEnum m_fiveElements;
    public FiveElementEnum FiveElements { get => m_fiveElements; set => m_fiveElements = value; }

    //protected NavMeshAgent m_navMeshAgent;
    //public NavMeshAgent navMeshAgent { get => m_navMeshAgent; }

    protected GridAgent m_navMeshAgent;
    public GridAgent navMeshAgent { get => m_navMeshAgent; set => m_navMeshAgent = value; }

    protected CapsuleCollider m_CapsuleCollider;
    private Rigidbody m_Rigidbody;

    public void SetCurrentHealth(float health)
    {
        m_currentHealth = health;
    }
    [SerializeField]
    protected BaseStateMachine m_StateMachine;
    public BaseStateMachine StateMachine { get => m_StateMachine; set => m_StateMachine = value; }

    public BaseState m_idleState;
    public BaseState m_skillCastState;
    public BaseState m_runToSkillTargetState;
    public BaseState m_rushState;
    public BaseState m_guardState;
    public BaseState m_deathState;
    public BaseState m_beatBackState;
    public BaseState m_polygonState;
    public BaseState m_runToDestinationState;

    protected AnimationType m_AnimationType;
    public virtual AnimationType AnimationType
    {
        get { return m_AnimationType; }
        set { m_AnimationType = value; }
    }

    protected float m_animationSpeed;
    public virtual float AnimationSpeed
    {
        get => m_animationSpeed; set => m_animationSpeed = value;
    }

    private float m_searchRange = 20;
    public float SearchRange { get => m_searchRange; }
    [SerializeField]
    protected GameEntity m_targetActor;
    public GameEntity TargetActor
    {
        get
        {
            return m_targetActor;
        }
        set
        {
            m_targetActor = value;
        }
    }
    //焦点建筑，主要目标是主城，找不到主城就找其他建筑
    protected GameEntity m_focusBuilding;
    /// <summary>
    /// 焦点建筑，主要目标是主城，找不到主城就找其他建筑
    /// </summary>
    public GameEntity FocusBuilding { get => m_focusBuilding; set => m_focusBuilding = value; }

    private bool m_isUnbeatable = false;
    public bool isUnbeatable { get => m_isUnbeatable; set => m_isUnbeatable = value; }

    private Vector3 m_guardPosition;
    public Vector3 guardPosition { get => m_guardPosition; set => m_guardPosition = value; }

    #region rush
    protected Vector3 m_rushDir;
    public Vector3 rushDir { get => m_rushDir; }

    protected float m_rushDistance;
    public float rushDistance { get => m_rushDistance; }

    protected float m_rushSpeed;
    public float rushSpeed { get => m_rushSpeed; }

    protected string m_rushEffect;
    public string rushEffect { get => m_rushEffect; }

    protected Skill m_rushSkill;
    public Skill rushSkill { get => m_rushSkill; }


    protected bool m_isRush;
    public bool isRush { get => m_isRush; set => m_isRush = value; }
    #endregion


    //是否跟随
    protected bool m_isFollow;
    public bool isFollow { get => m_isFollow; set => m_isFollow = value; }

    private bool m_isUnderControl;
    /// <summary>
    /// 被控制
    /// </summary>
    public bool isUnderControl { get => m_isUnderControl; set => m_isUnderControl = value; }
    /// <summary>
    /// 五行的伤害累积
    /// </summary>
    protected float[] m_AccumulateTotal = new float[6];
    protected float[] m_AccumulateTime = new float[6];

    //攻击动作的时长
    protected float m_attackClipLength = 1;

    public override DisableActionEnum disableAction
    {
        get => m_disableAction;
        set
        {
            m_disableAction = value;
            if (m_disableAction == DisableActionEnum.None)
            {
                this.MoveSpeed = m_originalMoveSpeed;
                m_navMeshAgent.speed = this.MoveSpeed;
            }
            else
            {
                this.MoveSpeed = 0;
                m_navMeshAgent.speed = 0;
            }
        }
    }
    public override float CastingRange
    {
        get
        {
            if (m_currentSkill != null)
                return m_currentSkill.CastingRange;
            if (m_normalSkill != null)
                return m_normalSkill.CastingRange;
            return m_castingRange;
        }
        set { m_castingRange = value; }
    }
    /// <summary>
    /// 保存ScriptableObject的实例
    /// </summary>
    Dictionary<ScriptableObject, ScriptableObject> m_clonedObjects = new Dictionary<ScriptableObject, ScriptableObject>();

    //移动方向
    public int horizontal;
    public int vertical;

    public override void Awake()
    {
        base.Awake();
        m_navMeshAgent = GetComponent<GridAgent>();
        if (m_navMeshAgent == null)
            m_navMeshAgent = gameObject.AddComponent<GridAgent>();

        m_StateMachine = new BaseStateMachine(this, m_navMeshAgent, m_idleState);

        m_CapsuleCollider = GetComponent<CapsuleCollider>();
        if (m_CapsuleCollider == null)
            m_CapsuleCollider = gameObject.AddComponent<CapsuleCollider>();
        m_CapsuleCollider.isTrigger = true;

        m_Rigidbody = GetComponent<Rigidbody>();
        if (m_Rigidbody == null)
            m_Rigidbody = gameObject.AddComponent<Rigidbody>();
        m_Rigidbody.isKinematic = true;

        //可以立即生效
        for (int i = 0; i < m_AccumulateTime.Length; i++)
            m_AccumulateTime[i] = -monsterConfig.accumulationCoolingTime;

    }

    public override void OnEnable()
    {

        //m_navMeshAgent.stoppingDistance = 0.1f;

        m_navMeshAgent.speed = this.MoveSpeed;

        base.OnEnable();
    }

    //public override void OnDisable()
    //{
    //    if( GameMain.Instance != null)
    //        GameMain.Instance.RemoveActor(this);
    //    base.OnDisable();
    //}


    public virtual void PlayIdle()
    {
        AnimationSpeed = 1;
        AnimationType = AnimationType.Idle;
    }
    public virtual void PlayRun()
    {
        AnimationType = AnimationType.Run;
    }
    public virtual void PlayWalk()
    {
        AnimationType = AnimationType.Walk;
    }

    public virtual void PlayAttack()
    {
        AnimationSpeed = 1;
        AnimationType = AnimationType.Attack;
    }

    public override void PlayDeath()
    {
        if (m_AnimationType == AnimationType.Death) return;
        AnimationSpeed = 1;
        AnimationType = AnimationType.Death;
    }

    public virtual void AttackEvent()
    {
        if (m_currentSkill != null)
            m_currentSkill.ApplyEffect();
        // m_normalAttackCount++;
    }

    public void OnAttackEnd()
    {

    }

    public virtual void StopCombat()
    {

    }

    private List<Skill> m_skillListCache = new List<Skill>();
    public List<Skill> skillListCache { get => m_skillListCache; }
    public override void Update()
    {
        base.Update();

        m_buffSystem.Update();
        m_skillEffectSystem.UpdateEffect();

        if (m_StateMachine != null)
            m_StateMachine.Update();

        if (isDeath && !m_isGhost)
        {
            OnDeath();
            return;
        }
        if (GameMain.Instance.gameStage != GameStage.Fight) return;
        //if (m_isGhost) return;

        //要放在m_StateMachine.Update()之后执行;
        if (m_currentSkill != null)
        {
            m_currentSkill.UpdateEffect();
        }

        //if (m_objectType != ObjectType.Role && (m_AnimationType != AnimationType.Run || m_AnimationType != AnimationType.Run))
        {
            for (int i = 0; i < m_skillList.Count; i++)
            {
                if (m_skillList[i].Priority == Skill.PriorityEnum.Normal) continue;

                if (!m_skillList[i].IsCompleteSkillEffect && m_skillList[i].IsTriggerEffect)
                {
                    m_skillList[i].UpdateEffect();
                    if (m_skillList[i].IsCompleteSkillEffect)
                    {
                        m_lastAttackTime = Time.time;
                        if (m_skillList[i] == m_normalSkill)
                            m_normalAttackCount++;
                    }
                }
                else
                {
                    if (!m_skillList[i].Enable) continue;
                    m_skillList[i].CheckTrigger(this);
                    m_skillList[i].Upate();
                    if (m_skillList[i].isTrigger)
                    {
                        GameEntity target = m_skillList[i].GetTarget();
                        if (target == null) continue;

                        transform.forward = (target.transform.position - transform.position).normalized;
                        m_skillList[i].ApplyEffect();
                    }
                }
            }
        }
        //UpdateFlocking();
    }


    public override void AttackResult(float damage)
    {
        m_buffSystem.AttackResult(damage);
    }
    public float GetAttributeValue()
    {
        return 0;
    }

    public override void DoRush(GameEntity target, Skill skill, float distance, float speed, string effectID)
    {
        m_rushDir = (target.transform.position - transform.position).normalized;


        m_rushDir.y    = 0;
        m_rushDistance = distance;
        m_rushSpeed    = speed;
        m_rushEffect   = effectID;
        m_rushSkill    = skill;

        transform.forward = m_rushDir;

        m_StateMachine.ChangeState(m_rushState);
    }

    public override void JumpTo(GameEntity target, float height, float time)
    {

    }

    //被击退
    public override void BeatBack(Vector3 dir, float distance, float speed, bool interrupting)
    {
        dir.y = 0;
        m_StateMachine.direction = dir;
        m_StateMachine.distance  = distance;
        m_StateMachine.speed     = speed;
        m_StateMachine.ChangeState(m_beatBackState);
    }

    public override void Pull(GameEntity target, Vector3 to, float speed, bool interrupting)
    {
    }

    public override void BeatFly(GameEntity target, float height, float airTime, float upSpeed, float downSpeed, bool interrupting)
    {

    }

    public override void ChangeAttribute(int attrID, float coefficient, int mode)
    {
        //1 = 效果目标自身的该项属性值 * 系数值
        //2 = 效果目标自身的该项属性值 + 系数值
        //当改变的属性类型为生命值时，是改变目标的生命上限，且改变后，若当前剩余的生命值超出生命上限，则将剩余的生命值自动降低到与生命上限持平，改变后的生命值上限最少为1

    }

    public void EnableAction()
    {
        disableAction = DisableActionEnum.None;
    }


    //public void MoveTo(Vector3 destination)
    //{
    //    if (Vector3.Distance(transform.position, destination) < 0.5f) return;
    //    m_StateMachine.Destination = destination;
    //    if( m_runState != null )
    //        m_StateMachine.ChangeState(m_runState);
    //    //m_StateMachine.chang
    //}

    public void Guard()
    {
        if (m_guardState != null)
        {
            m_guardPosition = transform.position;
            m_StateMachine.ChangeState(m_guardState);
        }
    }

    public void Chase()
    {
        if (m_guardState != null)
            m_StateMachine.ChangeState(m_guardState);
    }

    /// <summary>
    /// 技能前摇阶段
    /// </summary>
    public virtual void PlayPreSkillCast()
    {
        //float atkSpeed = Mathf.Max(AttackSpeed * (1 + AttackSpeedChange), fightConfig.atkSpeedA);
        float atkSpeed = Mathf.Max(AttackSpeed * (AttackSpeedChange), fightConfig.atkSpeedA);
        m_attackClipLength = Mathf.Max(1 / atkSpeed, fightConfig.atkSpeedB);

        if (m_currentSkill.Priority == Skill.PriorityEnum.Normal)
            AnimationSpeed = m_PreSkillCastAnimationLength / m_attackClipLength;
        else
            AnimationSpeed = 1;
        AnimationType = AnimationType.PreSkillCast;
    }

    /// <summary>
    /// 释放技能
    /// </summary>
    public virtual void SkillCast()
    {
        if (m_currentSkill != null)
        {
            m_currentSkill.ApplyEffect();
        }
    }
    /// <summary>
    /// 进入技能循环
    /// </summary>
    public virtual void PlayLoopSkillCast()
    {
        AnimationSpeed = 1;
        AnimationType = AnimationType.LoopSkillCast;
    }
    /// <summary>
    /// 技能循环阶段
    /// </summary>
    public void SkillCastLoop()
    {
    }
    /// <summary>
    /// 技能后摇阶段
    /// </summary>
    public virtual void PlayPostSkillCast()
    {
        AnimationSpeed = 1;
        AnimationType = AnimationType.PostSkillCast;
    }

    public void SkillCastFinish()
    {
        m_IsInSkillCast = false;
    }

    public float lastNormalizedTime = 0;
    public virtual bool isCurrentAnimationFinish()
    {
        //AnimatorStateInfo stateInfo = m_Animator.GetCurrentAnimatorStateInfo(0);
        ////if (stateInfo.IsName(name))
        //{
        //    float normalizedTime = stateInfo.normalizedTime % 1.0f;

        //    if (lastNormalizedTime > 0.9f && normalizedTime < 0.2f)
        //        return true;

        //    lastNormalizedTime = normalizedTime;
        //}
        return false;
    }



    public override bool CannotBeSelected { get => m_buffSystem.CannotBeSelected; }

    public void OnCollisionEnter(Collision collision)
    {
    }

    public virtual void OnTriggerEnter(Collider other)
    {
        m_StateMachine.OnTriggerEnter(other);
    }

    public virtual void TestSkill(int id) { }

    public virtual bool UseSkill() { return false; }

    public virtual bool CheckSkill()
    {
        if (m_currentSkill != null) return false;

        //检测是否有被动技能可以使用
        if (isDisableSkill) return false;

        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (!m_skillList[i].Enable) continue;
            m_skillList[i].Upate();
        }

        m_skillListCache.Clear();

        for (int i = 0; i < m_skillList.Count; i++)
        {
            //if (m_skillList[i].skillShow == null) continue;
            if (m_skillList[i].isTrigger && m_skillList[i].SkillTriggerType == Skill.SkillTriggerEnum.Passive)
                m_skillListCache.Add(m_skillList[i]);
        }
        if (m_skillListCache.Count > 0)
        {
            m_skillListCache.Sort((left, right) => right.Priority.CompareTo(left.Priority));

            m_currentSkill = m_skillListCache[0];
            return true;
        }
        return false;
    }

    /// <summary>
    /// 选中技能后，生成目标列表，选中最近目标，根据目标距离进入不同状态
    /// </summary>
    public bool EnterSkillState()
    {
        GameEntity target = m_currentSkill.GetTarget();
        if (target == null)
        {
            m_currentSkill = null;
            return false;
        }

        m_targetActor = target;
        AddThreat(target, false);

        m_IsInSkillCast = true;

        if (target != this)
        {

            Vector3 dir = (target.transform.position - transform.position).normalized;
            transform.forward = dir;

            //float distance = Vector3.Distance(transform.position, target.transform.position);
            //if (distance > m_currentSkill.CastingRange)
            //{
            //    distance = (distance - m_currentSkill.CastingRange);
            //    Vector3 destination = transform.position + dir * distance;

            //    m_StateMachine.Destination = destination;
            //    m_StateMachine.ChangeState(m_runToSkillTargetState);
            //}
            //else
            m_StateMachine.ChangeState(m_skillCastState);
        }
        else
            m_StateMachine.ChangeState(m_skillCastState);

        return true;
    }

    public void ChangeColor(Color color)
    {
        Renderer[] allRenderer = GetComponentsInChildren<Renderer>();
        for (int i = 0; i < allRenderer.Length; i++)
        {
            // 排除掉layer是minimap的Renderer
            if (allRenderer[i].gameObject.layer != LayerMask.NameToLayer("minimap")) {
                allRenderer[i].material.color = color;
            }
        }
    }

    public virtual void AddFollower(Actor actor) { }
    public virtual void AddElf(Elf Elf) { }

    public override void OnDeath()
    {
        if (m_isGhost) return;
        //ClearSkill();
        m_isGhost = true;
        EventDispatcher.TriggerEvent(EventDispatcherType.ActorDeath, this);
        //播放死亡动画
        m_StateMachine.ChangeState(m_deathState);
        //PlayDeath();
        m_deathTime = Time.time;
        //base.OnDeath();
        m_navMeshAgent.ClearTempObstacles();

        m_CapsuleCollider.enabled = false;
    }

    public void GameEnd()
    {
        m_StateMachine.ChangeState(m_idleState);
    }

    public override void ReplaceSkill(int oldSkillID, int newSkillID)
    {
        if (m_currentSkill != null && m_currentSkill.SkillID == oldSkillID)
        {
            m_StateMachine.ChangeState(m_idleState);
        }
        base.ReplaceSkill(oldSkillID, newSkillID);
    }
    /// <summary>
    /// 设置技能各个阶段的播放速度，达到整个技能播放时间符合攻击速度
    /// </summary>
    public virtual void SetSkillCast()
    {

    }
    public override void OnDisable()
    {
        m_StateMachine.Stop();
        for (int i = 0; i < m_clonedObjects.Values.Count; i++)
        {
            ScriptableObject.Destroy(m_clonedObjects.Values.ElementAt(i));
        }
        m_clonedObjects.Clear();

        base.OnDisable();
    }
    //void OnDestroy()
    //{
    //    EventDispatcher.TriggerEvent(EventDispatcherType.ActorDeath, this);
    //}

    public void Teleport(Vector3 target, float delay)
    {
        StartCoroutine(TeleportCoroutine(target, delay));
    }

    public IEnumerator TeleportCoroutine(Vector3 target, float delay)
    {
        yield return new WaitForSeconds(delay);
        transform.position = target;

        MainBase.Instance.CurrentHealth += (AttackDamage * 8);

        Vector3 pos = MainBase.Instance.GetAnchor(AnchorType.Top).position;
        FloatTextManager.Instance.Add((AttackDamage * 8).ToString(), pos, 3, Color.green);
    }

    public virtual Actor Clone(float percent, string skillID, float cloneDuration)
    {
        GameObject clone = GameObject.Instantiate(gameObject);

        Actor cloneActor = clone.GetComponent<Actor>();
        cloneActor.skillList.Clear();
        cloneActor.buffSystem.Clear();
        cloneActor.skillEffectSystem.Clear();
        cloneActor.skillListCache.Clear();
        cloneActor.threatList.Clear();

        cloneActor.originalHealth        = m_originalHealth;
        cloneActor.originalArmorBreak    = m_originalArmorBreak;
        cloneActor.originalDefense       = m_originalDefense;
        cloneActor.originalAttackDamage  = m_originalAttackDamage;
        cloneActor.originalAttackPercent = m_originalAttackPercent;
        cloneActor.originalHealthPercent = m_originalHealthPercent;
        cloneActor.originalAttackSpeed   = m_originalAttackSpeed;
        cloneActor.originalMoveSpeed     = m_originalMoveSpeed;
        cloneActor.originalCoolingReduced= m_originalCoolingReduced;
        cloneActor.originalDamageBonus   = m_originalDamageBonus;
        cloneActor.originalDamageReduced = m_originalDamageReduced;
        cloneActor.originalAttackRange   = m_originalAttackRange;

        percent += 1;
        cloneActor.AttributeEnhancement(percent);

        cloneActor.normalAttackCount = 0;
        cloneActor.killCount         = 0;
        cloneActor.hitCount          = 0;
        cloneActor.isFollow          = false;
        cloneActor.isDeath           = false;
        cloneActor.isRush            = false;
        cloneActor.isClone           = true;
        cloneActor.currentSkill      = null;

        cloneActor.disableAction    = DisableActionEnum.None;
        cloneActor.TargetActor      = null;
        cloneActor.gameObject.layer = Utility.layerAlly;

        if (cloneActor is Role)
        {
            cloneActor.LoadState();
            ((Role) cloneActor).RemoveAllEvent();
        }

        string[] skillArray = skillID.Split("|");
        for (int i = 0; i < skillArray.Length; i++)
        {
            cloneActor.AddSkill(int.Parse(skillArray[i]));
        }

        GameMain.Instance.AddActor(cloneActor);

        cloneActor.StartCloneDuration(cloneDuration);
        return cloneActor;
    }


    public void LoadState()
    {
        BaseState idleState             = Resources.Load<FSM.State>("StateMachine\\Monster\\Idle State");
        BaseState skillCastState        = Resources.Load<FSM.State>("StateMachine\\Monster\\Skill Cast State");
        BaseState runToDestinationState = Resources.Load<FSM.State>("StateMachine\\Monster\\Run To Destination State");
        BaseState runToSkillTargetState = Resources.Load<FSM.State>("StateMachine\\Monster\\Run To Skill Target State");
        BaseState rushState             = Resources.Load<FSM.State>("StateMachine\\Monster\\Rush State");
        BaseState deathState            = Resources.Load<FSM.State>("StateMachine\\Monster\\Death State");
        BaseState beatBackState         = Resources.Load<FSM.State>("StateMachine\\Monster\\Beat Back State");
        BaseState polygonState          = Resources.Load<FSM.State>("StateMachine\\Monster\\Polygon State");
        m_clonedObjects.Clear();
        m_idleState             = ScriptableObjectUtility.DeepCopy(idleState, m_clonedObjects);
        m_skillCastState        = ScriptableObjectUtility.DeepCopy(skillCastState, m_clonedObjects);
        m_runToDestinationState = ScriptableObjectUtility.DeepCopy(runToDestinationState, m_clonedObjects);
        m_runToSkillTargetState = ScriptableObjectUtility.DeepCopy(runToSkillTargetState, m_clonedObjects);
        m_rushState             = ScriptableObjectUtility.DeepCopy(rushState, m_clonedObjects);
        m_deathState            = ScriptableObjectUtility.DeepCopy(deathState, m_clonedObjects);
        m_beatBackState         = ScriptableObjectUtility.DeepCopy(beatBackState, m_clonedObjects);
        m_polygonState          = ScriptableObjectUtility.DeepCopy(polygonState, m_clonedObjects);

        m_StateMachine.ChangeState(m_idleState);

    }

    public void StartCloneDuration(float duration)
    {
        StartCoroutine(CloneDurationCoroutine(duration) );
    }
    IEnumerator CloneDurationCoroutine(float duration)
    {
        yield return new WaitForSeconds(duration);
        GameMain.Instance.RemoveActor(this);
        Destroy(this.gameObject);
    }

}

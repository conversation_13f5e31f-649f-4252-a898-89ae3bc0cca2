using Excel.hero;
using Excel.map;
using Excel.monster;
using Excel.pet;
using System.Collections;
using System.Collections.Generic;
using Unity.Cinemachine;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.SceneManagement;
using Excel.shop;
using Excel.rune;

public enum GameStage
{
    /// <summary>
    /// 建造阶段
    /// </summary>
    Build,
    /// <summary>
    /// 战斗阶段
    /// </summary>
    Fight,
    /// <summary>
    /// 游戏结束
    /// </summary>
    GameEnd,
}
public class GameMain : Singleton<GameMain>
{
    private int m_heroID = 101;
    public int HeroID { get => m_heroID; set => m_heroID = value; }

    [SerializeField]
    private List<Actor> m_InvaderList = new List<Actor>();
    public List<Actor> InvaderList { get => m_InvaderList; }
    private List<Actor> m_AllyList = new List<Actor>(); // 友军 + 中立
    public List<Actor> mAllyList { get => m_AllyList; }
    public List<Actor> AllyList { get => m_AllyList.FindAll(x => x.Camp == CampEnum.Ally); } // 友军
    public List<Actor> NeutralList { get => m_AllyList.FindAll(x => x.Camp == CampEnum.Neutral); } // 中立
    private List<Elf> m_elfList = new List<Elf>();
    public List<Elf> elfList { get => m_elfList; }
    /// <summary>
    /// 已经建成的建筑列表
    /// </summary>
    public List<BuildingBase> m_listCompletedBuildings = new List<BuildingBase>();
    /// <summary>
    /// 已经建成的建筑列表
    /// </summary>
    public List<BuildingBase> ListCompletedBuildings { get => m_listCompletedBuildings; set => m_listCompletedBuildings = value; }

    public static string[] wuxing_icon = { "", "Icon\\wuxing\\icon_jin", "Icon\\wuxing\\icon_mu", "Icon\\wuxing\\icon_shui", "Icon\\wuxing\\icon_huo", "Icon\\wuxing\\icon_tu" };

    private Role m_currentRole;
    public Role Role { get => m_currentRole; set => m_currentRole = value; }

    private Actor m_currentControlActor;
    public Actor currentControlActor { get => m_currentControlActor; set => m_currentControlActor = value; }

    protected int m_currentTurn;
    /// <summary>
    /// 当前回合
    /// </summary>
    public int CurrentTurn { get => m_currentTurn; set => m_currentTurn = value; }
    /// <summary>
    /// 是否生成完成
    /// </summary>
    protected bool m_isSpawnComplete;
    protected bool m_isLastRound = false;

    private int m_jadeTotal;
    /// <summary>
    /// 灵玉总和
    /// </summary>
    public int jadeTotal { 
        get {
            return m_jadeTotal;
        } 
        set {
            m_jadeTotal = value;
            // 更新UI
            EventDispatcher.TriggerEvent(EventDispatcherType.UpdateJadeTotal, m_jadeTotal);
        } 
    }

    private int m_goldTotal;
    /// <summary>
    /// 金币总和
    /// </summary>
    public int goldTotal {
        get {
            return m_goldTotal;
        }
        set {
            m_goldTotal = value;
            // 更新UI
            EventDispatcher.TriggerEvent(EventDispatcherType.UpdateGoldTotal, m_goldTotal);
        }
    }

    private float m_startFightTime;

    public GameStage m_gameStage;
    public GameStage gameStage { get => m_gameStage; set => m_gameStage = value; }

    public static InventoryManager inventoryManager = null;

    private int mapId = 100014; //地图id

    //private List<petBaseEntity> m_listPetBaseEntity = new List<petBaseEntity>();
    // 灵兽
    public static List<petBaseEntity> ListPetBaseEntity = new List<petBaseEntity>();
    // // 符文
    // public List<runeBaseEntity> m_listRuneBase = new List<runeBaseEntity>();
    // // 宝石
    // public List<GemItem> m_listGemBase = new List<GemItem>();
    // // 精华
    // public List<GemItem> m_listEssenceBase = new List<GemItem>();
    // public List<PetItemEntity> m_listPetBase = new List<PetItemEntity>();
    // 技能
    public List<heroSkillEntity> m_listSkillBase = new List<heroSkillEntity>();
    // 法宝|阵法
    public List<heroSkillEntity> m_disposableSkills = new List<heroSkillEntity>();
    // // 秘宝包
    // public List<treasurePoolEntity> m_listTreasurePool = new List<treasurePoolEntity>();
    // // 卷轴包
    // public List<scrollPoolEntity> m_listScrollPool = new List<scrollPoolEntity>();
    // public List<GameObject> bottomViewList = new List<GameObject>();

    public bool isBeastRound = false;

    public override void Awake()
    {
        SceneManager.sceneLoaded += SceneLoadedCallback;

        EventDispatcher.AddEventListener<Actor>(EventDispatcherType.SelectActor, OnSelectActor);
        EventDispatcher.AddEventListener<BuildingBase>(EventDispatcherType.BuildComplete, OnBuildComplete);
        EventDispatcher.AddEventListener(EventDispatcherType.SpawnComplete, OnSpawnComplete);
        EventDispatcher.AddEventListener<GameEntity>(EventDispatcherType.ActorDeath, OnActorDeath);
        EventDispatcher.AddEventListener<int>(EventDispatcherType.JadeIncome, OnJadeIncome);
        EventDispatcher.AddEventListener(EventDispatcherType.StartFight, OnStartFight);
        EventDispatcher.AddEventListener(EventDispatcherType.LastRound, OnLastRound);

        EventDispatcher.AddEventListener(EventDispatcherType.GameLose, OnGameEnd);
        EventDispatcher.AddEventListener(EventDispatcherType.GameWin, OnGameEnd);

        EventDispatcher.AddEventListener<BuildingBase>(EventDispatcherType.BuildingDestroy, OnBuildingDestroy);
        EventDispatcher.AddEventListener(EventDispatcherType.BeastDomainStartFight, OnBeastDomainStartFight);
        EventDispatcher.AddEventListener(EventDispatcherType.BeastDomainEndFight, OnBeastDomainEndFight);
        monsterConfig.Init();
        inventoryManager = new InventoryManager();
    }
    private void OnDestroy()
    {
        isDestroy = true;
        if (m_currentRole != null)
        {
            Destroy(m_currentRole.gameObject);
            m_currentRole = null;
        }
    }
    //临时代码
    IEnumerator EnterGame()
    {
        yield return new WaitForSeconds(1);
        EventDispatcher.TriggerEvent(EventDispatcherType.MainBaseUpgrade, 1);
        initMapJad();
    }
    public void AddActor(Actor actor)
    {
        if (actor.Camp == CampEnum.Invader )
            m_InvaderList.Add(actor);
        else if (actor.Camp == CampEnum.Ally || actor.Camp == CampEnum.Neutral )
            m_AllyList.Add(actor);
    }

    public void RemoveActor(Actor actor)
    {
        if (actor.Camp == CampEnum.Invader)
            m_InvaderList.Remove(actor);
        else if (actor.Camp == CampEnum.Ally)
        {
            m_AllyList.Remove(actor);

            if( actor is Elf)
                m_elfList.Remove((Elf)actor);
        }
    }
    public GameEntity GetClosestActor(GameEntity self, List<GameEntity> listActor,Vector3 position, float SearchRange)
    {
        float min_distance = 99999;
        float distance = 0;
        GameEntity closestActor = null;
        for (int i = 0; i < listActor.Count; i++)
        {
            if (listActor[i] == self) continue; // Skip self
            if (listActor[i].isDeath) continue;
            if (Utility.IsBlockedByBuilding(self, listActor[i])) continue;

            //己方可以选中那些被禁止选中的
            if ( self.Camp != listActor[i].Camp && listActor[i].CannotBeSelected) continue;

            distance = Vector3.Distance(position, listActor[i].transform.position) - listActor[i].Radius;
            if ((distance - SearchRange) > Actor.CastingRangeOffset) continue;
            if (distance < min_distance)
            {
                min_distance = distance;
                closestActor = listActor[i];
            }
        }
        return closestActor;
    }

    List<GameEntity> m_tempList = new List<GameEntity>();


    public GameEntity GetClosestEnemy(GameEntity actor, float SearchRange, Vector3 position)
    {
        m_tempList.Clear();
        if (actor.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(AllyList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Ally);
            m_tempList.AddRange(filter);
        }
        else if (actor.Camp == CampEnum.Ally)
        {
            m_tempList.AddRange(m_InvaderList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Invader);
            m_tempList.AddRange(filter);
        }

        return GetClosestActor(actor, m_tempList, position, SearchRange);
    }
    public GameEntity GetClosestEnemy(GameEntity self,float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(AllyList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Ally);
            m_tempList.AddRange(filter);
        }
        else if (self.Camp == CampEnum.Ally)
        {
            m_tempList.AddRange(m_InvaderList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Invader);
            m_tempList.AddRange(filter);
        }

        GameEntity threat = self.GetHighestThreatTarget();
        if (threat != null)
            m_tempList.Add(threat); //return threat;

        return GetClosestActor(self, m_tempList, self.transform.position, SearchRange);
    }

    public GameEntity GetClosestEnemyWithoutThreat(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(AllyList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Ally));
        }
        else if (self.Camp == CampEnum.Ally) {
            m_tempList.AddRange(m_InvaderList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Invader));
        }

        return GetClosestActor(self, m_tempList, self.transform.position, SearchRange);
    }


    public GameEntity GetClosestFriend(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Invader)
            m_tempList.AddRange(AllyList);
        else
            m_tempList.AddRange(m_InvaderList);

        return GetClosestActor(self, m_tempList, self.transform.position, SearchRange);
    }

    public List<GameEntity> GetFriend(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(m_InvaderList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Invader));
        }
        else
        {
            m_tempList.AddRange(AllyList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Ally));
        }

        List<GameEntity> actors = new List<GameEntity>();
        float distance = 0;
        for (int i = 0; i < m_tempList.Count; i++)
        {
            if (m_tempList[i] == self) continue;
            GameEntity actor = m_tempList[i];
            distance = Vector3.Distance(self.transform.position, actor.transform.position) - actor.Radius;
            if ((distance - SearchRange) < Actor.CastingRangeOffset)
                actors.Add(actor);
        }
        return actors;
    }

    public List<GameEntity> GetEnemy(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Ally)
        {
            m_tempList.AddRange(m_InvaderList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Invader));
        }
        else
        {
            m_tempList.AddRange(AllyList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item => item.Camp == CampEnum.Ally));
        }

        List<GameEntity> actors = new List<GameEntity>();
        float distance = 0;
        for (int i = 0; i < m_tempList.Count; i++)
        {
            if (m_tempList[i] == self) continue;
            GameEntity actor = m_tempList[i];
            distance = Vector3.Distance(self.transform.position, actor.transform.position) - actor.Radius;
            if ((distance - SearchRange) < Actor.CastingRangeOffset)
                actors.Add(actor);
        }
        return actors;
    }
    public Actor GetLowHealthActor(GameEntity self, List<Actor> listActor)
    {
        float min_health = 99999;
        float health = 0;
        Actor lowHealthActor = null;
        for (int i = 0; i < listActor.Count; i++)
        {
            if (listActor[i] == self) continue; // Skip self
            if (listActor[i].isDeath) continue;

            //己方可以选中那些被禁止选中的
            if (self.Camp != listActor[i].Camp && listActor[i].CannotBeSelected) continue;

            health = listActor[i].CurrentHealth;
            if (health < min_health)
            {
                min_health = health;
                lowHealthActor = listActor[i];
            }
        }
        return lowHealthActor;
    }

    public Actor GetFriendLowHealthActor(GameEntity self)
    {
        List<Actor> listActor;
        if (self.Camp == CampEnum.Invader)
            listActor = m_InvaderList;
        else
            listActor = AllyList;
        return GetLowHealthActor(self, listActor);
    }

    public GameEntity GetClosestGameEntity(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Ally)
        {
            m_tempList.AddRange(m_InvaderList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Invader);
            m_tempList.AddRange(filter);
        }
        else if (self.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(AllyList);
            var filter = m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Ally);
            m_tempList.AddRange(filter);
        }
        float minDistance = float.MaxValue;
        GameEntity closestEntity = null;

        GameEntity entity = null;
        for(int i = 0; i < m_tempList.Count; i++)
        {
            entity = m_tempList[i];
            if (entity == self) continue; // Skip self
            if (entity.isDeath) continue;
            //己方可以选中那些被禁止选中的
            if (self.Camp != entity.Camp && entity.CannotBeSelected) continue;

            float distance = Vector3.Distance(self.transform.position, entity.transform.position) - entity.Radius;
            if ((distance - SearchRange) < Actor.CastingRangeOffset && distance < minDistance)
            {
                minDistance = distance;
                closestEntity = entity;
            }
        }

        return closestEntity;
    }
    public GameEntity GetBestTarget(GameEntity self, float SearchRange)
    {
        m_tempList.Clear();
        if (self.Camp == CampEnum.Invader)
        {
            m_tempList.AddRange(AllyList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Ally));
        }
        else if (self.Camp == CampEnum.Ally)
        {
            m_tempList.AddRange(m_InvaderList);
            m_tempList.AddRange(m_listCompletedBuildings.FindAll(item=> item.Camp == CampEnum.Invader));
        }

        //GameEntity threat = self.GetHighestThreatTarget();
        //if (threat != null)
        //    m_tempList.Add(threat); //return threat;
        
        float maxScore = float.MinValue;
        GameEntity bestTarget = null;
        float minDistance = float.MaxValue;
        GameEntity closestEntity = null;

        for (int i = 0; i < m_tempList.Count; i++)
        {
            GameEntity entity = m_tempList[i];
            if ( entity.isDeath) continue;
            if (self.Camp == entity.Camp || entity.CannotBeSelected) continue;
            if (Utility.IsBlockedByBuilding(self, m_tempList[i])) continue;
            float distance = Vector3.Distance(self.transform.position, entity.transform.position) - entity.Radius;
            if (distance - SearchRange > Actor.CastingRangeOffset) continue;

            // 计算目标价值分数（价值/距离）
            float score = entity.TargetValue / (distance + 0.1f); // 加0.1防止除零
            
            // 同时记录最近目标作为备选
            if (distance < minDistance)
            {
                minDistance = distance;
                closestEntity = entity;
            }

            // 选择分数最高的目标
            if (score > maxScore)
            {
                maxScore = score;
                bestTarget = entity;
            }
        }

        // 优先返回高价值目标，其次返回最近目标
        return bestTarget ?? closestEntity;
    }

    private void SceneLoadedCallback(Scene scene, LoadSceneMode mode)
    {
        if(scene.name != "InitialScene" && scene.name != "Start")
        {
            GameObject goBornPoint = GameObject.Find("BornPoint");

            if (goBornPoint != null) 
            {
                m_currentRole = CreateHero(HeroID, goBornPoint.transform.position);
                m_currentControlActor = m_currentRole;
                m_currentControlActor.isUnderControl = true;
            }
            
            m_currentTurn = 1;
            StartCoroutine(EnterGame());
        }
    }

    static int RuntimeID = 100;
    private Role CreateHero(int id, Vector3 bornPoint)
    {
        m_heroID = id;
        herobaseEntity entity = ExcelData.Instance.GetHeroBase(id);
        GameObject prefab = Resources.Load( entity.model ) as GameObject;
        GameObject goHero = GameObject.Instantiate( prefab,bornPoint, Quaternion.identity );
        goHero.layer = LayerMask.NameToLayer("Player");

        //NavMeshAgent agent = goHero.GetComponent<NavMeshAgent>();
        //agent.agentTypeID = SceneMain.Instance.NavMeshSurfaceAlly.agentTypeID;
        //agent.Warp(bornPoint);
        //goHero.transform.position = bornPoint;

        Role role = goHero.GetComponent<Role>();
        role.SetData(entity);

        GameObject goCamera = GameObject.Find("RoleCinemachineCamera");
        CinemachineCamera cam = goCamera.GetComponent<CinemachineCamera>();

        cam.Target.TrackingTarget = goHero.transform;

        m_AllyList.Add(role);

        EventDispatcher.TriggerEvent(EventDispatcherType.CreateHero, goHero);

        return role;
    }

    public Monster CreateMonster(int id, CampEnum camp, Vector3 bornPoint)
    {
        monsterBaseEntity entiy = ExcelData.Instance.GetMonster(id);
        if (entiy == null) return null;
        
        GameObject prefab = Resources.Load<GameObject>(entiy.model);
        GameObject goMonster = GameObject.Instantiate( prefab ,bornPoint, Quaternion.identity);

        //NavMeshAgent agent = goMonster.GetComponent<NavMeshAgent>();

        Monster monster = goMonster.GetComponent<Monster>();
        monster.SetData(entiy);
        monster.Camp = camp;

        monster.name = camp.ToString() + id.ToString() + " " + RuntimeID;
        RuntimeID++;
    
        if (camp == CampEnum.Invader)
        {
            goMonster.layer = LayerMask.NameToLayer("Invader");
            //agent.agentTypeID = SceneMain.Instance.NavMeshSurfaceInvader.agentTypeID;
            monster.ObjectType = ObjectType.Monster;
            m_InvaderList.Add(monster);
        }
        // 创建我方卫兵
        else if (camp == CampEnum.Ally)
        {
            goMonster.layer = LayerMask.NameToLayer("Ally");
            //agent.agentTypeID = SceneMain.Instance.NavMeshSurfaceAlly.agentTypeID;
            monster.ObjectType = ObjectType.Soldier;
            m_AllyList.Add(monster);
        } else if (camp == CampEnum.Neutral)
        {
            goMonster.layer = LayerMask.NameToLayer("Neutral");
            //agent.agentTypeID = SceneMain.Instance.NavMeshSurfaceAlly.agentTypeID;
            monster.ObjectType = ObjectType.Monster;
            m_AllyList.Add(monster);
        }
        return monster;
    }

    public Elf CreateElf(petBaseEntity entiy, petLvEntity lvEntity, CampEnum camp, Vector3 bornPoint)
    {
        GameObject prefab = Resources.Load<GameObject>(entiy.unitModel);
        GameObject goElf = GameObject.Instantiate(prefab);
        goElf.layer = LayerMask.NameToLayer("Ally");

        NavMeshAgent agent = goElf.GetComponent<NavMeshAgent>();
        agent.agentTypeID = SceneMain.Instance.NavMeshSurfaceAlly.agentTypeID;
        agent.Warp(bornPoint);

        Elf elf = goElf.GetComponent<Elf>();
        elf.SetData(entiy, lvEntity);
        elf.spawnTime = Time.time;
        elf.Camp = camp;
        //elf.name = "Elf " + ID.ToString();
        RuntimeID++;

        //if (camp == CampEnum.Monster)
        //    m_monsterList.Add(elf);
        //else if (camp == CampEnum.Friend)
            m_AllyList.Add(elf);
        m_elfList.Add(elf);
        return elf;
    }

    void OnSelectActor(Actor actor)
    {
        if(m_currentControlActor == actor ) return;

        if( actor is Elf)
            m_currentRole.RemoveElf((Elf)actor);

        m_currentControlActor.isUnderControl = false;
        m_currentControlActor = actor;
        m_currentControlActor.isUnderControl = true;

        GameObject goCamera = GameObject.Find("RoleCinemachineCamera");
        CinemachineCamera cam = goCamera.GetComponent<CinemachineCamera>();

        cam.Target.TrackingTarget = actor.transform;
    }

    void OnBuildComplete(BuildingBase building)
    {
        m_listCompletedBuildings.Add(building);
    }

    void OnStartFight()
    {
        m_startFightTime = Time.time;

        Role.Instance.StartFight();
        m_gameStage = GameStage.Fight;
    }

    void OnGameEnd()
    {
        if(isBeastRound) 
        {
            clearInvader();
            return;
        };
        m_gameStage = GameStage.GameEnd;

        for(int i = 0; i < m_AllyList.Count; i++)
            m_AllyList[i].GameEnd();

        for(int i = 0; i < m_InvaderList.Count; i++)
            m_InvaderList[i].GameEnd();
    }

    void OnLastRound()
    {
        m_isLastRound = true;
    }

    void OnSpawnComplete()
    {
        m_isSpawnComplete = true;
    }

    void OnActorDeath(GameEntity actor)
    {
        if (actor.Camp == CampEnum.Invader)
        {
            m_InvaderList.Remove((Actor)actor);
        }
        else
        {
            m_AllyList.Remove((Actor)actor);
            if( actor is Elf)
                m_elfList.Remove((Elf)actor);
        }
        // 野兽区域调整
        if(isBeastRound && m_InvaderList.Count <= 0)
        {
            EventDispatcher.TriggerEvent(EventDispatcherType.InvadersIsClear);
            return;
        }

        if( m_isSpawnComplete && m_InvaderList.Count == 0 )
        {
            // 结算每回合获取的灵玉
            getRoundJade();
            StartCoroutine(NextTurn());
        }
        // 最后一回合的怪物打完 或者 野兽区域的怪物打完
        if( m_isLastRound && m_InvaderList.Count == 0)
        {
            EventDispatcher.TriggerEvent(EventDispatcherType.GameWin);
        }
    }

    void OnBuildingDestroy(GameEntity building)
    {
        //m_buildList.Remove((BuildingBase)building);

        //删除所有怪物的mainbase
        for (int i = 0; i < m_InvaderList.Count; i++)
        {
            if( m_InvaderList[i].FocusBuilding == building)
            {
                m_InvaderList[i].FocusBuilding = null;
            }
        }
    }

    public void OnBeastDomainStartFight()
    {
        gameStage = GameStage.Fight;
    }
    public void OnBeastDomainEndFight()
    {
        gameStage = GameStage.Build;
    }
    private void getRoundJade() {
        List<roundMonsterEntity> list = ExcelData.Instance.MapExcel.roundMonsterList;
        if(list.Count == 0) return;
        // 去表里查询回合结算的灵玉数量
        roundMonsterEntity data = list.Find(val => val.round == CurrentTurn);
        if (data == null) return;
        // 累加灵玉
        OnJadeIncome(data.coinNum);
    }
    // 下回合
    IEnumerator NextTurn()
    {
        yield return new WaitForSeconds(1);
        m_isSpawnComplete = false;
        m_gameStage = GameStage.Build;
        m_currentTurn++;
        EventDispatcher.TriggerEvent(EventDispatcherType.NextTurn);
        OnNextTurn();
    }

    private void OnNextTurn()
    {
        StopAllCoroutines();
        Role.Instance.Renew();
        for(int i = 0; i < m_AllyList.Count; i++) 
            m_AllyList[i].Renew();
        for(int i = 0; i < m_listCompletedBuildings.Count; i++)
            m_listCompletedBuildings[i].Renew();


        //限定时间内完成回合挑战结算时额外获得金币
        goldTotal += RuneSystem.Instance.getTimedTurnChallengeGold( Time.time - m_startFightTime);

        RuneSystem.Instance.NextTurn();
    }



    void OnJadeIncome(int count)
    {
        EventDispatcher.TriggerEvent(EventDispatcherType.UpdateJadeTotal, m_jadeTotal);
        jadeTotal += count;
    }

    public void Restart()
    {
        m_isLastRound = false;
        m_isSpawnComplete = false;
        m_currentTurn = 1;
        m_jadeTotal = 0;
        m_gameStage = GameStage.Build;
        m_AllyList.Clear();
        m_listCompletedBuildings.Clear();
        m_elfList.Clear();
        m_InvaderList.Clear();
        ListPetBaseEntity.Clear();
        SceneManager.LoadScene(1);
    }

    public void clearInvaderList()
    {
        for (int i = 0; i < m_InvaderList.Count; i++)
        {
            m_InvaderList[i].Destroy();
        }
        m_InvaderList.Clear();
    }

    public void clearInvader() {
        m_InvaderList.Clear();
    }

    List<Actor> GetActorsList(CampEnum camp)
    {
        switch (camp) {
            case CampEnum.Invader:
                return m_InvaderList;
            case CampEnum.Ally:
            case CampEnum.Neutral:
                return m_AllyList;
            default:
                return this.AllyList;
        }
    }

    public void changeActorCamp(Actor actor, CampEnum camp)
    {
        CampEnum oldCamp = actor.Camp;
        if (oldCamp == camp) return;
        List<Actor> oldList = GetActorsList(oldCamp);
        List<Actor> newList = GetActorsList(camp);
        actor.Camp = camp;
        if(oldList == newList) return;
        oldList.Remove(actor);
        newList.Add(actor);
    }

    private void initMapJad()
    {
        int id = mapId;
        // 获取地图的数据信息，读表。拿到灵玉数量初始化
        int jadeCount = 0;
        mapEntity data = ExcelData.Instance.MapExcel.mapList.Find(val => val.name == id);
        if (data != null)
        {
            jadeCount = data.initialCoinNum;
        }
        jadeCount = 9999;
        OnJadeIncome(jadeCount);
    }

    ///<summary>
    /// 扣款
    /// </summary>
    public bool payJade(int jade)
    {
        if (jadeTotal - jade < 0)
        {
            // 灵玉不足
            EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, Language.GetText(1005));
            return false;
        }

        OnJadeIncome(jade * -1);
        return true;
    }

    private void Update()
    {
        if(SceneMain.Instance.CameraHeightController == null) return;
        if (Input.GetKeyDown(KeyCode.LeftAlt)|| Input.GetKeyDown(KeyCode.RightAlt) )
        {
            if (m_gameStage == GameStage.Build )
            {
                SwitchView(true);
            }
        }
        if (Input.GetKeyUp(KeyCode.LeftAlt) || Input.GetKeyUp(KeyCode.RightAlt))
        {
            if (m_gameStage == GameStage.Build)
            {
                SwitchView(false);
            }
        }
    }

    private void SwitchView(bool Up)
    {
        EventDispatcher.TriggerEvent(EventDispatcherType.SwitchingView);
        SceneMain.Instance.CameraHeightController.SwitchSuperView(Up,
        (isSuperView) =>
        {
            if (isSuperView)
            {
                for (int i = 0; i < m_listCompletedBuildings.Count; i++)
                {
                    if (m_listCompletedBuildings[i].Camp == CampEnum.Ally)
                        m_listCompletedBuildings[i].showNextBuilding();
                }
            }
            else
            {
                for (int i = 0; i < m_listCompletedBuildings.Count; i++)
                {
                    if (m_listCompletedBuildings[i].Camp == CampEnum.Ally)
                        m_listCompletedBuildings[i].hideNextBuilding();
                }
                //EventDispatcher.TriggerEvent(EventDispatcherType.CancelShowBuildingCostInSuperView);
            }
        },
        (height) =>
        {
            if(height < 0.2f)
            {
                if (Up )
                {
                    EventDispatcher.TriggerEvent(EventDispatcherType.ShowBuildingCostInSuperView);
                }
                else
                {
                    EventDispatcher.TriggerEvent(EventDispatcherType.CancelShowBuildingCostInSuperView);
                }
            }

            //Debug.Log("height:" + height);
        });
    }
}

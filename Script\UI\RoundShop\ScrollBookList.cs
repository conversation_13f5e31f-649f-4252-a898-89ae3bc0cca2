using UnityEngine;
using System.Collections.Generic;
using Excel.shop;
using System.Linq;
public class ScrollBookList : MonoBehaviour
{
    public GameObject prefabItem;
    private static int ShopId = (int)ShopType.ScrollBook; // 当前商店Id
    private shopTypeEntity shopTypeEntity; // 当前商店类型参数
    private shopExcel excelData; // excel数据
    private int maxItemCount = 0; // 当前展示最大数量
    public bool isClickRefresh = false; // 是否手动刷新
    public bool isAutoRefresh = true; // 是否回合刷新
    private List<scrollPoolEntity> weightData; // 奖池类型配置
    private int roundLimit = 0; // 回合限制
    private int configRoundLimit = 0; // 配置的回合限制
    private void Awake() {
        // 加载Excel数据
        excelData = Resources.Load<shopExcel>("Excel/shopExcel");
        // 商店类型配置
        shopTypeEntity = excelData?.shopTypeList?.Find(x => x.id == ShopId);
        // 奖池数据
        weightData = excelData?.scrollPoolList;

        if (shopTypeEntity != null) {
            maxItemCount = shopTypeEntity.gridNum;
            isClickRefresh = shopTypeEntity.malyRefresh == 1;
            isAutoRefresh = shopTypeEntity.autoRefresh == 1;
        }
    }

    // 查询购买次数
    private int getBuyNum(int id) {
        List<BaseGoodsData> packageData = InventoryManager.Scroll.AllItems;
        if(packageData == null || packageData.Count <= 0) 
        {
            return 0;
        }
        int result = 0;
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                result++;
            }
        }
        return result;
    }
    // 查询是否存在前置
    private bool isExistFront(int id) {
        List<BaseGoodsData> packageData = InventoryManager.Scroll.AllItems;
        if(packageData == null || packageData.Count <= 0) 
        {
            return false;
        }
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                return true;
            }
        }
        return false;
    }
    /// <summary>
    /// 判断是否禁止刷新回合
    /// </summary>
    /// <param name="target"></param>
    /// <returns></returns>
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    /// <summary>
    /// 筛选数据
    /// </summary>
    /// <returns></returns>
    private List<scrollPoolEntity> filterData() {
        List<scrollPoolEntity> result = new List<scrollPoolEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 购买次数限制筛选
            bool isMaxBuy = weightData[i].buyNumMax > 0 && getBuyNum(weightData[i].id) >= weightData[i].buyNumMax;
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);
            // 前置条件筛选
            bool hasExistFront = weightData[i].refFront < 0 || (weightData[i].refFront > 0 && isExistFront(weightData[i].refFront));

            if(isRoundLimit && hasExistFront && !isMaxBuy) {
                result.Add(weightData[i]);
            }
        }
        return result;
    }
    /// <summary>
    /// 获取权重列表
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private List<int> getListWeight(List<scrollPoolEntity> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    private void handleBuy(BaseGoodsItem valObject) {
        roundLimit = 0;
    }
    public void toNextRound() {
        if(roundLimit > 0) {
            roundLimit--;
        } else {
            LoadData();
            roundLimit = 2;
        }
    }
    // 渲染
    private void RenderItem(scrollPoolEntity data) {
        if(prefabItem == null) return;
        GameObject _instantiate = Instantiate(prefabItem, transform);
        ScrollCard script = _instantiate.GetComponent<ScrollCard>();
        script.isGift = false;
        script.shopOpen = true;
        if(configRoundLimit > 0) 
        {
            script.onBuy(handleBuy);
        }
        script.setBackImg(shopTypeEntity.BackResourcesShow);
        script.LoadItem(data);
    }
    public void LoadData() {
        ClearChild();
        if(weightData == null || weightData.Count <= 0) return;
        List<scrollPoolEntity> filter = filterData(); // 前置筛选
        List<int> weightList = getListWeight(filter); // 获取权重列表
        for(int i = 0; i < maxItemCount; i++) {
            int index = WeightCalculator.GetWeightedRandomIndex(weightList); // 获取随机索引
            RenderItem(filter[index]); // 创建对应的实体
        }
    }
    private void ClearChild() {
        // 调用子节点的注销事件
        for (int i = 0; i < transform.childCount; i++) {
            BaseGoodsItem script = transform.GetChild(i).GetComponent<BaseGoodsItem>();
            script.DestroyItem();
        }
    }
}
using UnityEngine;
using UnityEngine.Events;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using TMPro;
using System;
using Excel.text;
public class BaseGoodsItem : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler
{
    [SerializeField] TMP_Text priceText;
    [SerializeField] Image frontImage;
    [SerializeField] Image backImage;
    public CardFlip cardFlip; // 翻牌组件
    public BtnEvent CardClickEvent; // 点击事件组件
    public BtnEvent BuyClickEvent; // 购买组件
    public BtnEvent SellClickEvent; // 出售组件
    public BtnEvent SelectClickEvent; // 选中事件组件
    public ItemChangeBorder baseBorderItem; // 边框组件
    public GameObject JadeLabel; // 灵玉标签
    public ItemsDetail itemsDetail; // 物品详情
    [Tooltip ("点击事件")]
    public UnityEvent SelectEvent; // 选中事件
    public UnityEvent SellEvent; // 出售事件
    [Tooltip ("行为事件")]
    public UnityEvent FlipEndEvent; // 翻转结束事件
    [Tooltip ("回调事件")]
    public Action<BaseGoodsItem> buyCallback = null; // 购买事件
    public Action<BaseGoodsItem> chooseCallback = null; // 选中事件
    public Action<BaseGoodsItem> selectCallback = null; // 选中事件
    public Action<BaseGoodsData> sellCallback = null; // 出售事件
    public BaseGoodsData baseGoodsData;
    private bool selected = false; // 选中状态
    public float hoverScale = 1.2f; // 缩放参数
    public string backImageUrl; // 背景图
    public GameObject parentObject;
    public bool isGift = false; // 包展示状态
    public bool shopOpen = false; // 商店打开状态
    // 选中
    public bool Selected {
        get {
            return selected;
        }
        set {
            selected = value;
            // 显示支付按钮
            BuyClickEvent?.gameObject.SetActive(selected && !isGift);
            // 显示出售按钮
            SellClickEvent?.gameObject.SetActive(selected);
            // 显示选中按钮
            SelectClickEvent?.gameObject.SetActive(selected && isGift);
            // 播放动画
            StartCoroutine(ScaleAnimation(selected ? hoverScale : 1, 0.3f));
            // 层级提高，避免被遮挡
            if(Selected) {
                transform.SetAsLastSibling();
            } else {
                transform.SetAsFirstSibling();
            }
        }
    }

    private void Start() {
        // 监听翻牌事件
        if(cardFlip != null && !isGift) 
        {
            cardFlip.OnFlipComplete.AddListener(OnFlipEndAction);
        }
        // 点击卡片事件监听
        if(CardClickEvent != null) 
        {
            CardClickEvent.OnClick.AddListener(handleSelect);
        }
        // 支付监听
        if(BuyClickEvent != null && !isGift) 
        {
            BuyClickEvent.OnClick.AddListener(handleBuy);
        }
        // 出售监听
        if(SellClickEvent != null && !isGift) 
        {
            SellClickEvent.OnClick.AddListener(handleSell);
        }
        // 选中监听
        if(SelectClickEvent != null && isGift) 
        {
            SelectClickEvent.OnClick.AddListener(handleChooese);
        }
    }

    void OnEnable()
    {
        EventDispatcher.AddEventListener<bool>(EventDispatcherType.ShopChange, shopChange);
        EventDispatcher.AddEventListener<GameObject>(EventDispatcherType.SelectGoods, handleSelfMsg);
    }

    void OnDisable()
    {
        EventDispatcher.RemoveEventListener<bool>(EventDispatcherType.ShopChange, shopChange);
        EventDispatcher.RemoveEventListener<GameObject>(EventDispatcherType.SelectGoods, handleSelfMsg);
    }

    private void shopChange(bool isOpen)
    {
        Selected = false;
        setDetail(false);
    }

    private void handleSelfMsg(GameObject obj)
    {
        if(obj == gameObject) return;
        Selected = false;
    }

    // 卡片选中监听
    private void handleSelect(PointerEventData eventData) {
        // 如果不是礼包且商店未打开，则不执行
        if (!isGift && !shopOpen) return;
        Selected = !Selected;
        selectCallback?.Invoke(this);
        EventDispatcher.TriggerEvent<GameObject>(EventDispatcherType.SelectGoods, gameObject);
    }
    // 购买事件监听
    private void handleBuy(PointerEventData eventData) {
        BuyItem();
    }
    // 出售事件监听
    private void handleSell(PointerEventData eventData) {
        EventDispatcher.TriggerEvent<int>(EventDispatcherType.JadeIncome, baseGoodsData.sellPrice);
        sellCallback?.Invoke(baseGoodsData);
        DestroyItem();
    }
    // 选中事件监听
    private void handleChooese(PointerEventData eventData) {
        ChooseItem();
    }
    // 翻牌结束事件
    private void OnFlipEndAction() {
        // 翻牌完成后显示价格
        if(JadeLabel != null) {
            JadeLabel.SetActive(true);
        }
        FlipEndEvent?.Invoke();
    }
    private void setDetail(bool state = false) 
    {
        if(itemsDetail != null) {
            itemsDetail.gameObject.SetActive(state);
        }
    }
    // 鼠标移入
    public void OnPointerEnter(PointerEventData eventData)
    {
        setDetail(true);
    }
    // 鼠标移出
    public void OnPointerExit(PointerEventData eventData)
    {
        setDetail(false);
    }
    // 缩放过度动画
    public IEnumerator ScaleAnimation(float scale, float duration) {
        float t = 0f;
        Vector3 startScale = transform.localScale;
        Vector3 endScale = new Vector3(scale, scale, 1);
        while(t < duration) {
            transform.localScale = Vector3.Lerp(startScale, endScale, t / duration);
            t += Time.deltaTime;
            yield return null;
       }
    } 
    // 设置背面的数据
    public void setBackImg(string url)
    {
        backImageUrl = url;
        backImage.sprite = Resources.Load<Sprite>(url);
    }
    // 设置正面的数据
    public void setFrontImg(string url)
    {
        frontImage.sprite = Resources.Load<Sprite>(url);
    }
    // 设置价格
    public void setPrice(float price)
    {
        string _text = price.ToString();
        if(SellClickEvent != null )
        {
            _text = baseGoodsData.sellPrice.ToString();
        }
        priceText.text = _text;
    }
    // 选中事件
    public void onSelect(Action<BaseGoodsItem> function) {
        selectCallback = function;
    }
    // 购买事件
    public void onBuy(Action<BaseGoodsItem> function) {
        buyCallback = function;
    }
    // 出售事件
    public void onSell(Action<BaseGoodsData> function) {
        sellCallback = function;
    }
    // 选择事件
    public void onChoose(Action<BaseGoodsItem> function) {
        chooseCallback = function;
    }
    // 销毁
    public void DestroyItem()
    {
        if(gameObject == null) return;
        Destroy(gameObject);
    }
    // 加载
    public virtual void LoadItem(){}
    public virtual void LoadItem(int id){}
    // 购买
    public virtual void BuyItem() {
        buyCallback?.Invoke(this);
        DestroyItem();
    }
    // 选择
    public virtual void ChooseItem() {
        chooseCallback?.Invoke(this);
        DestroyItem();
    }
    
}
using System.Collections.Generic;
using UnityEngine;
/// <summary>
/// 
/// </summary>
public class InventoryManager
{
    public static InventoryBase Rune = new InventoryBase(); // 符文仓
    public static InventoryBase Skill = new InventoryBase(); // 技能仓
    public static InventoryBase Other = new InventoryBase(); // 综合仓&宝石仓
    public static InventoryBase Scroll = new InventoryBase(); // 卷轴仓
    public static GoldBase Gold = new GoldBase(); // 金币仓 - 用于建筑  升级、拆除、建造
    public static GoldBase Jade = new GoldBase(); // 灵玉仓 - 用于商店购买消耗
    // 构造函数
    public InventoryManager() {
        // Debug.Log("初始化仓储系统 ---");
        initRune();
        initSkill();
        initOther();
        initScroll();
        initGold();
        initJade();
    }
    private void initRune() {
        Rune.Clear();
        Rune.maxCount = shopConfig.runeNum;
    }
    private void initSkill() {
        Skill.Clear();
        Skill.maxCount = (int)SkillTypeToCode.Treasure + 1;
    }
    private void initOther()
    {
        Other.Clear();
        Other.maxCount = shopConfig.bagNum;
        Other.allowRepeat = true;
    } 
    private void initScroll() {
        Scroll.Clear();
        Scroll.maxCount = 999;
    }
    private void initGold() {
        Gold.setMaxGold(9999);
        Gold.addGold(9999);
    }
    private void initJade() {
        Jade.setMaxGold(9999);
        Jade.addGold(9999);
    }
    
}
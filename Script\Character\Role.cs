using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Excel.hero;
using Excel.skill;
using Excel.WeaponAnimation;
using FSM;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.Rendering.Universal;
using UnityEngine.UIElements;


public enum RoleEnum
{
    Warrior     = 101,
    <PERSON>      = 102,
    <PERSON><PERSON>   = 103,
    Mage        = 104,
    <PERSON>        = 105,
}
public class Role : ActorControllable
{
    private static Role m_instance;

    public static Role Instance { get => m_instance; }
    [SerializeField]
    [ReadOnly]
    private RoleEnum m_roleEnum = RoleEnum.Warrior;
    [SerializeField]
    protected AdvancedAnimationSystem m_animationSystem;
    public AdvancedAnimationSystem AnimationSystem { get => m_animationSystem; set => m_animationSystem = value; }

    public override float AnimationSpeed
    {
        get => m_animationSystem.speed;
        set => m_animationSystem.speed = value;
    }

    public DecalProjector m_projector;

    private herobaseEntity m_herobaseEntity;

    private int m_AcquiredSkillID;
    /// <summary>
    /// 从魔法研究院学习到的技能ID
    /// 有回合限制，到回合后技能消失
    /// </summary>
    public int AcquiredSkillID { get => m_AcquiredSkillID; set => m_AcquiredSkillID = value; }

    private int m_AcquiredSkillTurnCount;
    public int AcquiredSkillTurnCount { get => m_AcquiredSkillTurnCount; set => m_AcquiredSkillTurnCount = value; }

    [SerializeField] private float m_minDistance = 2.5f;     // 聚拢到主角时之间的最小间距

    public override float CastingRange
    {
        get
        {
            float range = 0;
            if (m_currentSkill != null)
                range = m_currentSkill.CastingRange;
            if (m_normalSkill != null)
                range = m_normalSkill.CastingRange;
            return range > this.AttackRange ? range : this.AttackRange;
        }

    }
    /// <summary>
    /// 已经学习的技能，只能学习一次
    /// </summary>
    private int m_learnSkill = -1;
    public int learnSkill { get => m_learnSkill; }

    private int m_isSelectAlly = 0;

    /// <summary>
    /// 武器带有的技能
    /// </summary>
    private List<heroSkillEntity> m_heroSkillList = new List<heroSkillEntity>();

    private int m_currentHeroSkillID;

    protected int m_lv = 0;
    public int Lv { get => m_lv; set => m_lv = value; }

    //EmptyHanded = 1,   // 空手
    //Bow = 2,           // 弓
    //Staff = 3,         // 杖
    //OneHandSword = 4,  // 单手剑
    //Club = 5,          // 木棍
    //Greatsword = 6,    // 巨剑
    //Spear = 7,         // 长枪
    //DualBlades = 8,    // 双刀
    //SwordAndShield = 9 // 剑盾
    [SerializeField]
    private GameObject[] m_weaponRight;
    [SerializeField]
    private GameObject[] m_weaponLeft;

    public static bool StopMove = false;

    public override void Awake()
    {
        m_objectType = ObjectType.Role;
        m_instance = this;

        m_beatBackState = Resources.Load<FSM.State>("StateMachine/Role/Beat Back State");

        base.Awake();
    }
    public override void OnEnable()
    {
        EventDispatcher.AddEventListener<int>(EventDispatcherType.LearnSkill, OnLearnSkill);
        EventDispatcher.AddEventListener<int>(EventDispatcherType.AddAcquiredSkill, OnAddResearchSkill);
        EventDispatcher.AddEventListener(EventDispatcherType.NextTurn, Revive);
        EventDispatcher.AddEventListener<heroSkillEntity>(EventDispatcherType.BuySkill, OnAddSkill);
        EventDispatcher.AddEventListener<heroSkillEntity>(EventDispatcherType.SellSkill, OnRemoveSkill);
        m_lastPosition = transform.position;
        base.OnEnable();

        KeyStatusManager.Instance.RegisterKeyUpCallback(KeyCode.D, gameObject, OnRightKeyUp);
        KeyStatusManager.Instance.RegisterKeyUpCallback(KeyCode.A, gameObject, OnLeftKeyUp);
        KeyStatusManager.Instance.RegisterKeyUpCallback(KeyCode.W, gameObject, OnForwardKeyUp);
        KeyStatusManager.Instance.RegisterKeyUpCallback(KeyCode.S, gameObject, OnBackwardKeyUp);

        KeyStatusManager.Instance.RegisterKeyDownCallback(KeyCode.D, gameObject, OnRightKeyDown);
        KeyStatusManager.Instance.RegisterKeyDownCallback(KeyCode.A, gameObject, OnLeftKeyDown);
        KeyStatusManager.Instance.RegisterKeyDownCallback(KeyCode.W, gameObject, OnForwardKeyDown);
        KeyStatusManager.Instance.RegisterKeyDownCallback(KeyCode.S, gameObject, OnBackwardKeyDown);

        KeyStatusManager.Instance.RegisterKeyHoldCallback(KeyCode.D, gameObject, OnRightKeyHold);
        KeyStatusManager.Instance.RegisterKeyHoldCallback(KeyCode.A, gameObject, OnLeftKeyHold);
        KeyStatusManager.Instance.RegisterKeyHoldCallback(KeyCode.W, gameObject, OnForwardKeyHold);
        KeyStatusManager.Instance.RegisterKeyHoldCallback(KeyCode.S, gameObject, OnBackwardKeyHold);

        m_selectUnitRange = heroConfig.selectRange;
    }

    public override void OnDisable()
    {
        RemoveAllEvent();
        base.OnDisable();
    }

    public void RemoveAllEvent()
    {
        EventDispatcher.RemoveEventListener<int>(EventDispatcherType.LearnSkill, OnLearnSkill);
        EventDispatcher.RemoveEventListener<int>(EventDispatcherType.AddAcquiredSkill, OnAddResearchSkill);
        EventDispatcher.RemoveEventListener(EventDispatcherType.NextTurn, Revive);
        EventDispatcher.RemoveEventListener<heroSkillEntity>(EventDispatcherType.BuySkill, OnAddSkill);
        EventDispatcher.RemoveEventListener<heroSkillEntity>(EventDispatcherType.SellSkill, OnRemoveSkill);

        if (KeyStatusManager.Instance == null) return;

        KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.D, gameObject);
        KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.A, gameObject);
        KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.W, gameObject);
        KeyStatusManager.Instance.RemoveKeyDownCallback(KeyCode.S, gameObject);

        KeyStatusManager.Instance.RemoveKeyUpCallback(KeyCode.D, gameObject);
        KeyStatusManager.Instance.RemoveKeyUpCallback(KeyCode.A, gameObject);
        KeyStatusManager.Instance.RemoveKeyUpCallback(KeyCode.W, gameObject);
        KeyStatusManager.Instance.RemoveKeyUpCallback(KeyCode.S, gameObject);

        KeyStatusManager.Instance.RemoveKeyHoldCallback(KeyCode.D, gameObject);
        KeyStatusManager.Instance.RemoveKeyHoldCallback(KeyCode.A, gameObject);
        KeyStatusManager.Instance.RemoveKeyHoldCallback(KeyCode.W, gameObject);
        KeyStatusManager.Instance.RemoveKeyHoldCallback(KeyCode.S, gameObject);
    }
    public void OnAddResearchSkill(int id)
    {
        if (m_skillList.Exists(skill => skill.SkillID == id)) return;

        m_AcquiredSkillID = id;
        Skill skill = new Skill(id, this);
        skill.CastingRange = skill.originalCastingRange * m_attributeCoefficient;

        m_skillList.Add(skill);
        m_skillList.Sort((left, right) => right.Priority.CompareTo(left.Priority));

        ResetCastingRange();
    }

    public void OnAddSkill(heroSkillEntity entity)
    {
        //entity = ExcelData.Instance.HeroExcel.heroSkillList.Find(element => element.id == 100101);
        if (m_skillList.Exists(skill => skill.SkillID == entity.id)) return;

        Skill skill = null;
        string[] skills = entity.skillGroup.Split("|");
        for (int i = 0; i < skills.Length; i++)
        {
            skill = new Skill(int.Parse(skills[i]), this);
            skill.CastingRange = skill.originalCastingRange * m_attributeCoefficient;
            m_skillList.Add(skill);
        }
        ResetCastingRange();
        //是否普攻技能
        if (entity.shopShowType == 1)
        {
            //是否是武器
            if(entity.weaponType > 0)
            {
                //重新加载动画
                m_animationSystem.ReloadAnimationClips(entity.weaponType);
                m_currentHeroSkillID = entity.id;
                //隐藏武器
                for (int i = 0; i < m_weaponRight.Length; i++)
                {
                    if (m_weaponRight[i] != null)
                        m_weaponRight[i].SetActive(false);
                    if (m_weaponLeft[i] != null)
                        m_weaponLeft[i].SetActive(false);
                }
                //显示要装备的武器
                if (m_weaponRight[entity.weaponType] != null)
                    m_weaponRight[entity.weaponType].SetActive(true);
                if (m_weaponLeft[entity.weaponType] != null)
                    m_weaponLeft[entity.weaponType].SetActive(true);
            }
        }

        //加入列表
        if (!m_heroSkillList.Exists(element => element.id == entity.id))
            m_heroSkillList.Add(entity);
    }

    public void OnRemoveSkill(heroSkillEntity entity)
    {
        string[] skills = entity.skillGroup.Split("|");
        for (int i = 0; i < skills.Length; i++)
        {
            int skillID = int.Parse(skills[i]);
            RemoveSkill(skillID);
        }
        if( entity.id == m_currentHeroSkillID)
        {
            //换到空手
            heroSkillEntity heroSkillEntity = ExcelData.Instance.GetHeroSkill(m_herobaseEntity.passive);
            if (heroSkillEntity != null)
                OnAddSkill(heroSkillEntity);

        }
        m_heroSkillList.RemoveAll( element => element.id == entity.id );

    }
    public void ShowSelectUnitRing(bool show)
    {
        m_projector.enabled = show;
        m_projector.size  =  new Vector3(m_selectUnitRange * 2, m_selectUnitRange * 2, 2);
    }

    public void SetData(herobaseEntity entity)
    {
        m_herobaseEntity = entity;
        m_id = entity.id;
        attrEntity attr = ExcelData.Instance.HeroExcel.attrList.Find(element => element.hero == m_id && element.lv == m_lv);
        this.Health         = attr.hp;
        m_currentHealth     = attr.hp;
        this.ArmorBreak     = attr.brk;
        this.Defense        = attr.def;
        this.AttackDamage   = attr.atk;
        this.AttackRange    = attr.atkRange;
        this.AttackSpeed    = attr.asp;
        this.MoveSpeed      = attr.spd;

        m_originalHealth        = attr.hp;
        m_originalArmorBreak    = attr.brk;
        m_originalDefense       = attr.def;
        m_originalAttackDamage  = attr.atk;
        m_originalAttackRange   = attr.atkRange;
        m_originalAttackSpeed   = attr.asp;
        m_originalMoveSpeed     = attr.spd;

        //m_fiveElements = (FiveElementEnum)entity.attributeType;
        m_movementType = (MovementType)entity.terrainUnitType;
        m_CombatType = (CombatType)entity.combatMode;

        //AddSkill(entity.passive);
        //AddSkill(entity.active1);
        //AddSkill(entity.active2);
        //AddSkill(entity.active3);

        heroSkillEntity heroSkillEntity = ExcelData.Instance.GetHeroSkill(entity.passive);
        m_heroSkillList.Add(heroSkillEntity);
        OnAddSkill(heroSkillEntity);

        m_normalSkill = m_skillList.Find(skill => skill.SkillID == entity.passive);

        //102的模型使用Additive模式
        //m_animationSystem.isAdditive = m_id == 102;

        //m_animationSystem.OnAttackCallback = OnAttackEvent;

        //float atkSpeed = Mathf.Max(AttackSpeed * (1 + AttackSpeedChange), fightConfig.atkSpeedA);
        //m_attackClipLength = Mathf.Max(1 / atkSpeed, fightConfig.atkSpeedB);
    }

    void OnLearnSkill(int skillID)
    {
        if( m_learnSkill > 0 ) return;//已经学习过技能
        m_learnSkill = skillID;
        AddSkill(skillID);
    }
    public override Skill getActiveSkill()
    {
        //根据英雄技能表检查是否是主动技能
        List<heroSkillEntity> heroSkillList = ExcelData.Instance.HeroExcel.heroSkillList;
        for (int i = 0; i < m_skillList.Count; i++)
        {
            Skill skill = m_skillList[i];
            for (int j = 0; j < heroSkillList.Count; j++)
            {
                if (heroSkillList[j].shopShowType == 3)
                {
                    if (heroSkillList[i].skillGroup.Contains(skill.SkillID.ToString()))
                    {
                        return skill;
                    }
                }
            }
        }
        return null;
    }

    public override void PlayIdle()
    {
        //依然在移动
        if(vertical != 0 || horizontal != 0 ) return;

        AnimationType = AnimationType.Idle;
        m_animationSystem.PlayAnimation(AnimationState.Idle);   
    }
    public override void PlayRun()
    {
        AnimationType = AnimationType.Run;
        m_animationSystem.PlayAnimation(AnimationState.Run);
    }
    public override void PlayWalk()
    {
        AnimationType = AnimationType.Walk;
    }

    public override void PlayAttack()
    {
        AnimationType = AnimationType.Attack;
        m_animationSystem.PlayAnimation(AnimationState.Attack);
    }

    public override void PlayDeath()
    {
        if (m_AnimationType == AnimationType.Death) return;

        AnimationType = AnimationType.Death;
        m_animationSystem.PlayAnimation(AnimationState.Death);
    }

    public override void PlayPreSkillCast()
    {
        AnimationType = AnimationType.PreSkillCast;
        m_animationSystem.PlayAnimation(AnimationState.PreSkillCast);
        m_animationSystem.StopIdle();
        if (m_animationSystem.PreSkillCastEventTime > 0)
        {
            float time = m_animationSystem.PreSkillCastEventTime / m_animationSystem.PreSkillCastSpeed;
            StartCoroutine(OnAttackEvent(time));
        }

    }

    public override void PlayLoopSkillCast()
    {
        AnimationType = AnimationType.LoopSkillCast;
        m_animationSystem.PlayAnimation(AnimationState.LoopSkillCast);
        m_animationSystem.StopIdle();
    }

    public override void PlayPostSkillCast()
    {
        AnimationType = AnimationType.PostSkillCast;
        m_animationSystem.PlayAnimation(AnimationState.PostSkillCast);
        m_animationSystem.StopIdle();
    }

    private IEnumerator OnAttackEvent(float time)
    {
        yield return new WaitForSeconds(time);
        if (m_currentSkill != null)
        {
            m_currentSkill.ApplyEffect();
        }
    }

    public override void StopCombat()
    {
        m_animationSystem.StopCombat();
    }

    void selectAllyActor()
    {
        List<Actor> is_followerList = GameMain.Instance.AllyList;
        for (int i = 0; i < is_followerList.Count; i++)
        {
            if (is_followerList[i] == null) continue;
            if (is_followerList[i] == this) continue;
            if (is_followerList[i] is Sheep) continue;

            Actor actor = is_followerList[i];

            // 判断是否已经add
            if (m_followerList.Exists(val => val == actor )) continue;
            if (Vector3.Distance(transform.position, actor.transform.position) < m_selectUnitRange)
            {
                actor.ChangeColor(Color.red);
                m_followerList.Add(actor);
            }
        }
    }

    public override void Update()
    {
        if(m_isClone)
        {
            base.Update();
            return;
        }
        if (!m_isGhost || RuneSystem.Instance.isGhostCanSelectUnit)
        {
            // 按R选中友军
            if (Input.GetKey(KeyCode.R))
            {
                //if (GameMain.Instance.gameStage != GameStage.Build) return;
                if (m_isSelectAlly > 0) return;
                // 显示选择范围
                ShowSelectUnitRing(true);
                // 执行选择
                selectAllyActor();

            }
            else if (Input.GetKeyUp(KeyCode.R))
            {
                //if (GameMain.Instance.gameStage != GameStage.Build) return;
                ShowSelectUnitRing(false);
                // 第二次按下R键松开后放置怪物
                if (m_isSelectAlly >= 1 && m_followerList.Count > 0)
                {
                    TeleportFollower();
                    m_isSelectAlly = 0;
                }
                else
                {
                    if (m_followerList.Count > 0)
                    {
                        m_isSelectAlly++;
                    }
                }
            }
        }
        if (Input.GetKeyDown(KeyCode.X))
        {
            GuardCommand();
        }
        else if (Input.GetKeyDown(KeyCode.C))
        {
            ChaseCommand();
        }
        else if (Input.GetKeyDown(KeyCode.Space))
        {
            UseSkill();
        }

        //更新跟随者的位置
        if (m_AnimationType == AnimationType.Run || m_AnimationType == AnimationType.Walk)
        {
            if ( m_followerList.Count > 0 || m_elfList.Count > 0 )
            {
                float distance = Vector3.Distance(m_lastPosition, transform.position);
                if (distance > 1)
                {
                    UpdateFollower(m_elfList);
                    UpdateFollower(m_followerList);
                    m_lastPosition = transform.position;
                }
            }
        }

        SmartBodyOrientation();

        base.Update();
    }

    /// <summary>
    /// 更新位置
    /// </summary>
    private void TeleportFollower()
    {
        if (m_followerList.Count == 0) return;
        // 获取圆形排列的半径
        float radius = m_selectUnitRange;

        // 计算每个跟随者的角度间隔
        float angleStep = 360f / m_followerList.Count;

        // 为每个跟随者分配圆周上的位置
        for (int i = 0; i < m_followerList.Count; ++i)
        {
            m_followerList[i].isFollow = false;
            m_followerList[i].ChangeColor(Color.white);

            if (Vector3.Distance(transform.position, m_followerList[i].transform.position) < radius + 1)
                continue;

            // 计算当前跟随者的角度
            float angle = i * angleStep;

            // 将角度转换为弧度
            float radian = angle * Mathf.Deg2Rad;

            // 计算圆周上的位置
            Vector3 offset = new Vector3(
                Mathf.Cos(radian) * radius,
                0,
                Mathf.Sin(radian) * radius
            );

            // 计算目标位置
            Vector3 destination = transform.position + offset;

            // 检查目标位置是否可行走
            Vector3 finalDestination;
            if ( GridPathfinding.HasObstaclesInRadius(destination, m_followerList[i].Radius))
            {
                // 如果位置不可行走，尝试找到附近的可行走点
                if (!GridPathfinding.FindWalkablePointInSector(
                    transform.position,
                    destination,
                    radius * 0.8f,
                    radius * 1.5f,
                    m_followerList[i].Radius + 0.5f,
                    out finalDestination))
                {
                    // 如果找不到合适的点，使用当前位置
                    finalDestination = m_followerList[i].transform.position;
                }
            }
            else
            {
                finalDestination = destination;
            }
            m_followerList[i].transform.position = finalDestination;
        }
        m_followerList.Clear();
    }


    public void StartFight()
    {
        for (int i = 0; i < m_followerList.Count; i++)
        {
            if (m_followerList[i] == null) continue;

            m_followerList[i].isFollow = false;
            m_followerList[i].ChangeColor(Color.white);
        }
        m_followerList.Clear();
    }
    public void GuardCommand()
    {
        for (int i = 0; i < m_followerList.Count; ++i)
        {
            m_followerList[i].Guard();
        }
    }

    public void ChaseCommand()
    {
        for (int i = 0; i < m_followerList.Count; ++i)
        {
            m_followerList[i].Chase();
        }
    }

    public override bool UseSkill()
    {
        if(isDisableSkill || isGhost ) return false;
        if (GameMain.Instance.gameStage != GameStage.Fight) return false;

        List<Skill> list = m_skillList.FindAll(skill => skill.SkillTriggerType == Skill.SkillTriggerEnum.Active && skill.isCoolDownFinish && skill.Enable );
        if (list.Count > 0)
        {
            list.Sort((left, right) => right.Priority.CompareTo(left.Priority));

            if (m_currentSkill != null )
            {
                if (list[0].Priority > m_currentSkill.Priority && m_currentSkill.SkillID != list[0].SkillID)
                {
                    m_currentSkill = list[0];
                    EnterSkillState();
                    return true;
                }
            }
            else
            {
                m_currentSkill = list[0];
                EnterSkillState();
                return true;
            }
        }

        return false;
    }

    public override void TestSkill(int id)
    {
        m_currentSkill = m_skillList.Find(skill => skill.SkillID == id);
        if( m_currentSkill == null )
        {
            m_currentSkill = new Skill(id,this);
            m_currentSkill.CastingRange = m_currentSkill.originalCastingRange * m_attributeCoefficient;
            m_skillList.Add(m_currentSkill);
        }

        EnterSkillState();
    }
    public override void AddElf(Elf Elf)
    {
        Elf.isFollow = true;

        m_elfList.Add(Elf);
        UpdateFollower(m_elfList);
    }
    public void RemoveElf(Elf actor)
    {
        m_elfList.Remove(actor);
    }

    public void ElfEnterBuilding(Elf elf)
    {
        //m_elfList.Remove(elf);
    }

    public void ElfExitBuilding(Elf elf)
    {
        //m_elfList.Add(elf);
        UpdateFollower(m_elfList);
    }


    public void OnMouseDown()
    {
        EventDispatcher.TriggerEvent(EventDispatcherType.SelectActor, this);
    }
    //public bool haveSkillsToLearn
    //{
    //    get
    //    {
    //        if (!m_skillList.Exists(skill => skill.SkillID == m_herobaseEntity.active1))
    //            return true;
    //        if (!m_skillList.Exists(skill => skill.SkillID == m_herobaseEntity.active2))
    //            return true;
    //        if (!m_skillList.Exists(skill => skill.SkillID == m_herobaseEntity.active3))
    //            return true;
    //        return false;
    //    }
    //}

    //public override void OnTriggerEnter(Collider other)
    //{
    //    base.OnTriggerEnter(other);
    //}

    //public void OnTriggerExit(Collider other)
    //{
    //}

    public override void OnDeath()
    {
        if (m_isClone)
        {
            base.OnDeath();
            return;
        }

        m_targetActor = null;
        m_animationSystem.ClearCombatState();
        StateMachine.ChangeState(m_idleState);

        var shaderRenderer = GetComponentsInChildren<Renderer>();
        for( int i = 0; i < shaderRenderer.Length; i++ )
        {
            materialSetOpcity(shaderRenderer[i].material, 0.5f);
        }

        EventDispatcher.TriggerEvent(EventDispatcherType.ActorDeath, this);
        m_deathTime = Time.time;

        gameObject.SetActive(true);
        m_isGhost = true;
        StartCoroutine(ReviveDownCount());

        m_navMeshAgent.ClearTempObstacles();
        //m_navMeshAgent.obstacleAvoidanceType = UnityEngine.AI.ObstacleAvoidanceType.NoObstacleAvoidance;
    }

    /// <summary>
    /// 开始新的关卡时重置数据
    /// </summary>
    public void Revive()
    {
        var shaderRenderer = GetComponentsInChildren<Renderer>();
        for (int i = 0; i < shaderRenderer.Length; i++)
        {
            recoverMaterial(shaderRenderer[i].material);
        }
        base.Renew();
        EventDispatcher.TriggerEvent(EventDispatcherType.CloseRoleReviveCountDown);
    }
    /// <summary>
    /// 复活倒计时
    /// </summary>
    /// <returns></returns>
    public IEnumerator ReviveDownCount()
    {
        yield return new WaitForSeconds(0.5f);
        
        for(int i = 0; i < heroConfig.resurrectionTime; i++ )
        {
            EventDispatcher.TriggerEvent(EventDispatcherType.RoleReviveCountDown, heroConfig.resurrectionTime - i);
            yield return new WaitForSeconds(1f);
        }
        //复活
        Revive();
    }

    public void OnRightKeyDown()
    {
        horizontal = 1;
        MoveAction();
    }

    public void OnLeftKeyDown()
    {
        horizontal = -1;
        MoveAction();
    }

    public void OnForwardKeyDown()
    {
        vertical = 1;
        MoveAction();
    }

    public void OnBackwardKeyDown()
    {
        vertical = -1;
        MoveAction();
    }

    public void OnRightKeyUp()
    {
        horizontal = 0;
        MoveAction();
    }

    public void OnLeftKeyUp()
    {
        horizontal = 0;
        MoveAction();
    }

    public void OnForwardKeyUp()
    {
        vertical = 0;
        MoveAction();
    }

    public void OnBackwardKeyUp()
    {
        vertical = 0;
        MoveAction();
    }

    public void OnRightKeyHold()
    {
        horizontal = 1;
        MoveAction();
    }

    public void OnLeftKeyHold()
    {
        horizontal = -1;
        MoveAction();
    }

    public void OnForwardKeyHold()
    {
        vertical = 1;
        MoveAction();
    }

    public void OnBackwardKeyHold()
    {
        vertical = -1;
        MoveAction();
    }

    public void MoveAction()
    {
        if(Role.StopMove) return;

        if (isDisableMove) return;
        if (isRush) return;

        if (horizontal == 0 && vertical == 0)
        {
            if(m_targetActor == null)
                m_animationSystem.RestoreOrientation();

            m_animationSystem.PlayAnimation( AnimationState.Idle );

            navMeshAgent.Velocity = Vector3.zero;
            return;
        }
        AnimationType = AnimationType.Run;

        m_animationSystem.PlayAnimation(AnimationState.Run);

        Vector3 direction = new Vector3(horizontal, 0, vertical).normalized;
        if (Mathf.Abs(direction.x) > 0.0f && Mathf.Abs(direction.z) > 0.0f) // 判断是否为斜线移动
        {
            direction *= 0.7071f; // 斜线移动时乘以 0.7071
        }
        navMeshAgent.Velocity = direction * MoveSpeed;
        Vector3 targetPosition = transform.position + (direction * MoveSpeed * Time.deltaTime);

        if ( navMeshAgent.IsWalkable(targetPosition))
        {
            transform.forward = direction;
            transform.position = targetPosition;
        }
        navMeshAgent.ClearTempObstacles();
        navMeshAgent.SetTempObstacles();

        if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
        {
            if (CurrentHealth == Health)
            {
                navMeshAgent.speed = heroConfig.fast;
                AnimationSpeed = 1.5f;
            }
        }
        else if (Input.GetKeyUp(KeyCode.LeftShift) || Input.GetKeyUp(KeyCode.RightShift))
        {
            navMeshAgent.speed = MoveSpeed;
            AnimationSpeed = 1;
        }
    }

    public override bool isCurrentAnimationFinish()
    {
        return m_animationSystem.isCombatAnimationFinish();
    }
    /// <summary>
    /// 设置技能各个阶段的播放速度，达到整个技能播放时间符合攻击速度
    /// </summary>
    public override void SetSkillCast()
    {
        float atkSpeed = Mathf.Max(AttackSpeed * (AttackSpeedChange), fightConfig.atkSpeedA);
        m_attackClipLength = Mathf.Max(1 / atkSpeed, fightConfig.atkSpeedB);
        m_animationSystem.SetSkillCast(m_attackClipLength, m_currentSkill.skillShow);
    }

    /// <summary>
    /// 整体向前移动，上半身朝向目标
    /// </summary>
    public void SmartBodyOrientation()
    {
        if(m_isRush)
        {
            m_animationSystem.RestoreOrientation();
            return;
        }

        if (m_targetActor != null && m_targetActor != this )
        {
            Vector3 targetDir = m_targetActor.transform.position - transform.position;
            m_animationSystem.SmartBodyOrientation(targetDir);
        }
        else
            m_animationSystem.RestoreOrientation();
    }

    //private void OnDrawGizmos()
    //{
    //    // 绘制5米范围的选择圈
    //    Gizmos.color = Color.green;
    //    Gizmos.DrawWireSphere(transform.position, 5f); // 半径2.5米即直径5米

    //    // 可选：添加文字标签说明
    //    GUIStyle style = new GUIStyle();
    //    style.normal.textColor = Color.green;
    //    UnityEditor.Handles.Label(transform.position + Vector3.up * 3, "5米范围", style);
    //}


}

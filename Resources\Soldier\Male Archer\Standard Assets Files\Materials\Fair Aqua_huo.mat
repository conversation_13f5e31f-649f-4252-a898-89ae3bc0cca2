%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Fair Aqua_huo
  m_Shader: {fileID: 4800000, guid: 933532a4fcc9baf4fa0491de14d08ed7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _SPECULARHIGHLIGHTS_OFF
  m_InvalidKeywords:
  - TCP2_DISABLE_WRAPPED_LIGHT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 4cd9dd28c8cb95643997e9c3fa7e710e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 4cd9dd28c8cb95643997e9c3fa7e710e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 2800000, guid: abc83c21d57d4a74cabe12065fa1a8b2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _STexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _DstBlendOutline: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Offset1: 0
    - _Offset2: 0
    - _Outline: 1
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _RampSmooth: 0.7
    - _RampSmoothAdd: 0.75
    - _RampSmoothOtherLights: 0.5
    - _RampThreshold: 0.35
    - _RampThresholdOtherLights: 0.5
    - _ReceiveShadows: 1
    - _RimMax: 0.85
    - _RimMin: 0.6
    - _RimStrength: 0.5
    - _Shininess: 0.1
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecBlend: 1
    - _SpecSmooth: 1
    - _SpecularHighlights: 0
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _SrcBlendOutline: 0
    - _Surface: 0
    - _TCP2_DISABLE_WRAPPED_LIGHT: 1
    - _TCP2_OUTLINE_CONST_SIZE: 0
    - _TCP2_OUTLINE_TEXTURED: 0
    - _TCP2_RAMPTEXT: 0
    - _TCP2_SPEC_TOON: 0
    - _TCP2_STYLIZED_FRESNEL: 0
    - _TCP2_ZSMOOTH_ON: 0
    - _TexLod: 5
    - _UVSec: 0
    - _WorkflowMode: 1
    - _ZSmooth: -0.5
    - _ZWrite: 1
    - __dummy__: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DiffTint: {r: 0.7, g: 0.8, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 0.785, g: 0.785, b: 0.785, a: 1}
    - _OutlineColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _RimColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.6}
    - _SColor: {r: 0.19499996, g: 0.19499996, b: 0.19499996, a: 1}
    - _SpecColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &2570338057419609315
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9

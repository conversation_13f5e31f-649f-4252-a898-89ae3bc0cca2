using UnityEngine;
using UnityEngine.UI;
using TMPro;

[RequireComponent(typeof(Canvas))]
public class BeastDetail : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private ItemsDetail detail;
    [SerializeField] private float offset = 10f;
    private RectTransform rectTransform;
    private Canvas canvas;
    public GameObject target;

    public string Title {
        set 
        {
            detail.Title = value;
        }
    }

    public string ContentText
    {
        set
        {
            detail.Content = value;
        }
    }

    void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        canvas = GetTopmostCanvas(transform);
        canvas.sortingOrder = 100; // 确保弹窗显示在最上层
    }

    // 显示弹窗
    public void Show()
    {
        if(target == null) return;
        // 强制更新布局以确保获取的位置是最新的
        RectTransform itemRect = target.GetComponent<RectTransform>();
        Canvas.ForceUpdateCanvases(); // 强制更新所有Canvas和布局
        LayoutRebuilder.ForceRebuildLayoutImmediate(itemRect);
        
        // 获取Item四个角的屏幕坐标
        Vector3[] itemCorners = new Vector3[4];
        itemRect.GetWorldCorners(itemCorners);
        // corners: 0=左下, 1=左上, 2=右上, 3=右下

        // 检查父对象是否为HorizontalLayoutGroup
        HorizontalLayoutGroup layoutGroup = itemRect.GetComponentInParent<HorizontalLayoutGroup>();
        // 考虑布局参数调整位置计算
        LayoutInfo layoutInfo = GetLayoutInfo(layoutGroup, itemRect);
        
        // 获取弹窗尺寸
        Vector2 popupSize = rectTransform.rect.size * canvas.scaleFactor;

        // 设置弹窗的锚点也为左上角，以与target保持一致
        SetPopupAnchor(rectTransform);

        // 计算最佳显示位置，考虑布局信息和左上锚点
        Vector3 targetPosition = CalculateBestPosition(
            itemCorners,
            popupSize,
            layoutInfo
        );

        // 应用屏幕边界限制
        targetPosition = ClampToScreen(targetPosition, popupSize);

        // 设置弹窗位置，考虑到锚点影响
        rectTransform.position = targetPosition;

        detail.gameObject.SetActive(true);
    }

    // 设置弹窗的锚点为左上角，与target保持一致
    private void SetPopupAnchor(RectTransform popupRect)
    {
        // 保存当前位置
        Vector3 currentPos = popupRect.position;
        
        // 设置锚点为左上角
        popupRect.anchorMin = new Vector2(0, 1);
        popupRect.anchorMax = new Vector2(0, 1);
        popupRect.pivot = new Vector2(0, 1);
        
        // 恢复位置，因为锚点变化会影响位置
        popupRect.position = currentPos;
    }

    // 存储布局相关信息的结构体
    private struct LayoutInfo
    {
        public bool isInLayout;
        public float paddingLeft;
        public float paddingRight;
        public float spacing;
        public bool isMiddleCenter;
        public int childIndex;
        public int totalChildren;
    }

    // 获取布局相关信息
    private LayoutInfo GetLayoutInfo(HorizontalLayoutGroup layoutGroup, RectTransform itemRect)
    {
        LayoutInfo info = new LayoutInfo();
        
        if (layoutGroup == null)
        {
            info.isInLayout = false;
            return info;
        }
        
        info.isInLayout = true;
        info.paddingLeft = layoutGroup.padding.left;
        info.paddingRight = layoutGroup.padding.right;
        info.spacing = layoutGroup.spacing;
        info.isMiddleCenter = 
            layoutGroup.childAlignment == TextAnchor.MiddleCenter;
        
        // 获取子对象索引
        Transform parent = itemRect.parent;
        info.childIndex = itemRect.GetSiblingIndex();
        info.totalChildren = parent.childCount;
        
        return info;
    }

    // 隐藏弹窗
    public void Hide()
    {
        GameObject popup = detail.gameObject;
        target = null;
        Title = "";
        ContentText = "";
        popup.SetActive(false);
    }

    // 获取顶层Canvas的辅助方法
    private Canvas GetTopmostCanvas(Transform transform)
    {
        Debug.Log("这里的执行次数---------------");
        Canvas[] parentCanvases = transform.GetComponentsInParent<Canvas>();
        return parentCanvases[parentCanvases.Length - 1];
    }

    // 计算坐标，考虑布局信息和左上锚点
    private Vector3 CalculateBestPosition(Vector3[] itemCorners, Vector2 popupSize, LayoutInfo layoutInfo)
    {
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;

        // 计算目标对象的中心点和尺寸
        Vector3 itemCenter = (itemCorners[0] + itemCorners[2]) / 2f;
        float itemWidth = Vector3.Distance(itemCorners[3], itemCorners[2]);
        float itemHeight = Vector3.Distance(itemCorners[0], itemCorners[1]);

        // 左上锚点(1)在左上角，需要特别注意垂直位置计算
        // 由于锚点在左上，当放在target下方时，需要向下移动整个popup的高度

        // 计算四个方向的可用空间，考虑布局
        float rightSpace, leftSpace;
        
        if (layoutInfo.isInLayout)
        {
            // 考虑布局中的间距情况
            bool isFirstChild = layoutInfo.childIndex == 0;
            bool isLastChild = layoutInfo.childIndex == layoutInfo.totalChildren - 1;
            
            // 左侧可能有其他元素，考虑spacing(40)和paddingLeft(10)
            leftSpace = itemCorners[0].x - offset;
            if (isFirstChild)
            {
                // 第一个元素左侧有paddingLeft
                leftSpace = itemCorners[0].x - offset - layoutInfo.paddingLeft;
            }
            
            // 右侧可能有其他元素，考虑spacing(40)和paddingRight
            rightSpace = screenWidth - (itemCorners[2].x + offset);
            if (isLastChild)
            {
                // 最后一个元素右侧有paddingRight
                rightSpace = screenWidth - (itemCorners[2].x + offset) - layoutInfo.paddingRight;
            }
            
            // 如果不是第一个或最后一个元素，两侧都有spacing
            if (!isFirstChild && !isLastChild)
            {
                // 两侧都可能被其他元素占用，需要考虑spacing
                leftSpace -= layoutInfo.spacing;
                rightSpace -= layoutInfo.spacing;
            }
        }
        else
        {
            // 非布局元素，简单计算
            rightSpace = screenWidth - (itemCorners[2].x + offset);
            leftSpace = itemCorners[0].x - offset;
        }
        
        float topSpace = screenHeight - (itemCorners[1].y + offset);
        float bottomSpace = itemCorners[0].y - offset;

        // 由于Horizontal Layout Group的特性，我们更倾向于在左右位置显示
        // 给左右方向增加权重，考虑实际布局
        float HORIZONTAL_PRIORITY = layoutInfo.isInLayout ? 2.0f : 1.5f;
        
        // 存储最佳位置和对应的优先级
        Vector3 bestPosition = Vector3.zero;
        float maxWeightedSpace = -1;

        // 检查右侧空间 (带权重)
        float weightedRightSpace = rightSpace * HORIZONTAL_PRIORITY;
        if (rightSpace >= popupSize.x && weightedRightSpace > maxWeightedSpace)
        {
            maxWeightedSpace = weightedRightSpace;
            // 对于左上锚点，垂直位置应为target的左上角Y坐标
            // 水平位置应为target的右边缘 + offset
            bestPosition = new Vector3(
                itemCorners[2].x + offset,
                itemCorners[1].y,  // 使用左上角Y坐标，因为锚点在左上
                0
            );
        }

        // 检查左侧空间 (带权重)
        float weightedLeftSpace = leftSpace * HORIZONTAL_PRIORITY;
        if (leftSpace >= popupSize.x && weightedLeftSpace > maxWeightedSpace)
        {
            maxWeightedSpace = weightedLeftSpace;
            // 对于左上锚点，垂直位置应为target的左上角Y坐标
            // 水平位置应为target的左边缘 - popup宽度 - offset
            bestPosition = new Vector3(
                itemCorners[0].x - popupSize.x - offset,
                itemCorners[1].y,  // 使用左上角Y坐标，因为锚点在左上
                0
            );
        }

        // 检查顶部空间
        if (topSpace >= popupSize.y && topSpace > maxWeightedSpace)
        {
            maxWeightedSpace = topSpace;
            // 对于左上锚点，垂直位置应为target的左上角Y坐标 + offset
            // 水平位置应为target的左边缘
            bestPosition = new Vector3(
                layoutInfo.isMiddleCenter ? itemCenter.x - popupSize.x / 2 : itemCorners[0].x,
                itemCorners[1].y + offset,  // 向上移动
                0
            );
        }

        // 检查底部空间
        if (bottomSpace >= popupSize.y && bottomSpace > maxWeightedSpace)
        {
            maxWeightedSpace = bottomSpace;
            // 对于左上锚点，垂直位置应为target的左下角Y坐标 - popup高度 - offset
            // 水平位置应为target的左边缘
            bestPosition = new Vector3(
                layoutInfo.isMiddleCenter ? itemCenter.x - popupSize.x / 2 : itemCorners[0].x,
                itemCorners[0].y - popupSize.y - offset,  // 需要考虑popup高度，向下移动
                0
            );
        }

        // 如果所有方向都没有足够的空间，选择空间最大的方向(考虑权重)
        if (maxWeightedSpace < 0)
        {
            // 找出最大空间的方向(考虑权重)
            float[] spaces = { weightedRightSpace, weightedLeftSpace, topSpace, bottomSpace };
            int maxIndex = 0;
            for (int i = 1; i < spaces.Length; i++)
            {
                if (spaces[i] > spaces[maxIndex])
                {
                    maxIndex = i;
                }
            }

            // 根据最大空间方向设置位置，考虑左上锚点
            switch (maxIndex)
            {
                case 0: // 右侧
                    bestPosition = new Vector3(
                        itemCorners[2].x + offset,
                        itemCorners[1].y,  // 左上角Y坐标
                        0
                    );
                    break;
                case 1: // 左侧
                    bestPosition = new Vector3(
                        itemCorners[0].x - popupSize.x - offset,
                        itemCorners[1].y,  // 左上角Y坐标
                        0
                    );
                    break;
                case 2: // 顶部
                    bestPosition = new Vector3(
                        layoutInfo.isMiddleCenter ? itemCenter.x - popupSize.x / 2 : itemCorners[0].x,
                        itemCorners[1].y + offset,
                        0
                    );
                    break;
                case 3: // 底部
                    bestPosition = new Vector3(
                        layoutInfo.isMiddleCenter ? itemCenter.x - popupSize.x / 2 : itemCorners[0].x,
                        itemCorners[0].y - popupSize.y - offset,
                        0
                    );
                    break;
            }
        }

        return bestPosition;
    }

    // 确保弹窗不超出屏幕
    private Vector3 ClampToScreen(Vector3 position, Vector2 popupSize) {
        // X轴限制（防止左右溢出）
        position.x = Mathf.Clamp(
            position.x, 
            0, 
            Screen.width - popupSize.x
        );
        
        // Y轴限制（防止上下溢出），考虑左上锚点
        position.y = Mathf.Clamp(
            position.y,
            popupSize.y,  // 下限：确保弹窗底部不超出屏幕底部
            Screen.height  // 上限：确保弹窗顶部不超出屏幕顶部
        );
        
        return position;
    }
}
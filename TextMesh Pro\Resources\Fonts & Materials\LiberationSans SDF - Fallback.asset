%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2180264
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: LiberationSans SDF Material
  m_Shader: {fileID: 4800000, guid: fe393ace9b354375a9cb14cdbbc28be4, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cube:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FaceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 28268798066460806}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Ambient: 0.5
    - _Bevel: 0.5
    - _BevelClamp: 0
    - _BevelOffset: 0
    - _BevelRoundness: 0
    - _BevelWidth: 0
    - _BumpFace: 0
    - _BumpOutline: 0
    - _BumpScale: 1
    - _ColorMask: 15
    - _CullMode: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _Diffuse: 0.5
    - _DstBlend: 0
    - _FaceDilate: 0
    - _FaceUVSpeedX: 0
    - _FaceUVSpeedY: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _GlowInner: 0.05
    - _GlowOffset: 0
    - _GlowOuter: 0.05
    - _GlowPower: 0.75
    - _GradientScale: 10
    - _LightAngle: 3.1416
    - _MaskSoftnessX: 0
    - _MaskSoftnessY: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineSoftness: 0
    - _OutlineUVSpeedX: 0
    - _OutlineUVSpeedY: 0
    - _OutlineWidth: 0
    - _Parallax: 0.02
    - _PerspectiveFilter: 0.875
    - _Reflectivity: 10
    - _ScaleRatioA: 0.9
    - _ScaleRatioB: 0.73125
    - _ScaleRatioC: 0.73125
    - _ScaleX: 1
    - _ScaleY: 1
    - _ShaderFlags: 0
    - _Sharpness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularPower: 2
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _TextureHeight: 512
    - _TextureWidth: 512
    - _UVSec: 0
    - _UnderlayDilate: 0
    - _UnderlayOffsetX: 0
    - _UnderlayOffsetY: 0
    - _UnderlaySoftness: 0
    - _VertexOffsetX: 0
    - _VertexOffsetY: 0
    - _WeightBold: 0.75
    - _WeightNormal: 0
    - _ZWrite: 1
    m_Colors:
    - _ClipRect: {r: -32767, g: -32767, b: 32767, a: 32767}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EnvMatrixRotation: {r: 0, g: 0, b: 0, a: 0}
    - _FaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _GlowColor: {r: 0, g: 1, b: 0, a: 0.5}
    - _MaskCoord: {r: 0, g: 0, b: 32767, a: 32767}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectFaceColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectOutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnderlayColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 71c1514a6bd24e1e882cebbe1904ce04, type: 3}
  m_Name: LiberationSans SDF - Fallback
  m_EditorClassIdentifier: 
  m_Version: 1.1.0
  m_FaceInfo:
    m_FaceIndex: 0
    m_FamilyName: Liberation Sans
    m_StyleName: Regular
    m_PointSize: 86
    m_Scale: 1
    m_UnitsPerEM: 2048
    m_LineHeight: 98.8916
    m_AscentLine: 77.853516
    m_CapLine: 59
    m_MeanLine: 45
    m_Baseline: 0
    m_DescentLine: -18.22461
    m_SuperscriptOffset: 77.853516
    m_SuperscriptSize: 0.5
    m_SubscriptOffset: -18.22461
    m_SubscriptSize: 0.5
    m_UnderlineOffset: -12.261719
    m_UnderlineThickness: 6.298828
    m_StrikethroughOffset: 18
    m_StrikethroughThickness: 6.298828
    m_TabWidth: 24
  m_Material: {fileID: 2180264}
  m_SourceFontFileGUID: e3265ab4bf004d28a9537516768c1c75
  m_CreationSettings:
    sourceFontFileName: 
    sourceFontFileGUID: e3265ab4bf004d28a9537516768c1c75
    faceIndex: 0
    pointSizeSamplingMode: 0
    pointSize: 86
    padding: 9
    paddingMode: 0
    packingMode: 4
    atlasWidth: 512
    atlasHeight: 512
    characterSetSelectionMode: 1
    characterSequence: 32 - 126, 160 - 255, 8192 - 8303, 8364, 8482, 9633
    referencedFontAssetGUID: 8f586378b4e144a9851e7b34d9b748ee
    referencedTextAssetGUID: 
    fontStyle: 0
    fontStyleModifier: 0
    renderMode: 4169
    includeFontFeatures: 1
  m_SourceFontFile: {fileID: 12800000, guid: e3265ab4bf004d28a9537516768c1c75, type: 3}
  m_SourceFontFilePath: 
  m_AtlasPopulationMode: 1
  InternalDynamicOS: 0
  m_GlyphTable: []
  m_CharacterTable: []
  m_AtlasTextures:
  - {fileID: 28268798066460806}
  m_AtlasTextureIndex: 0
  m_IsMultiAtlasTexturesEnabled: 1
  m_GetFontFeatures: 1
  m_ClearDynamicDataOnBuild: 1
  m_AtlasWidth: 512
  m_AtlasHeight: 512
  m_AtlasPadding: 9
  m_AtlasRenderMode: 4169
  m_UsedGlyphRects: []
  m_FreeGlyphRects:
  - m_X: 0
    m_Y: 0
    m_Width: 511
    m_Height: 511
  m_FontFeatureTable:
    m_MultipleSubstitutionRecords: []
    m_LigatureSubstitutionRecords: []
    m_GlyphPairAdjustmentRecords:
    - m_FirstAdjustmentRecord:
        m_GlyphIndex: 20
        m_GlyphValueRecord:
          m_XPlacement: 0
          m_YPlacement: 0
          m_XAdvance: -6.3828125
          m_YAdvance: 0
      m_SecondAdjustmentRecord:
        m_GlyphIndex: 20
        m_GlyphValueRecord:
          m_XPlacement: 0
          m_YPlacement: 0
          m_XAdvance: 0
          m_YAdvance: 0
      m_FeatureLookupFlags: 0
    m_MarkToBaseAdjustmentRecords: []
    m_MarkToMarkAdjustmentRecords: []
  m_ShouldReimportFontFeatures: 0
  m_FallbackFontAssetTable: []
  m_FontWeightTable:
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  fontWeights:
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  - regularTypeface: {fileID: 0}
    italicTypeface: {fileID: 0}
  normalStyle: 0
  normalSpacingOffset: 0
  boldStyle: 0.75
  boldSpacing: 7
  italicStyle: 35
  tabSize: 10
  m_fontInfo:
    Name: Liberation Sans
    PointSize: 86
    Scale: 1
    CharacterCount: 250
    LineHeight: 98.90625
    Baseline: 0
    Ascender: 77.84375
    CapHeight: 59.1875
    Descender: -18.21875
    CenterLine: 0
    SuperscriptOffset: 77.84375
    SubscriptOffset: -12.261719
    SubSize: 0.5
    Underline: -12.261719
    UnderlineThickness: 6.298828
    strikethrough: 23.675
    strikethroughThickness: 0
    TabWidth: 239.0625
    Padding: 9
    AtlasWidth: 1024
    AtlasHeight: 1024
  m_glyphInfoList: []
  m_KerningTable:
    kerningPairs: []
  fallbackFontAssets: []
  atlas: {fileID: 0}
--- !u!28 &28268798066460806
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: LiberationSans SDF Atlas
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 1
  m_Height: 1
  m_CompleteImageSize: 1
  m_MipsStripped: 0
  m_TextureFormat: 1
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 0
    m_WrapV: 0
    m_WrapW: 0
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 1
  _typelessdata: 00
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 

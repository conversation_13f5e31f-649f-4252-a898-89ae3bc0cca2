%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7094014840790335634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8014291710545081079}
  - component: {fileID: 2300076047732698437}
  m_Layer: 0
  m_Name: Stamp (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8014291710545081079
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7094014840790335634}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 334.8, y: -11.9, z: 315.2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2300076047732698437
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7094014840790335634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9795eda8381a191479e8ca56eae4ee90, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stamp: {fileID: 11400000, guid: 082049223336d614492df8e21226f939, type: 2}
  maskOverride: {fileID: 0}
  maskMap:
    mapType: 2
    input: 3
    target: 0
    modifier:
      modifierType: 2
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0
      edgeSize: 0.73
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.385
    inspectorInitialized: 1
    selfIndex: 0
  heightMap:
    mapType: 0
    input: 1
    target: 1
    modifier:
      modifierType: 0
      blendMode: 2
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 1
      cutoffMax: 0.5
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  colorMap:
    mapType: 1
    input: 2
    target: 2
    modifier:
      modifierType: 1
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  highlightMap:
    mapType: 5
    input: 0
    target: 19
    modifier:
      modifierType: 4
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 0
    selfIndex: 0
  roadMaskMap:
    mapType: 4
    input: 12
    target: 0
    modifier:
      modifierType: 2
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 0
    selfIndex: 0
  stampMaps:
  - mapType: 3
    input: 0
    target: 3
    modifier:
      modifierType: 0
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  holeMap:
    mapType: 6
    input: 0
    target: 20
    modifier:
      modifierType: 5
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  center: {x: 0, y: 0, z: 0}
  size: {x: 1000, y: 50, z: 1000}
  roadBlending: 0
  tileMask: 0
  stampTiling: {x: 1, y: 1}

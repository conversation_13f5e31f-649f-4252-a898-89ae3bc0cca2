using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using DG.Tweening;
using System;

public class TabData {
    public int index;
    public GameObject panel;
    public GameObject button;
    public BuildEditUI.TabItemType type;
}

public class TabManager : MonoBehaviour
{
    [SerializeField] private BuildEditUI buildEditUI; // 主界面脚本
    [SerializeField] private UIHorizontalLayout TabUILayout; // tab的布局组件
    private List<TabData> tabDataList = new List<TabData>();
    private int currentTabIndex = -1;
    // 动画相关设置
    public float animationDuration = 0.3f; // 切换动画的持续时间
    public Ease easeType = Ease.OutQuad;   // 动画曲线类型 (DOTween 使用 Ease 枚举)

    private bool isAnimating = false; // 标记当前是否正在进行动画
    private Sequence currentSequence; // 用于存储当前的 DOTween Sequence，方便在需要时终止
    private Action<int> changeAction;
    public List<TabData> TabDatas { get => tabDataList; }
    public bool DisableCheck { get; set; } = false;
    public int CurrentTabIndex {
        get  {
            return currentTabIndex;
        }
        set  {
            if (value ==  currentTabIndex) return;
            currentTabIndex = value;
            changeAction?.Invoke(currentTabIndex);
        }
    }
    // 添加一个Tab项
    public int AddItem(GameObject button, GameObject panel, BuildEditUI.TabItemType type) 
    {
        int index = -1;
        if (button == null || panel == null) return index;
        index = tabDataList.Count;
        tabDataList.Add(new TabData() {
            index = index,
            panel = panel,
            button = button,
            type = type
        });
        return index;
    }
    // 根据索引查找Tab数据
    public TabData SelectorIndex(int index) 
    {
        for (int i = 0; i < tabDataList.Count; i++)
        {
            if (tabDataList[i].index == index) {
                return tabDataList[i];
            }
        }
        return null;
    }
    // 移除指定索引的Tab和面板
    public void RemoveItem(int index) 
    {
        for (int i = 0; i < tabDataList.Count; i++)
        {
            if (tabDataList[i].index == index) {
                DestroyItem(tabDataList[i], index);
                return;
            }
        }
    }
    public void DestroyItem(TabData tabData, int index = -1)
    {
        if(tabData.button != null)
        {
             Destroy(tabData.button);
        }
        if(tabData.panel != null)
        {
             Destroy(tabData.panel);
        }
        if(index != -1)
        {
            tabDataList.RemoveAt(index);
        }
    }
    public void StartRender(int index = 0) 
    {
        if(tabDataList.Count > 0) {
            CurrentTabIndex = index;
            TabUILayout.onLayoutUpdate(() => {
                // 初始化时，只显示第一个面板
                Transform transform = tabDataList[CurrentTabIndex].button.transform;
                // 等布局结束后执行首项选中
                transform.DOScale(new Vector3(1.2f, 1.2f, 1.2f), animationDuration).SetEase(easeType);
                transform.SetAsLastSibling();
            });
            GameObject firstPanel = tabDataList[CurrentTabIndex].panel;
            firstPanel.SetActive(true);
        }
    }

    public void onChange(Action<int> change) {
        changeAction = change;
    }

    public void clear() 
    {
        changeAction = null;
        CurrentTabIndex = -1;
        if(tabDataList.Count > 0)
        {
            for  (int i = 0; i < tabDataList.Count; i++)
            {
                DestroyItem(tabDataList[i]);
            }
            tabDataList.Clear();
        }
    }

    void Update()
    {
        if(DisableCheck) return;
        // 检测Q键按下，切换到上一个Tab
        if (Input.GetKeyDown(KeyCode.Q))
        {
            PreviousTab();
        }

        // 检测E键按下，切换到下一个Tab
        if (Input.GetKeyDown(KeyCode.E))
        {
            NextTab();
        }
    }

    // 切换到指定索引的Tab
    public void SwitchTab(int newIndex, bool instant = false)
    {
        if (newIndex < 0 || newIndex >= tabDataList.Count)
        {
            Debug.LogWarning("Tab index out of bounds: " + newIndex);
            return; // 索引无效
        }

        if (newIndex == CurrentTabIndex && !instant)
        {
            // 已经是当前Tab，且不是强制即时切换，则不执行
            return;
        }

        if (isAnimating && !instant)
        {
            // 正在动画，且不是强制即时切换，则不执行
            // 或者，如果你希望新的切换打断当前动画，可以在这里 Kill 掉 currentSequence
            // currentSequence?.Kill();
            // isAnimating = false; // 重置状态
            // Debug.Log("Tab switch blocked: animation in progress.");
            return; // 阻止重复切换
        }


        // 如果有正在进行的动画，先停止它
        currentSequence?.Kill();
        isAnimating = true; // 开始新的动画标记


        GameObject oldPanel = tabDataList[CurrentTabIndex].panel;
        GameObject newPanel = tabDataList[newIndex].panel;

        // 获取或添加 CanvasGroup 组件
        CanvasGroup oldCanvasGroup = oldPanel.GetComponent<CanvasGroup>();
        if (oldCanvasGroup == null) oldCanvasGroup = oldPanel.AddComponent<CanvasGroup>();
        CanvasGroup newCanvasGroup = newPanel.GetComponent<CanvasGroup>();
        if (newCanvasGroup == null) newCanvasGroup = newPanel.AddComponent<CanvasGroup>();

        // 确保新面板的 CanvasGroup 存在且可以被动画
        newCanvasGroup.alpha = instant ? 1f : 0f; // 即时切换直接满透明，否则从0开始

        // 如果是即时切换
        if (instant)
        {
            oldPanel.SetActive(false);
            newPanel.SetActive(true);

            // 强制刷新布局，让 ContentSizeFitter 计算新高度
            LayoutRebuilder.ForceRebuildLayoutImmediate(newPanel.GetComponent<RectTransform>());

            oldCanvasGroup.alpha = 0f;
            oldCanvasGroup.interactable = false;
            oldCanvasGroup.blocksRaycasts = false;
            newCanvasGroup.alpha = 1f;
            newCanvasGroup.interactable = true;
            newCanvasGroup.blocksRaycasts = true;

            isAnimating = false; // 即时切换没有动画，直接结束
        }
        else // 使用 DOTween 进行动画切换
        {
            // 创建一个 DOTween Sequence 来编排动画步骤
            currentSequence = DOTween.Sequence();

            // 1. 动画退出当前面板 (如果它不是一开始就隐藏的)
            if (oldPanel.activeSelf)
            {
                 currentSequence.Append(oldCanvasGroup.DOFade(0f, animationDuration)
                                     .SetEase(easeType)
                                     .OnStart(() => {
                                          // 动画开始时禁用旧面板的交互
                                          oldCanvasGroup.interactable = false;
                                          oldCanvasGroup.blocksRaycasts = false;
                                      })
                                     .OnComplete(() => {
                                         // 动画结束后隐藏旧面板
                                         oldPanel.SetActive(false);
                                     }));
                 // 如果是第一个面板切换到第二个面板，第一个面板在Start时已经是激活状态，需要先淡出
            }


            // 2. 激活新面板并强制刷新布局 (发生在旧面板淡出并隐藏之后，新面板淡入之前)
            currentSequence.AppendCallback(() => {
                newPanel.SetActive(true);
                // **在这里强制刷新布局**
                // 确保 ContentSizeFitter 在淡入动画开始前计算出正确高度
                LayoutRebuilder.ForceRebuildLayoutImmediate(newPanel.GetComponent<RectTransform>());
            });

            // 3. 动画进入新面板
             currentSequence.Append(newCanvasGroup.DOFade(1f, animationDuration)
                                     .SetEase(easeType)
                                      .OnComplete(() => {
                                         // 动画结束后启用新面板的交互
                                         newCanvasGroup.interactable = true;
                                         newCanvasGroup.blocksRaycasts = true;
                                     }));


            // 4. 整个 Sequence 完成后，标记动画结束
            currentSequence.OnComplete(() => {
                 isAnimating = false;
                 currentSequence = null; // 清空引用
            });

            // 确保动画在 Update 循环外被驱动，Sequence 默认会自动播放
        }

        // 更新Tab按钮的可视状态 (如果使用了Tab按钮列表)
        UpdateTabButtonStates(newIndex, instant);

        CurrentTabIndex = newIndex; // 更新当前Tab索引
    }

    // 切换到下一个Tab
    public void NextTab()
    {
        int nextIndex = (CurrentTabIndex + 1) % tabDataList.Count;
        SwitchTab(nextIndex);
    }

    // 切换到上一个Tab
    public void PreviousTab()
    {
        int previousIndex = (CurrentTabIndex - 1 + tabDataList.Count) % tabDataList.Count; // + contentPanels.Count 是为了处理0 - 1 = -1 的情况
        SwitchTab(previousIndex);
    }

    // 更新Tab按钮的高亮状态 (可选)
    void UpdateTabButtonStates(int activeIndex, bool instant = false)
    {
        if (tabDataList.Count > 0)
        {
            for (int i = 0; i < tabDataList.Count; i++)
            {
                Transform transform = tabDataList[i].button.transform;
                // 根据你的UI设计，改变按钮的颜色、Sprite、文本样式等来表示选中状态
                if (i == activeIndex)
                {
                    // 使用DOTween进行放大动画
                    transform.DOScale(new Vector3(1.2f, 1.2f, 1.2f), animationDuration)
                             .SetEase(easeType);
                        transform.SetAsLastSibling();
                }
                else
                {
                    // 使用DOTween进行缩小动画
                    transform.DOScale(new Vector3(1f, 1f, 1f), animationDuration)
                             .SetEase(easeType);
                        transform.SetAsFirstSibling();
                }
            }
        }
    }

    // 在脚本销毁时停止所有 DOTween 动画，防止报错
    void OnDestroy()
    {
        currentSequence?.Kill();
        // 或者使用 DOTween.KillAll(); 如果你想停止所有 DOTween 动画 (慎用)
    }
}
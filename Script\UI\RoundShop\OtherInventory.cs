using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
public class OtherInventory : MonoBehaviour
{
    public static OtherInventory Instance;
    [SerializeField] List<GoodsToPrefab> Prefabs; // 预制体 宝石和灵宠的预制体
    private InventoryBase store = null; // 仓库
    public List<BaseGoodsData> buyList { get => store?.AllItems; } // 购买列表
    public List<BaseGoodsItem> list = new List<BaseGoodsItem>();
    // 新增开关属性和位置存储
    private RectTransform rectTransform; // 获取UI组件
    private Vector2 originalAnchoredPos; // 存储初始UI位置
    float viewMove = 70f; // 列表移动距离
    private void Awake() {
        // 获取UI组件
        rectTransform = transform.parent.GetComponent<RectTransform>(); 
        // 存储初始UI位置
        originalAnchoredPos = rectTransform.anchoredPosition; 
        // 初始化参数
        store = store ?? InventoryManager.Other;
        
        Instance = this;
        // 监听商店是否打开
        EventDispatcher.AddEventListener<bool>(EventDispatcherType.ShopChange, ToggleInventoryPanel);
    }

    private void Start()
    {
        init();
    }

    private void init()
    {
        for(int i = 0; i < list.Count; i++)
        {
            Destroy(list[i].gameObject);
        }
        list.Clear();
        if(store.ItemCount <= 0) return;
        for (int i = 0; i < store.ItemCount; i++)
        {
            LoadItems(store.AllItems[i]);
        }
    }

    // 新增开关方法
    public void ToggleInventoryPanel(bool open)
    {
        // 设置Item的状态
        setItemShopState(open);

        float targetY = open ? originalAnchoredPos.y + viewMove : originalAnchoredPos.y;
        
        // 使用UI专用的锚点动画
        rectTransform.DOAnchorPosY(targetY, 0.3f)
            .SetEase(Ease.OutQuad)
            .SetUpdate(true); // 确保在Time.timeScale=0时仍能播放
    }
    // 设置Item的状态
    private void setItemShopState(bool open)
    {
        if(list.Count <= 0) return;
        for(int i = 0; i < list.Count; i++)
        {
            list[i].shopOpen = open;
        }
    }
    // 渲染
    public BaseGoodsItem LoadItems(BaseGoodsData val) 
    {
        if(val == null || Prefabs == null || Prefabs.Count <= 0) return null;
        GoodsType type = (GoodsType)val.goodsType;
        GameObject _prefab = Prefabs.Find(x=> x.goodsType == type)?.prefab;
        if(_prefab == null) return null;
        GameObject itemPrefab = Instantiate(_prefab, transform);
        BaseGoodsItem script = itemPrefab.GetComponent<BaseGoodsItem>();
        script.setBackImg(val.background);
        // script.setPrice(val.sellPrice);
        script.isGift = false;
        script.shopOpen = true;
        // // 加载数据
        script.onSell(x=> store.RemoveItem(x));
        script.LoadItem(val.id);
        return script;
    }
    // 加载购买成功的物品
    public bool AddItems(BaseGoodsData val) 
    {
        // 校验
        if (!store.CheckAdd(val)) return false;
        // 新增
        BaseGoodsItem script = LoadItems(val);
        if(script == null) return false;
        // 入仓
        bool res = store.AddItem(script.baseGoodsData);
        list.Add(script);
        if(!res) 
        {
            Debug.Log("添加到仓库失败");
            script.DestroyItem();
            return false;
        };
        // 成功
        return true;
    }
    // 使用物品
    public void UseItems(BaseGoodsData val)
    {
        int itemIndex = list.FindIndex(x=> x.baseGoodsData.id == val.id);
        list[itemIndex]?.DestroyItem();
        list.RemoveAt(itemIndex);
        store.RemoveItem(val);
    }
}
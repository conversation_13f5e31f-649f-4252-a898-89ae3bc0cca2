using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Excel.skill;
using static UnityEngine.EventSystems.EventTrigger;

[Serializable]
public class BuffData
{
    //改变目标属性、持续性伤害、限制技能使用、停止行动、嘲讽目标、持续性治疗、无敌、吸血、光环、护盾、反弹
    [Serializable]
    public enum BuffEnum
    {
        None            = 0,
        ChangeAttribute = 1,
        DamageOverTime  = 2,
        RestrictSkill   = 3,
        StopAct         = 4,
        Taunt           = 5,//嘲讽目标
        HealthOverTime  = 6,
        Unbeatable      = 7,
        LifeSteal       = 8, //吸血
        Halo            = 9,
        Shield          = 10,
        Rebound         = 11,
        CannotBeSelected= 12,//不能被选中
        
        ReplaceSkill    = 14,
        HealingBonus    = 17,//治疗加成
        DamageBonus     = 20,//受伤加成
        IgnoreAbnormal  = 22,//无视异常状态
        ArmorBreak      = 23,//攻击时无视目标防御
        /// <summary>
        /// 受伤免疫
        /// </summary>
        DamageImmunity  = 26,
        /// <summary>
        /// 根据范围内友方数量增强属性值
        /// </summary>
        GainPowerBasedAllies = 27,
        HPDamageBonus        = 29, //生命值伤害加成
        RaceDamageBonus      = 30, //怪物种族伤害加成
        SpeedDamageBonus     = 31, //速度伤害加成
        DamageLifesteal      = 32, // 伤害回血
        AttackEnemiesToPowerUp       = 33, //攻击对方增强自己
        AttributeOnFollowerStatus    = 35, //跟随者根据状态改变属性
        ChangeAttributesInRange      = 36, //范围内的目标改变属性
        ChangeAttributesByTargetCount= 37, //范围内目标数量对应改变自身属性
        PartDamageOverTime           = 38, //部分伤害缓慢扣除
        DamageReduction              = 49, //伤害减免
        AttackDamageBonus            = 50, //攻击伤害加成
    }

    //1=不可叠加，一个目标重复获得相同的buffid的buff时，新获得的buff不生效													
    //2=不可叠加，一个目标重复获得相同的buffid的buff时，刷新持续时间但不叠加													
    //3=直接叠加，一个目标重复获得相同的buffid的buff时，新获得的buff直接叠加到已有buff上，同时生效													
    //4=按buff等级叠加，一个目标重复获得相同的buff组的buff时，读取下一级buff(叠加层数上限读取skill_buffgroup表的superpositionBuff字段）	
    public enum SuperpositionEnum
    {
        None    = 0,
        Discard = 1,    //新获得的buff不生效	
        Renew   = 2,    //刷新持续时间但不叠加
        Stack   = 3,
        Upgrade = 4,
    }

    //增益、减益、控制效果、异常状态
    public enum MainEnum
    {
        None            = 0,
        PositiveEffect  = 1,
        NegativeEffect  = 2,
        Control         = 3,
        Abnormal        = 4
    }

    //控制效果包含：1=冰冻、2=嘲讽、3=眩晕		
    public enum ControlEnum
    {
        /// <summary>
        /// 冰冻
        /// </summary>
        Frozen  = 1,
        /// <summary>
        /// 嘲讽
        /// </summary>
        Taunt   = 2,
        /// <summary>
        /// 眩晕
        /// </summary>
        Stun    = 3,
    }
    //异常状态包含：1=中毒、2=流血、3=燃烧、4=减速
    public enum AbnormalEnum
    {
        Poison  = 1,
        Blood   = 2,
        Fire    = 3,
        Slow    = 4,
    }
    //id	superposition	maxSuperposition	superpositionBuff	showId	mainType	subType	dieRemove	buffDelay	duration	buffType	buffParams1	buffParams2	buffParams3	buffParams4	buffParams5	buffParams6	buffParams7	
    private int m_id;
    public int ID { get => m_id; set => m_id = value; }

    private SuperpositionEnum m_superposition;
    public SuperpositionEnum Superposition { get => m_superposition; set => m_superposition = value; }

    private int m_maxSuperposition;
    public int MaxSupersition { get => m_maxSuperposition; set => m_maxSuperposition = value;}

    private int m_superpositionBuff;
    public int SuperpositionBuff {  get => m_superpositionBuff; set => m_superpositionBuff = value;}

    private int m_showID;
    public int ShowID { get => m_showID; set => m_showID = value; }

    private MainEnum m_mainType;

    /// <summary>
    /// 1=增益
    /// 2=减益
    /// 3=控制效果
    /// 4=异常状态
    /// </summary>
    public MainEnum MainType { get => m_mainType; set => m_mainType = value; }

    private int m_subType;
    public int SubType { get => m_subType; set => m_subType = value; }

    /// <summary>
    /// 1=死亡移除buff
    /// 2=死亡不移除buff
    /// </summary>
    private bool m_dieRemove;
    public bool dieRemove { get => m_dieRemove; set => m_dieRemove = value; }
    //buffDelay	duration	buffType	buffParams1	buffParams2	buffParams3	buffParams4	buffParams5	buffParams6	buffParams7	
    private int m_buffDelay;
    public int buffDelay { get => m_buffDelay; set => m_buffDelay = value; }

    private float m_duration;
    public float duration { get => m_duration; set => m_duration = value; }

    private BuffEnum m_buffType;
    public BuffEnum BuffType { get => m_buffType; set => m_buffType = value; }

    public float buffParams1;
    public float buffParams2;
    public float buffParams3;
    public float buffParams4;
    public float buffParams5;
    public float buffParams6;
    public float buffParams7;

    public BuffData(int id)
    {
        buffEntity entity = ExcelData.Instance.GetBuff(id);
        m_id = entity.id;
        m_superposition = (SuperpositionEnum)entity.superposition;
        m_maxSuperposition = entity.maxSuperposition;
        m_superpositionBuff = entity.superpositionBuff;
        m_showID = entity.showId;
        m_mainType = (MainEnum)entity.mainType;
        m_subType = entity.subType;
        m_dieRemove = entity.dieRemove==1;
        m_buffDelay = entity.buffDelay;
        m_duration = entity.duration/1000.0f;
        m_buffType = (BuffEnum)entity.buffType;

        if (!string.IsNullOrEmpty(entity.buffParams1))
            buffParams1 = float.Parse(entity.buffParams1);
        if (!string.IsNullOrEmpty(entity.buffParams2))
            buffParams2 = float.Parse(entity.buffParams2);
        if (!string.IsNullOrEmpty(entity.buffParams3))
            buffParams3 = float.Parse(entity.buffParams3);
        if (!string.IsNullOrEmpty(entity.buffParams4))
            buffParams4 = float.Parse(entity.buffParams4);
        if (!string.IsNullOrEmpty(entity.buffParams5))
            buffParams5 = float.Parse(entity.buffParams5);
        if (!string.IsNullOrEmpty(entity.buffParams6))
            buffParams6 = float.Parse(entity.buffParams6);
        if (!string.IsNullOrEmpty(entity.buffParams7))
            buffParams7 = float.Parse(entity.buffParams7);
    }

    public BuffData()
    {
        m_superposition = SuperpositionEnum.Discard;
        m_maxSuperposition = 0;
        m_superpositionBuff = 0;
        m_showID = 0;
        m_mainType = MainEnum.None;
        m_subType = 0;
        m_dieRemove = true;
        m_buffDelay = 0;
        m_duration = 0;
        m_buffType = BuffEnum.None;
    }

    /// <summary>
    /// 创建当前BuffData对象的深拷贝
    /// </summary>
    /// <returns>返回一个新的BuffData对象，包含当前对象的所有属性值</returns>
    public BuffData Clone()
    {
        BuffData newBuff = new BuffData(this.ID);
        newBuff.m_superposition = this.m_superposition;
        newBuff.m_maxSuperposition = this.m_maxSuperposition;
        newBuff.m_superpositionBuff = this.m_superpositionBuff;
        newBuff.m_showID = this.m_showID;
        newBuff.m_mainType = this.m_mainType;
        newBuff.m_subType = this.m_subType;
        newBuff.m_dieRemove = this.m_dieRemove;
        newBuff.m_buffDelay = this.m_buffDelay;
        newBuff.m_duration = this.m_duration;
        newBuff.m_buffType = this.m_buffType;
        newBuff.buffParams1 = this.buffParams1;
        newBuff.buffParams2 = this.buffParams2;
        newBuff.buffParams3 = this.buffParams3;
        newBuff.buffParams4 = this.buffParams4;
        newBuff.buffParams5 = this.buffParams5;
        newBuff.buffParams6 = this.buffParams6;
        newBuff.buffParams7 = this.buffParams7;
        return newBuff;
    }
}

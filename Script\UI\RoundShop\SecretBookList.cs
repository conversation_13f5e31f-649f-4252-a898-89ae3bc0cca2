using UnityEngine;
using System.Collections.Generic;
using Excel.shop;
using System.Linq;
public class SecretBookList : MonoBehaviour
{
    [SerializeField] GameObject prefabItem;
    private static int ShopId = (int)ShopType.SecretBook; // 当前商店Id
    private shopTypeEntity shopTypeEntity; // 当前商店类型参数
    private shopExcel excelData; // excel数据
    private int maxItemCount = 0; // 当前展示最大数量
    public bool isClickRefresh = false; // 是否手动刷新
    public bool isAutoRefresh = true; // 是否回合刷新
    private List<treasurePoolEntity> weightData; // 奖池类型配置

    private List<SecretItem> itemList = new List<SecretItem>();
    private void Awake() {
        // 加载Excel数据
        excelData = Resources.Load<shopExcel>("Excel/shopExcel");
        // 商店类型配置
        shopTypeEntity = excelData?.shopTypeList?.Find(x => x.id == ShopId);
        weightData = excelData?.treasurePoolList;
        if(shopTypeEntity != null) {
            maxItemCount = shopTypeEntity.gridNum;
            isClickRefresh = shopTypeEntity.malyRefresh == 1;
            isAutoRefresh = shopTypeEntity.autoRefresh == 1;
        }
    }
    // 查询购买次数
    private int getBuyNum(int id) {
        return -100;
    }
    // 查询是否存在前置
    private bool isExistFront(int id) {
        return false;
    }
    /// <summary>
    /// 获取权重列表
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private List<int> getListWeight(List<treasurePoolEntity> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    /// <summary>
    /// 判断是否禁止刷新回合
    /// </summary>
    /// <param name="target"></param>
    /// <returns></returns>
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    /// <summary>
    /// 筛选数据
    /// </summary>
    /// <returns></returns>
    private List<treasurePoolEntity> filterData() {
        List<treasurePoolEntity> result = new List<treasurePoolEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 购买次数限制筛选
            bool isMaxBuy = weightData[i].buyNumMax > 0 && getBuyNum(weightData[i].Id) >= weightData[i].buyNumMax;
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);
            // 前置条件筛选
            bool hasExistFront = weightData[i].refFront < 0 || (weightData[i].refFront > 0 && isExistFront(weightData[i].refFront));

            if(isRoundLimit && hasExistFront && !isMaxBuy) {
                result.Add(weightData[i]);
            }
        }
        return result;
    }
    // 渲染
    private void RenderItem(treasurePoolEntity data) {
        if(prefabItem == null) return;
        GameObject _instantiate = Instantiate(prefabItem, transform);
        SecretItem script = _instantiate.GetComponent<SecretItem>();
        script.isGift = false;
        script.shopOpen = true;
        script.onBuy(handleBuy);
        script.setBackImg(shopTypeEntity.BackResourcesShow);
        script.LoadItem(data);
        itemList.Add(script);
    }
    private void handleBuy (BaseGoodsItem valObject) {
        SecretItem sctipt = valObject as SecretItem;
        itemList.Remove(sctipt);
    }
    public void LoadData() {
        ClearChild();
        if(weightData == null || weightData.Count <= 0) return;
        for(int i = 0; i < maxItemCount; i++) {
            List<treasurePoolEntity> filter = filterData(); // 前置筛选
            List<int> weightList = getListWeight(filter); // 获取权重列表
            int index = WeightCalculator.GetWeightedRandomIndex(weightList); // 获取随机索引
            RenderItem(filter[index]); // 创建对应的实体
        }
    }
    private void ClearChild() {
        // 调用子节点的注销事件
        foreach (SecretItem item in itemList)
        {
            item.DestroyItem();
        }
        itemList.Clear();
    }
}
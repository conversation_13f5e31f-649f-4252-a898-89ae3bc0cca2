using UnityEngine;
using System.Linq;
using Excel.shop;
using System.Collections.Generic;
using System;
using Excel.hero;
using Excel.lable;
public class SkillCard : BaseGoodsItem
{
    [SerializeField] GameObject DetailWindow; // 详情窗口
    private List<gecang_skillEntity> weightData; //权重数据
    private List<heroSkillEntity> BaseSourceData; // 源数据
    public heroSkillEntity baseDataEntity; // 当前数据基本信息
    public AsideLeft asideLeft;

    void Awake() {
        // 加载Excel数据
        weightData = Resources.Load<shopExcel>("Excel/shopExcel")?.gecang_skillList;
        BaseSourceData = ExcelData.Instance.HeroExcel?.heroSkillList;
        asideLeft = GameObject.Find("AsideLeft")?.GetComponent<AsideLeft>();
    }

    // 获取权重列表
    private List<int> getListWeight(List<gecang_skillEntity> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    // 查询是否存在前置
    private bool isExistFront(int id) {
        List<heroSkillEntity> packageData = GameMain.Instance.m_listSkillBase;
        if(packageData == null || packageData.Count <= 0) 
        {
            return false;
        }
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                return true;
            }
        }
        return false;
    }
    // 查询购买次数
    private int getBuyNum(int id) {
        List<heroSkillEntity> packageData = GameMain.Instance.m_listSkillBase;
        if(packageData == null || packageData.Count <= 0) 
        {
            return 0;
        }
        int result = 0;
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                result++;
            }
        }
        return result;
    }
    // 判断回合限制
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    // 
    private bool isMaxLevel(int id) {
        List<AsideSkillItem> maxSkills = asideLeft.scripts.FindAll(x=> x.isInit).FindAll(x=> x.skillEntity.nextLvSkill < 0);
        if(maxSkills.Count <= 0) return false;
        // 判断是否是满级
        return maxSkills.Any(x=> x.skillEntity.cost == id);
    }
    // 筛选
    private List<gecang_skillEntity> filterData() {
        List<gecang_skillEntity> result = new List<gecang_skillEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 前置条件筛选
            bool hasExistFront = weightData[i].refFront < 0 || (weightData[i].refFront > 0 && isExistFront(weightData[i].refFront));
            // 购买次数限制筛选
            bool isMaxBuy = weightData[i].buyNumMax > 0 && getBuyNum(weightData[i].id) >= weightData[i].buyNumMax;
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);
            // 是否满级
            bool isMaxLv = isMaxLevel(weightData[i].id); 

            if(hasExistFront && !isMaxBuy && isRoundLimit && !isMaxLv) {
                result.Add(weightData[i]);
            }
        }
        return result;
    }
    
    private void setInfomation() 
    {
        if(itemsDetail == null || baseDataEntity == null) return;
        // 定义好符文的品质数据做转换
        itemsDetail.goodType = (int)GoodsType.Skill;
        itemsDetail.Title = Language.GetText(baseDataEntity.name); // 名字
        itemsDetail.level = baseDataEntity.skillLv; //等级
        // 标签信息
        List<MutationsLabelData> labelsList = new List<MutationsLabelData>();
        string [] labelsSplit = baseDataEntity.label.Split('|');
        for(int i = 0; i < labelsSplit.Length; i++)
        {
            int labelId = int.Parse(labelsSplit[i]);
            lableEntity label = ExcelData.Instance.getLableEntity(labelId);
            MutationsLabelData mutationsLabelData = new MutationsLabelData();
            mutationsLabelData.color = (BorderColor)label.quality;
            mutationsLabelData.text = Language.GetText(label.textId);
            labelsList.Add(mutationsLabelData);
        }
        itemsDetail.labels = labelsList;
        itemsDetail.Content = Language.GetText(int.Parse(baseDataEntity.text));
        itemsDetail.render();
    }

    // 创建技能实例
    private void renderUI(int id) {
        if(BaseSourceData == null || BaseSourceData.Count <= 0) return;
        heroSkillEntity target = BaseSourceData.Find(x=> x.id == id);
        if(target == null) return;
        setFrontImg(target.icon); // 图标
        baseBorderItem.CurrentBorder = target.quality;
        setPrice(target.price); // 价格
        baseDataEntity= target;
        // 详情
        setInfomation();
    }
    // 加载数据
    public override void LoadItem() {
        if(BaseSourceData == null || BaseSourceData.Count <= 0) return;
        List<gecang_skillEntity> filter = filterData(); // 前置筛选
        List<int> itemTypeList = getListWeight(filter); // 获取权重列表
        int index = WeightCalculator.GetWeightedRandomIndex(itemTypeList); // 根据权重获取随机索引
        int targetId = filter[index].id; // 根据索引获取对应的数据
        renderUI(targetId);
    }
    public override void LoadItem(int id) {
        renderUI(id);
    }
    public override void BuyItem() {
        if(baseDataEntity == null || asideLeft == null) {
            return;
        }
        // 尝试购买行为
        bool isAdd = asideLeft.checkAdd(baseDataEntity);
        if(!isAdd) {
            Debug.Log("校验不通过------");
            return;
        }
        bool payRes = GameMain.Instance.payJade(baseDataEntity.price);
        if(!payRes) {
            // 支付失败
            return;
        }
        // buyCallback?.Invoke(gameObject); // 购买成功回调
        asideLeft.addItem(baseDataEntity);
        base.BuyItem();
    }
    /// <summary>
    ///  选择
    /// </summary>
    public override void ChooseItem() {
        if(baseDataEntity == null || asideLeft == null) {
            return;
        }
        // 添加前校验
        bool checkRes = asideLeft.checkAdd(baseDataEntity);
        if(!checkRes) return;
        // chooseCallback?.Invoke(gameObject);
        asideLeft.addItem(baseDataEntity);
        base.ChooseItem();
    }
}
using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class BuildInsertedUI : MonoBehaviour
{
    [SerializeField] private TMP_Text NameText; // 名称
    [SerializeField] private TMP_Text LvText; // 等级
    [SerializeField] private TMP_Text AuxiliaryText; // 辅助
    [SerializeField] private TMP_Text TitleText; // 标题
    [SerializeField] private TMP_Text DescText; // 描述
    [SerializeField] private TMP_Text InsertedText; // 赋能
    [SerializeField] private TMP_Text CancelText; // 取消赋能
    [SerializeField] private TMP_Text UpdateText; // 替换
    [SerializeField] private TMP_Text UpgradeText; // 升级
    [SerializeField] private GameObject SlotPrefab; // 插槽预制体
    [SerializeField] private Transform SlotContainer; // 插槽容器
    [SerializeField] private UIRingLoader InsertedFn;
    [SerializeField] private UIRingLoader RemvoeFn;
    [SerializeField] private UIRingLoader ReplaceFn;
    [SerializeField] private UIRingLoader UpgradeFn;
    // 状态标记
    private bool canEmbed = false; // 是否可嵌入
    private bool canUpgrade = false; // 是否可升级
    private bool canRemove = false; // 是否可移除
    private bool canReplace = false; // 是否可替换
    private List<BaseGoodsData> replaceable = new List<BaseGoodsData>(); // 可替换的数据
    // 字典
    private Dictionary<KeyCode, int> keyCodeToIndex = new Dictionary<KeyCode, int>();
    private BuildingBase buildEntity;
    private GemGoods goodsItem;
    private bool isInserted = false;
    // 回调
    private Action<GemGoods> refreshFunction = null;
    private List<KeyCode> replaceKeyCodes = new List<KeyCode>();
    private bool whetherThereIsRoom = true;
    public bool IsInserted {
        get { return isInserted; }
        set { isInserted = value; }
    }
    public GemGoods GoodsItem { get=> goodsItem; }
    public void SetBuildingData(BuildingBase buildingBase, BaseGoodsData val) 
    {
        if(buildingBase == null  || val == null) return;
        replaceable.Clear();
        buildEntity = buildingBase;
        goodsItem = val as GemGoods;
        if(val != null)
        {
            var goodsData = goodsItem.dataEntity;
            NameText.text = Language.GetText(goodsData.name); // 名称
            bool whetherToAssist = goodsData.subType == 2; // 是否辅助类型
            // LvText.text = whetherToAssist ? "" : $"Lv.{goodsData.lv}"; // 等级
            AuxiliaryText.text = whetherToAssist ? "辅助" : "";//Language.GetText(); // 辅助
            LvText.gameObject.SetActive(!whetherToAssist); //   显示等级
            AuxiliaryText.gameObject.SetActive(whetherToAssist); // 显示辅助
            DescText.text = Language.GetText(goodsData.desc); //  描述
            UpgradeText.text = $"长按F键消耗{NameText.text}提升等级";
        }
        whetherThereIsRoom = InventoryManager.Other.RemainingSpace > 0;
        initStaticText();
        findInsertedBySubType();
        // 插槽初始化
        initSlotItem();
        // 更新UI状态
        UpdateButtonStates();
    }
    
    public void onRefresh(Action<GemGoods> action)
    {
        refreshFunction = action;
    }

    private void initStaticText() 
    {
        TitleText.text = "宝石赋能"; //Language.GetText(); // 标题
        InsertedText.text = "按下F键赋能";
        CancelText.text = whetherThereIsRoom ? "按下G键取消赋能" : "背包空间不足";
        UpdateText.text = "长按对应数字键替换赋能";
    }

    private void findInsertedBySubType()
    {
        int subType = goodsItem.dataEntity.subType;
        int unlock = Mathf.Clamp(buildEntity.CurrentLv, 0, buildEntity.MaxSlotNum);
        bool isSlot = unlock > buildEntity.SlotList.Count;

        // 嵌入槽内的同类型数据
        List<GemGoods> existingMain = getGoodsBySubType(buildEntity.SlotList, subType);
        // 已嵌入
        if(isInserted)
        {
            canRemove = true; // 允许移除
            return;
        }
        // 未嵌入且有位置
        if(isSlot)
        {
            // 主宝石替换主宝石逻辑
            if(subType == 1 && existingMain.Count > 0)
            {
                canReplace = true;
                replaceable.AddRange(existingMain);
                return;
            }
            // 新嵌入逻辑
            canEmbed = true;
            return;
        }
        // 主宝石存在主宝石时只替换主宝石
        List<GemGoods> addRangeData = subType == 1 && existingMain.Count > 0 ? existingMain : buildEntity.SlotList;
        // 嵌入的全是辅助
        replaceable.AddRange(addRangeData);// 全部标记可替换
        canReplace = replaceable.Count > 0;
    }
    private void viewUIRingLoader(UIRingLoader ringLoader, bool state = false, List<KeyCode> keyCodes = null)
    {
        ringLoader.transform.parent.gameObject.SetActive(state);
        if(keyCodes != null)
            ringLoader.TriggerKeyCodes = keyCodes;
    }
    private void UpdateButtonStates()
    {
        viewUIRingLoader(InsertedFn, canEmbed); // 赋能 F键
        List<KeyCode> removeKeys = new List<KeyCode>();
        if(whetherThereIsRoom)
        {
            removeKeys.Add(KeyCode.G);
        }
        viewUIRingLoader(RemvoeFn, canRemove, removeKeys); // 取消赋能 G键
        viewUIRingLoader(ReplaceFn, canReplace, replaceKeyCodes); // 替换 123键
        viewUIRingLoader(UpgradeFn, canUpgrade); // 升级 F键
        InsertedFn.onEnd(appendItem);
        RemvoeFn.onEnd(removeItem);
        ReplaceFn.onEnd(replaceItem);
    }

    private void appendItem()
    {
        if (goodsItem != null && buildEntity != null && canEmbed)
        {
            buildEntity.addGoodsToSlot(goodsItem); // 塔嵌入
            refreshFunction?.Invoke(goodsItem);
        }
    }
    
    private void removeItem()
    {
        if (goodsItem != null && buildEntity != null && canRemove)
        {
            buildEntity.removeGoodsFromSlot(goodsItem); // 塔拆
            refreshFunction?.Invoke(goodsItem);
        }
    }

    private void replaceItem(KeyCode keyCode)
    {
        if(!canReplace || buildEntity == null || goodsItem == null) return;
        // 检测索引是否存在
        if(!keyCodeToIndex.ContainsKey(keyCode)) return;
        // 替换的下标
        int slotIndex = keyCodeToIndex[keyCode];
        buildEntity.updateGoods(slotIndex, goodsItem);
        refreshFunction?.Invoke(goodsItem);
    }

    private List<GemGoods> getGoodsBySubType(List<GemGoods> val, int subType)
    {
        return val.FindAll(x=> x.dataEntity.subType == subType);
    }
    // 初始化插槽
    private void initSlotItem() 
    {
        if (buildEntity == null || SlotPrefab == null) return;
        int slotNumber = buildEntity.MaxSlotNum;
        var insertedList = buildEntity.SlotList;
        var buildLv = buildEntity.CurrentLv;
        bool viewInserted = false;
        replaceKeyCodes.Clear();
        keyCodeToIndex.Clear();
        for (int i = 0; i < slotNumber; i++) 
        {
            GameObject slotItem = Instantiate(SlotPrefab, SlotContainer);
            var script = slotItem.GetComponent<BuildEditTabItem>();
            script.IsTab = false; // 是否顶部tab
            script.IsInserted = true; // 是否插槽
            script.Index = i; // 索引
            script.LockSlot = i >= buildLv; // 锁定
            GemGoods initialData = insertedList.Count > i ? insertedList[i] : null;
            // 展示可嵌入状态
            if(initialData == null && i < buildLv && canEmbed && !viewInserted)
            {
                script.viewInserted = true;
                viewInserted = true;
            }
            // 展示可拆除状态
            if(canRemove  && initialData == goodsItem)
            {
                script.viewRemove = true && whetherThereIsRoom;
            }
            // 展示可替换状态
            if(initialData != null && canReplace && replaceable.Contains(initialData))
            {
                KeyCode keyCode = KeyCode.Alpha0 + (i +1);
                replaceKeyCodes.Add(keyCode); // 加入按键事件列表
                keyCodeToIndex.Add(keyCode, i); // 按键转换index
                script.viewReplace = true;
            }
            script.SetData(initialData);
        }
    }
}
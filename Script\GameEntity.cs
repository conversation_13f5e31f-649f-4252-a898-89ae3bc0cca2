using Excel.unit;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
///1=建筑
///2=士兵单位
///3=灵兽
///4=主角 

public enum ObjectType
{
    Building = 1,
    Soldier = 2,
    Pet = 3,
    Role = 4,
    Monster = 5,
}
/// <summary>
///1 = 恶魔族
///2 = 虫族
///3 = 兽族
///4 = 巨人族
/// </summary>
public enum RaceEnum
{
    None = 0,
    /// <summary>
    /// 恶魔族
    /// </summary>
    Demon = 1,
    /// <summary>
    /// 虫族
    /// </summary>
    Zerg,
    /// <summary>
    /// 兽族
    /// </summary>
    Orc,
    /// <summary>
    /// 巨人族
    /// </summary>
    Giant
}
public enum GameEntitClass
{
    None = 0,
    Self = 1,
    Monster = 2,
    Sprite = 3,
    Role = 4,
    Tower = 5
}

/// <summary>
///1 白_普通
///2 绿_稀有
///3 金_史诗
///4 红_传说
/// </summary>
public enum QualityEnum
{
    None = 0,
    /// <summary>
    /// 普通
    /// </summary>
    Common,
    /// <summary>
    /// 稀有
    /// </summary>
    Rare,
    /// <summary>
    /// 史诗
    /// </summary>
    Epic,
    /// <summary>
    /// 传说
    /// </summary>
    Legendary
}

public enum WeaponType
{
    EmptyHanded = 1,   // 空手
    Bow = 2,           // 弓
    Staff = 3,         // 杖
    OneHandSword = 4,  // 单手剑
    Club = 5,          // 木棍
    Greatsword = 6,    // 巨剑
    Spear = 7,         // 长枪
    DualBlades = 8,    // 双刀
    SwordAndShield = 9 // 剑盾
}
public class ThreatRecord
{
    public ThreatRecord(GameEntity entity, float time)
    {
        this.gameEntity = entity;
        this.time = time;
    }
    public float time;
    public GameEntity gameEntity;
}

public class GameEntityAndBuff
{
    public GameEntityAndBuff(GameEntity entity, Buff buff)
    {
        this.entity = entity;
        this.buff = buff;
    }
    public GameEntity entity;
    public Buff buff;
}

//buff对应的额外属性值
public class ExtraAttributeValue
{
    public ExtraAttributeValue(Buff buff, float extraValue)
    {
        this.buff = buff;
        this.extraValue = extraValue;
    }
    public Buff buff;
    public float extraValue;
}


public class AroundBuffCount
{
    public AroundBuffCount(Buff buff)
    {
        this.buff = buff;
    }
    public int Count = 0;
    public Buff buff;
}
[SerializeField]
public class AttributeChange
{
    /// <summary>
    /// 1，表示来自单位 unit，2，表示来自符文 rune, 3，表示来自buff
    /// </summary>
    public int type;
    public GameEntity gameEntity;
    public Rune rune;
    public Buff buff;

    public float value;
    private AttributeEnum attributeEnum;

    public AttributeChange(AttributeEnum attributeEnum, float value, Buff buff)
    {
        this.attributeEnum = attributeEnum;
        this.value = value;
        this.buff = buff;
        this.type = 3;
    }

    public AttributeChange(AttributeEnum attributeEnum, float value, Rune rune)
    {
        this.attributeEnum = attributeEnum;
        this.value = value;
        this.rune = rune;
        this.type = 2;
    }

    public AttributeChange(AttributeEnum attributeEnum, float value, GameEntity gameEntity)
    {
        this.attributeEnum = attributeEnum;
        this.value = value;
        this.gameEntity = gameEntity;
        this.type = 1;
    }
}


public class GameEntity : MonoBehaviour
{
    [SerializeField]
    protected CampEnum m_camp = CampEnum.Invader;
    public CampEnum Camp
    {
        get => m_camp;
        set
        {
            if (m_camp == CampEnum.Ally)
                gameObject.layer = Utility.layerAlly;
            else if (m_camp == CampEnum.Invader)
                gameObject.layer = Utility.layerInvader;
            else if (m_camp == CampEnum.Neutral)
                gameObject.layer = Utility.layerNeutral;
            m_camp = value;
        }
    }
    [SerializeField]
    protected int m_targetValue = 100;
    public int TargetValue { get => m_targetValue; set => m_targetValue = value; }

    [SerializeField]
    [ReadOnly]
    protected int m_id;
    public int ID { get => m_id; set => m_id = value; }

    protected string m_name;
    public string Name { get => m_name; set => m_name = value; }

    protected ObjectType m_objectType;
    public ObjectType ObjectType { get => m_objectType; set => m_objectType = value; }

    [SerializeField]
    protected List<Skill> m_skillList = new List<Skill>();
    public List<Skill> skillList { get => m_skillList; }

    /// <summary>
    /// 属性系数
    /// </summary>
    protected float m_attributeCoefficient = 1;
    /// <summary>
    /// 属性系数
    /// </summary>
    public float attributeCoefficient
    {
        get => m_attributeCoefficient;
        set
        {
            m_attributeCoefficient = value;
            for (int i = 0; i < m_skillList.Count; i++)
            {
                m_skillList[i].CastingRange = m_skillList[i].originalCastingRange * m_attributeCoefficient;
            }
        }
    }

    bool m_isHealthRecover;
    /// <summary>
    /// 是否有恢复系数
    /// </summary>
    public bool IsHealthRecover { get => m_isHealthRecover; set => m_isHealthRecover = value; }

    float m_healthRecoverCoefficient = 1;
    /// <summary>
    /// 恢复系数
    /// </summary>
    public float HealthRecoverCoefficient { get => m_healthRecoverCoefficient; set => m_healthRecoverCoefficient = value; }

    #region Attribute
    [SerializeField]
    private float m_health;
    public float Health { get => m_health + m_extraHealth + SumHealth; set => m_health = value; }

    [SerializeField]
    protected float m_currentHealth;
    /// <summary>
    /// 伤害不能使用此属性，用 NormalDamage, BuffDamage, SkillDamage
    /// </summary>
    public float CurrentHealth
    {
        get { return m_currentHealth; }
        set
        {
            //是否是增加
            if(value > m_currentHealth && m_isHealthRecover)
            {
                //增加的值
                float addValue = value - m_currentHealth;
                m_currentHealth = value;
                m_currentHealth += addValue * m_healthRecoverCoefficient;
            }
            else
                m_currentHealth = value;
            if (m_currentHealth <= 0)
            {
                m_isDeath = true;
                m_threatList.Clear();
            }
            if (m_currentHealth > this.Health)
                m_currentHealth = this.Health;
        }
    }

    private float m_armorBreak;
    /// <summary>
    /// 破防
    /// </summary>
    public float ArmorBreak { get => m_armorBreak + m_extraArmorBreak + SumArmorBreak; set => m_armorBreak = value; }

    [SerializeField]
    private float m_defense = 0;
    /// <summary>
    /// 防御
    /// </summary>
    public float Defense { get => m_defense + m_extraDefense + SumDefense; set => m_defense = value; }

    [SerializeField]
    private float m_attackDamage;
    /// <summary>
    /// 攻击
    /// </summary>
    public float AttackDamage { get => m_attackDamage + m_extraAttackDamage + SumAttackDamage; set => m_attackDamage = value; }
    [SerializeField]
    private float m_attackPercent;
    /// <summary>
    /// 攻击百分比
    /// </summary>
    public float AttackPercent { get => m_attackPercent + m_extraAttackPercent + SumAttackPercent; set => m_attackPercent = value; }
    [SerializeField]
    private float m_healthPercent;
    /// <summary>   
    /// 生命百分比
    /// </summary>
    public float HealthPercent { get => m_healthPercent + m_extraHealthPercent + SumHealthPercent; set => m_healthPercent = value; }
    [SerializeField]
    private float m_attackSpeed;
    /// <summary>
    /// 攻击速度
    /// </summary>
    public float AttackSpeed { get => m_attackSpeed; set => m_attackSpeed = value; }

    [SerializeField]
    private float m_moveSpeed;
    /// <summary>
    /// 移动速度
    /// </summary>  
    public float MoveSpeed { get => m_moveSpeed + m_extraMoveSpeed + SumMoveSpeed; set => m_moveSpeed = value; }


    [SerializeField]
    private float m_coolingReduced = 0;
    /// <summary>
    /// 冷却缩减
    /// </summary>
    public float CoolingReduced { get => m_coolingReduced + m_extraCoolingReduced + SumCoolingReduced; set => m_coolingReduced = value; }
    [SerializeField]
    private float m_damageBonus;
    /// <summary>
    /// 伤害加成
    /// </summary>
    public float DamageBonus { get => m_damageBonus + m_extraDamageBonus + SumDamageBonus; set => m_damageBonus = value; }
    [SerializeField]
    private float m_damageReduced;
    /// <summary>
    /// 伤害减免
    /// </summary>
    public float DamageReduced { get => m_damageReduced + m_extraDamageReduced + SumDamageReduced; set => m_damageReduced = value; }

    [SerializeField]
    private float m_attackRange = 1;
    public float AttackRange { get => m_attackRange + m_extraAttackRange + SumAttackRange; set => m_attackRange = value; }
    /// <summary>
    /// 原始数值
    /// </summary>
    protected float m_originalHealth = 0;
    public float originalHealth { get => m_originalHealth; set => m_originalHealth = value; }

    protected float m_originalArmorBreak = 0;
    public float originalArmorBreak { get => m_originalArmorBreak; set => m_originalArmorBreak = value; }

    protected float m_originalDefense = 0;
    public float originalDefense { get => m_originalDefense; set => m_originalDefense = value; }

    protected float m_originalAttackDamage = 0;
    public float originalAttackDamage { get => m_originalAttackDamage; set => m_originalAttackDamage = value; }

    protected float m_originalAttackPercent = 0;
    public float originalAttackPercent { get => m_originalAttackPercent; set => m_originalAttackPercent = value; }

    protected float m_originalHealthPercent = 0;
    public float originalHealthPercent { get => m_originalHealthPercent; set => m_originalHealthPercent = value; }

    protected float m_originalAttackSpeed = 0;
    public float originalAttackSpeed { get => m_originalAttackSpeed; set => m_originalAttackSpeed = value; }

    protected float m_originalMoveSpeed = 0;
    public float originalMoveSpeed { get => m_originalMoveSpeed; set => m_originalMoveSpeed = value; }

    protected float m_originalCoolingReduced = 0;
    public float originalCoolingReduced { get => m_originalCoolingReduced; set => m_originalCoolingReduced = value; }

    protected float m_originalDamageBonus = 0;
    public float originalDamageBonus { get => m_originalDamageBonus; set => m_originalDamageBonus = value; }

    protected float m_originalDamageReduced = 0;
    public float originalDamageReduced { get => m_originalDamageReduced; set => m_originalDamageReduced = value; }

    protected float m_originalAttackRange = 0;
    public float originalAttackRange { get => m_originalAttackRange; set => m_originalAttackRange = value; }

    protected List<AttributeChange> m_attributeHealth         = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeArmorBreak     = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeDefense        = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeAttackDamage   = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeAttackPercent  = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeHealthPercent  = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeAttackSpeed    = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeMoveSpeed      = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeCoolingReduced = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeDamageBonus    = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeDamageReduced  = new List<AttributeChange>();
    protected List<AttributeChange> m_attributeAttackRange    = new List<AttributeChange>();

    //public float SumHealth { get => m_attributeHealth.Sum(attribute => attribute.value); }
    //public float SumArmorBreak { get => m_attributeArmorBreak.Sum(attribute => attribute.value); }
    //public float SumDefense { get => m_attributeDefense.Sum(attribute => attribute.value); }
    //public float SumAttackDamage { get => m_attributeAttackDamage.Sum(attribute => attribute.value); }
    //public float SumAttackPercent { get => m_attributeAttackPercent.Sum(attribute => attribute.value); }
    //public float SumHealthPercent { get => m_attributeHealthPercent.Sum(attribute => attribute.value); }
    //public float SumAttackSpeed { get => m_attributeAttackSpeed.Sum(attribute => attribute.value); }
    //public float SumMoveSpeed { get => m_attributeMoveSpeed.Sum(attribute => attribute.value); }
    //public float SumCoolingReduced { get => m_attributeCoolingReduced.Sum(attribute => attribute.value); }
    //public float SumDamageBonus { get => m_attributeDamageBonus.Sum(attribute => attribute.value); }
    //public float SumDamageReduced { get => m_attributeDamageReduced.Sum(attribute => attribute.value); }
    //public float SumAttackRange { get => m_attributeAttackRange.Sum(attribute => attribute.value); }

    // Inspector中显示的Sum属性计算值
    [Space(10)]
    [Header("属性计算值 (只读)")]
    [SerializeField]
    [ReadOnly]
    private float m_sumHealthDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumArmorBreakDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumDefenseDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumAttackDamageDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumAttackPercentDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumHealthPercentDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumAttackSpeedDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumMoveSpeedDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumCoolingReducedDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumDamageBonusDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumDamageReducedDisplay;
    [SerializeField]
    [ReadOnly]
    private float m_sumAttackRangeDisplay;

    public float SumHealth    
    { 
        get 
        { 
            float value = m_attributeHealth.Sum(attribute => attribute.value);
            m_sumHealthDisplay = value;
            return value;
        } 
    }
    public float SumArmorBreak     
    { 
        get 
        { 
            float value = m_attributeArmorBreak.Sum(attribute => attribute.value);
            m_sumArmorBreakDisplay = value;
            return value;
        } 
    }
    public float SumDefense        
    { 
        get 
        { 
            float value = m_attributeDefense.Sum(attribute => attribute.value);
            m_sumDefenseDisplay = value;
            return value;
        } 
    }
    public float SumAttackDamage   
    { 
        get 
        { 
            float value = m_attributeAttackDamage.Sum(attribute => attribute.value);
            m_sumAttackDamageDisplay = value;
            return value;
        } 
    }
    public float SumAttackPercent  
    { 
        get 
        { 
            float value = m_attributeAttackPercent.Sum(attribute => attribute.value);
            m_sumAttackPercentDisplay = value;
            return value;
        } 
    }
    public float SumHealthPercent  
    { 
        get 
        { 
            float value = m_attributeHealthPercent.Sum(attribute => attribute.value);
            m_sumHealthPercentDisplay = value;
            return value;
        } 
    }
    public float SumAttackSpeed    
    { 
        get 
        { 
            float value = m_attributeAttackSpeed.Sum(attribute => attribute.value);
            m_sumAttackSpeedDisplay = value;
            return value;
        } 
    }
    public float SumMoveSpeed      
    { 
        get 
        { 
            float value = m_attributeMoveSpeed.Sum(attribute => attribute.value);
            m_sumMoveSpeedDisplay = value;
            return value;
        } 
    }
    public float SumCoolingReduced 
    { 
        get 
        { 
            float value = m_attributeCoolingReduced.Sum(attribute => attribute.value);
            m_sumCoolingReducedDisplay = value;
            return value;
        } 
    }
    public float SumDamageBonus    
    { 
        get 
        { 
            float value = m_attributeDamageBonus.Sum(attribute => attribute.value);
            m_sumDamageBonusDisplay = value;
            return value;
        } 
    }
    public float SumDamageReduced  
    { 
        get 
        { 
            float value = m_attributeDamageReduced.Sum(attribute => attribute.value);
            m_sumDamageReducedDisplay = value;
            return value;
        } 
    }
    public float SumAttackRange    
    { 
        get 
        { 
            float value = m_attributeAttackRange.Sum(attribute => attribute.value);
            m_sumAttackRangeDisplay = value;
            return value;
        } 
    }
    /// <summary>
    /// 附加数值
    /// </summary>
    protected float m_extraHealth = 0;
    protected float m_extraArmorBreak = 0;
    protected float m_extraDefense = 0;
    protected float m_extraAttackDamage = 0;
    protected float m_extraAttackPercent = 0;
    protected float m_extraHealthPercent = 0;
    protected float m_extraAttackSpeed = 0;
    protected float m_extraMoveSpeed = 0;
    protected float m_extraCoolingReduced = 0;
    protected float m_extraDamageBonus = 0;
    protected float m_extraDamageReduced = 0;
    protected float m_extraAttackRange = 0;
    #endregion
    /// <summary>
    /// buff 叠加计算属性的改变
    /// </summary>
    protected Dictionary<AttributeEnum, List<Buff>> m_AttributeChangeList = new Dictionary<AttributeEnum, List<Buff>>();

    /// <summary>
    /// buff周围单位的列表
    /// </summary>
    protected Dictionary<Buff, List<GameEntity>> m_aroundEntities = new Dictionary<Buff, List<GameEntity>>();
    /// <summary>
    /// 每个buff对应的属性值
    /// </summary>
    protected List<ExtraAttributeValue> m_extraAttributeValues = new List<ExtraAttributeValue>();

    protected List<GameEntityAndBuff> m_gameEntityAndBuffList = new List<GameEntityAndBuff>();
    [SerializeField]
    protected RaceEnum m_raceEnum = RaceEnum.None;
    public RaceEnum raceEnum { get => m_raceEnum; set => m_raceEnum = value; }

    protected MovementType m_movementType;
    public MovementType movementType { get => m_movementType; set => m_movementType = value; }

    protected CombatType m_CombatType;
    public CombatType CombatType { get => m_CombatType; }


    public float AttackSpeedChange
    {
        get => m_attackSpeedChange + m_extraAttackSpeedChange + SumAttackSpeed;

        set => m_attackSpeedChange = value;
    }
    /// <summary>
    /// 攻速增减
    /// </summary>
    [SerializeField]
    protected float m_attackSpeedChange = 1;

    protected float m_extraAttackSpeedChange = 0;

    protected float m_deathTime = 0;
    public float DeathTime { get => m_deathTime; }

    protected float m_threatTimeThreshold = 2;
    public float threatTimeThreshold { get => m_threatTimeThreshold; }
    /// <summary>
    /// 死亡后成为鬼魂，等播放完死亡动画后再销毁
    /// </summary>
    protected bool m_isDeath = false;
    /// <summary>
    /// 死亡后成为鬼魂，等播放完死亡动画后再销毁
    /// </summary>
    public bool isDeath { get => m_isDeath; set => m_isDeath = value; }


    /// <summary>
    /// 被禁止的行为
    ///1 = 停止移动
    ///2 = 停止所有技能与移动
    ///3 = 停止所有攻击与移动，包括普通攻击、技能、移动"
    /// </summary>
    [SerializeField]
    protected DisableActionEnum m_disableAction;

    public virtual DisableActionEnum disableAction { get => m_disableAction; set => m_disableAction = value; }

    public bool isDisableAttack { get => m_disableAction == DisableActionEnum.DisableAttack_Skill_Move; }

    public bool isDisableMove { get => m_disableAction != DisableActionEnum.None; }

    public bool isDisableSkill { get => m_disableAction == DisableActionEnum.DisableSkill_Move || m_disableAction == DisableActionEnum.DisableAttack_Skill_Move; }

    protected BuffSystem m_buffSystem;
    public BuffSystem buffSystem { get => m_buffSystem; }

    protected SkillEffectSystem m_skillEffectSystem;
    public SkillEffectSystem skillEffectSystem { get => m_skillEffectSystem; }

    protected Skill m_currentSkill;
    public virtual Skill currentSkill { get => m_currentSkill; set => m_currentSkill = value; }

    protected Skill m_normalSkill;
    public Skill normalSkill { get => m_normalSkill; set => m_normalSkill = value; }
    /// <summary>
    /// 进入技能流程
    /// </summary>
    protected bool m_IsInSkillCast = false;
    public bool IsInSkillCast
    {
        get => m_IsInSkillCast;
        set
        {
            m_IsInSkillCast = value;
            if (!m_IsInSkillCast)
            {
                m_currentSkill = null;
            }
        }
    }
    protected float m_castingRange = 3;
    public virtual float CastingRange { get => m_castingRange; set => m_castingRange = value; }


    [SerializeField]
    public Transform[] m_Anchors;

    /// <summary>
    /// 受到攻击时的威胁列表
    /// </summary>
    [SerializeField]
    protected List<ThreatRecord> m_threatList = new List<ThreatRecord>();
    public List<ThreatRecord> threatList { get => m_threatList; }

    //死亡后成为魂
    protected bool m_isGhost = false;

    public bool isGhost { get => m_isGhost; }

    //是不是克隆体
    protected bool m_isClone = false;
    public bool isClone { get => m_isClone; set => m_isClone = value; }

    /// <summary>
    ///1 白_普通
    ///2 绿_稀有
    ///3 金_史诗
    ///4 红_传说
    /// </summary>
    protected QualityEnum m_quality;
    public QualityEnum Quality { get => m_quality; }

    [SerializeField]
    protected float m_radius = 1f;
    public float Radius { get => m_radius; set => m_radius = value; }
    [SerializeField]
    protected int m_normalAttackCount;
    public int normalAttackCount { get => m_normalAttackCount; set => m_normalAttackCount = value; }

    protected int m_hitCount;
    public int hitCount { get => m_hitCount; set => m_hitCount = value; }

    protected int m_killCount;
    public int killCount { get => m_killCount; set => m_killCount = value; }

    /// <summary>
    /// 动作时长
    /// </summary>
    protected float m_idleAnimationLength;
    public float idleAnimationLength { get => m_idleAnimationLength; set => m_idleAnimationLength = value; }

    protected float m_runAnimationLength;
    public float runAnimationLength { get => m_runAnimationLength; set => m_runAnimationLength = value; }

    protected float m_attackAnimationLength;
    public float attackAnimationLength { get => m_attackAnimationLength; set => m_attackAnimationLength = value; }

    protected float m_PreSkillCastAnimationLength;
    public float PreSkillCastAnimationLength { get => m_PreSkillCastAnimationLength; set => m_PreSkillCastAnimationLength = value; }

    protected float m_SkillCastLoopAnimationLength;
    public float SkillCastLoopAnimationLength { get => m_SkillCastLoopAnimationLength; set => m_SkillCastLoopAnimationLength = value; }

    protected float m_PostSkillCastAnimationLength;
    public float PostSkillCastAnimationLength { get => m_PostSkillCastAnimationLength; set => m_PostSkillCastAnimationLength = value; }

    protected float m_damageAnimationLength;
    public float damageAnimationLength { get => m_damageAnimationLength; set => m_damageAnimationLength = value; }

    protected float m_deathAnimationLength;
    public float deathAnimationLength { get => m_deathAnimationLength; set => m_deathAnimationLength = value; }


    protected float m_lastAttackTime;
    public float lastAttackTime { get => m_lastAttackTime; set => m_lastAttackTime = value; }

    /// <summary>
    /// 跟随的列表
    /// </summary>
    protected List<Actor> m_followerList = new List<Actor>();
    public List<Actor> FollowerList { get => m_followerList; }

    public virtual void Awake()
    {
        m_skillEffectSystem = new SkillEffectSystem(this);
        m_buffSystem = new BuffSystem(this);
    }
    public virtual void OnEnable()
    {
        EventDispatcher.AddEventListener<GameEntity>(EventDispatcherType.ActorDeath, OnActorDeath);
    }

    public virtual void OnDisable()
    {
        EventDispatcher.RemoveEventListener<GameEntity>(EventDispatcherType.ActorDeath, OnActorDeath);
    }

    public float GetAttributeValue(AttributeEnum attributeEnum)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Health:
                return m_currentHealth;
            case AttributeEnum.Attack:
                return m_attackDamage;
            case AttributeEnum.AttackPercent:
                return m_attackPercent;
            case AttributeEnum.HealthPercent:
                return m_healthPercent;
            case AttributeEnum.AttackSpeed://攻速另外计算，返回攻速变化值
                return 1;// m_originalAttackSpeed;
            case AttributeEnum.MoveSpeed:
                return m_moveSpeed;
            case AttributeEnum.CoolingReduced:
                return m_coolingReduced;
            case AttributeEnum.DamageBonus:
                return m_damageBonus;
            case AttributeEnum.DamageReduced:
                return m_damageReduced;
            case AttributeEnum.ArmorBreak:
                return m_armorBreak;
            case AttributeEnum.Defense:
                return m_defense;
            default:
                return 0;
        }
    }

    public float GetAttributeOriginalValue(AttributeEnum attributeEnum)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Health:
                return m_originalHealth;
            case AttributeEnum.Attack:
                return m_originalAttackDamage;
            case AttributeEnum.AttackPercent:
                return m_originalAttackPercent;
            case AttributeEnum.HealthPercent:
                return m_originalHealthPercent;
            case AttributeEnum.AttackSpeed://攻速另外计算，返回攻速变化值
                return 1;// m_originalAttackSpeed;
            case AttributeEnum.MoveSpeed:
                return m_originalMoveSpeed;
            case AttributeEnum.CoolingReduced:
                return m_originalCoolingReduced;
            case AttributeEnum.DamageBonus:
                return m_originalDamageBonus;
            case AttributeEnum.DamageReduced:
                return m_originalDamageReduced;
            case AttributeEnum.ArmorBreak:
                return m_originalArmorBreak;
            case AttributeEnum.Defense:
                return m_originalDefense;
            default:
                return 0;
        }
    }

    public void SetAttributeOriginalValue(AttributeEnum attribute, float value)
    {
        switch (attribute)
        {
            case AttributeEnum.Attack:
                m_originalAttackDamage = value;
                break;
            case AttributeEnum.AttackPercent:
                m_originalAttackPercent = value;
                break;
            case AttributeEnum.AttackSpeed://攻速另外计算，这里保存攻速变化值
                //m_attackSpeed = value;
                m_attackSpeedChange = value;
                break;
            case AttributeEnum.Health:
                m_originalHealth = value;
                break;
            case AttributeEnum.HealthPercent:
                m_originalHealthPercent = value;
                break;
            case AttributeEnum.MoveSpeed:
                m_originalMoveSpeed = value;
                break;
            case AttributeEnum.CoolingReduced:
                m_originalCoolingReduced = value;
                break;
            case AttributeEnum.DamageBonus:
                m_originalDamageBonus = value;
                break;
            case AttributeEnum.DamageReduced:
                m_originalDamageReduced = value;
                break;
            case AttributeEnum.ArmorBreak:
                m_originalArmorBreak = value;
                break;
            case AttributeEnum.Defense:
                m_originalDefense = value;
                break;
            default:
                Debug.Assert(false);
                break;
        }
    }

    public void SetAttributeExtra(AttributeEnum attributeEnum, float value)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Attack:
                m_attackDamage += value;
                break;
            case AttributeEnum.AttackPercent:
                m_attackPercent += value;
                break;
            case AttributeEnum.AttackSpeed://攻速另外计算，这里保存攻速变化值
                //m_attackSpeed = value;
                m_attackSpeedChange += value;
                break;
            case AttributeEnum.Health:
                m_health += value;
                m_currentHealth = this.Health;
                break;
            case AttributeEnum.HealthPercent:
                m_healthPercent += value;
                break;
            case AttributeEnum.MoveSpeed:
                m_moveSpeed += value;
                break;
            case AttributeEnum.CoolingReduced:
                m_coolingReduced += value;
                break;
            case AttributeEnum.DamageBonus:
                m_damageBonus += value;
                break;
            case AttributeEnum.DamageReduced:
                m_damageReduced += value;
                break;
            case AttributeEnum.ArmorBreak:
                m_armorBreak += value;
                break;
            case AttributeEnum.Defense:
                m_defense += value;
                break;
            default:
                Debug.Assert(false);
                break;
        }
    }
    public void SetAttributeExtraOffset(AttributeEnum attributeEnum, float value)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Attack:
                m_extraAttackDamage += value;
                break;
            case AttributeEnum.AttackPercent:
                m_extraAttackPercent += value;
                break;
            case AttributeEnum.AttackSpeed://攻速另外计算，这里保存攻速变化值
                //m_attackSpeedChange += value;
                m_extraAttackSpeedChange += value;
                break;
            case AttributeEnum.Health:
                //m_health += value;
                m_extraHealth += value;
                m_currentHealth = this.Health;
                break;
            case AttributeEnum.HealthPercent:
                //m_healthPercent += value;
                m_extraHealthPercent += value;
                break;
            case AttributeEnum.MoveSpeed:
                //m_moveSpeed += value;
                m_extraMoveSpeed += value;
                break;
            case AttributeEnum.CoolingReduced:
                //m_coolingReduced += value;
                m_extraCoolingReduced += value;
                break;
            case AttributeEnum.DamageBonus:
                //m_damageBonus += value;
                m_extraDamageBonus += value;
                break;
            case AttributeEnum.DamageReduced:
                //m_damageReduced += value;
                m_extraDamageReduced += value;
                break;
            case AttributeEnum.ArmorBreak:
                //m_armorBreak += value;
                m_extraArmorBreak += value;
                break;
            case AttributeEnum.Defense:
                //m_defense += value;
                m_extraDefense += value;
                break;
            default:
                Debug.Assert(false);
                break;
        }
    }

    public void AddAttributeChangeByBuff(AttributeEnum attributeEnum, float valueChange, Buff buff)
    {
        AttributeChange attributeChange = new AttributeChange(attributeEnum, valueChange, buff);
        AddAttributeChange(attributeEnum, attributeChange);
    }

    public void AddAttributeChangeByGameEntity(AttributeEnum attributeEnum, float valueChange, GameEntity gameEntity)
    {
        AttributeChange attributeChange = new AttributeChange(attributeEnum, valueChange, gameEntity);
        AddAttributeChange(attributeEnum, attributeChange);
    }

    public void AddAttributeChangeByRune(AttributeEnum attributeEnum, float valueChange, Rune rune)
    {
        AttributeChange attributeChange = new AttributeChange(attributeEnum, valueChange, rune);
        AddAttributeChange(attributeEnum, attributeChange);
    }
    //属性值的叠加
    public void AddAttributeChange(AttributeEnum attributeEnum, AttributeChange change)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Attack:
                m_attributeAttackDamage.Add(change);
                break;
            case AttributeEnum.AttackPercent:
                m_attributeAttackPercent.Add(change);
                break;
            case AttributeEnum.AttackSpeed:
                m_attributeAttackSpeed.Add(change);
                break;
            case AttributeEnum.Health:
                m_attributeHealth.Add(change);
                m_currentHealth = this.Health;
                break;
            case AttributeEnum.HealthPercent:
                m_attributeHealthPercent.Add(change);
                break;
            case AttributeEnum.MoveSpeed:
                m_attributeMoveSpeed.Add(change);
                break;
            case AttributeEnum.CoolingReduced:
                m_attributeCoolingReduced.Add(change);
                break;
            case AttributeEnum.DamageBonus:
                m_attributeDamageBonus.Add(change);
                break;
            case AttributeEnum.DamageReduced:
                m_attributeDamageReduced.Add(change);
                break;
            case AttributeEnum.ArmorBreak:
                m_attributeArmorBreak.Add(change);
                break;
            case AttributeEnum.Defense:
                m_attributeDefense.Add(change);
                break;
            default:
                Debug.Assert(false);
                break;
        }
    }
    //移除值的叠加
    public void DelAttributeChange(AttributeEnum attributeEnum, Buff buff)
    {
        List<AttributeChange> list = GetAttributeChanges(attributeEnum);
        for (int i = list.Count - 1; i >= 0; i--)
        {
            AttributeChange attributeChange = list[i];
            if (attributeChange.buff == buff)
            {
                list.RemoveAt(i);
            }
        }
        if( attributeEnum == AttributeEnum.Health )
            m_currentHealth = this.Health;
    }

    public void DelAttributeChange(AttributeEnum attributeEnum, GameEntity gameEntity)
    {
        List<AttributeChange> list = GetAttributeChanges(attributeEnum);
        for (int i = list.Count - 1; i >= 0; i--)
        {
            AttributeChange attributeChange = list[i];
            if (attributeChange.gameEntity == gameEntity)
            {
                list.RemoveAt(i);
            }
        }
    }

    public void DelAttributeChange(AttributeEnum attributeEnum, Rune rune)
    {
        List<AttributeChange> list = GetAttributeChanges(attributeEnum);
        for (int i = list.Count - 1; i >= 0; i--)
        {
            AttributeChange attributeChange = list[i];
            if (attributeChange.rune == rune)
            {
                list.RemoveAt(i);
            }
        }
    }

    public List<AttributeChange> GetAttributeChanges(AttributeEnum attributeEnum)
    {
        switch (attributeEnum)
        {
            case AttributeEnum.Attack:
                return m_attributeAttackDamage;
            case AttributeEnum.AttackPercent:
                return m_attributeAttackPercent;
            case AttributeEnum.AttackSpeed:
                return m_attributeAttackSpeed;
            case AttributeEnum.Health:
                return m_attributeHealth;
            case AttributeEnum.HealthPercent:
                return m_attributeHealthPercent;
            case AttributeEnum.MoveSpeed:
                return m_attributeMoveSpeed;
            case AttributeEnum.CoolingReduced:
                return m_attributeCoolingReduced;
            case AttributeEnum.DamageBonus:
                return m_attributeDamageBonus;
            case AttributeEnum.DamageReduced:
                return m_attributeDamageReduced;
        }
        return null;
    }
    public void SetAttribute(AttributeEnum attribute, float value)
    {
        switch (attribute)
        {
            case AttributeEnum.Attack:
                m_attackDamage = value;
                break;
            case AttributeEnum.AttackPercent:
                m_attackPercent = value;
                break;
            case AttributeEnum.AttackSpeed://攻速另外计算，这里保存攻速变化值
                //m_attackSpeed = value;
                m_attackSpeedChange = value;
                break;
            case AttributeEnum.Health:
                m_health = value;
                m_currentHealth = m_health;
                break;
            case AttributeEnum.HealthPercent:
                m_healthPercent = value;
                break;
            case AttributeEnum.MoveSpeed:
                m_moveSpeed = value;
                break;
            case AttributeEnum.CoolingReduced:
                m_coolingReduced = value;
                break;
            case AttributeEnum.DamageBonus:
                m_damageBonus = value;
                break;
            case AttributeEnum.DamageReduced:
                m_damageReduced = value;
                break;
            case AttributeEnum.ArmorBreak:
                m_armorBreak = value;
                break;
            case AttributeEnum.Defense:
                m_defense = value;
                break;
            default:
                Debug.Assert(false);
                break;
        }
    }
    /// <summary>
    /// 叠加计算属性得改变
    /// </summary>
    /// <param name="attributeEnum"></param>
    /// <param name="newBuff"></param>
    public void AttributeChange(AttributeEnum attributeEnum)
    {
        List<Buff> list = m_AttributeChangeList[attributeEnum];
        float OriginalValue = GetAttributeOriginalValue(attributeEnum);
        float totalAddValue = 0;
        Buff buffOne;
        float coefficient = 0;
        int mode = 0;
        for (int i = 0; i < list.Count; i++)
        {
            buffOne = list[i];
            coefficient = buffOne.BuffData.buffParams2 * buffOne.StackCount;

            mode = (int)buffOne.BuffData.buffParams3;

            float value = 0;
            if (mode == 1)
                value = coefficient;
            else if (mode == 2)
                value = OriginalValue * coefficient;
            else if (mode == 3)
                value = OriginalValue + coefficient;
            else if (mode == 4)
                value = OriginalValue * Mathf.Max(0, 1 + coefficient);

            if ((int)buffOne.BuffData.buffParams4 > 0)
            {
                if (value > buffOne.BuffData.buffParams5)
                    value = buffOne.BuffData.buffParams5;
                if (value < buffOne.BuffData.buffParams6)
                    value = buffOne.BuffData.buffParams6;
            }
            totalAddValue += (value - OriginalValue);
        }
        //if (totalAddValue != 0)
        SetAttribute(attributeEnum, OriginalValue + totalAddValue);
    }

    public void AddAttributeBuff(Buff newBuff)
    {
        AttributeEnum attributeEnum = (AttributeEnum)newBuff.BuffData.buffParams1;
        if (!m_AttributeChangeList.ContainsKey(attributeEnum))
        {
            m_AttributeChangeList.Add(attributeEnum, new List<Buff>());
        }
        if (!m_AttributeChangeList[attributeEnum].Contains(newBuff))
        {
            m_AttributeChangeList[attributeEnum].Add(newBuff);
        }
    }
    public void DelAttributeChange(Buff delBuff)
    {
        AttributeEnum attributeEnum = (AttributeEnum)delBuff.BuffData.buffParams1;

        m_AttributeChangeList[attributeEnum].RemoveAll(buff => buff == delBuff);
    }

    public virtual void AttributeEnhancement(float coefficient)
    {
        attributeCoefficient = coefficient;
        m_attackDamage = m_attributeCoefficient * m_originalAttackDamage;
        m_health = m_attributeCoefficient * m_originalHealth;
        m_currentHealth = m_attributeCoefficient * m_originalHealth;
        m_armorBreak = m_attributeCoefficient * m_originalArmorBreak;
        m_defense = m_attributeCoefficient * m_originalDefense;
        m_attackPercent = m_attributeCoefficient * m_originalAttackPercent;
        m_healthPercent = m_attributeCoefficient * m_originalHealthPercent;
        m_attackSpeed = m_attributeCoefficient * m_originalAttackSpeed;
        m_moveSpeed = m_attributeCoefficient * m_originalMoveSpeed;
        m_coolingReduced = m_attributeCoefficient * m_originalCoolingReduced;
        m_damageBonus = m_attributeCoefficient * m_originalDamageBonus;
        m_damageReduced = m_attributeCoefficient * m_originalDamageReduced;

    }

    public virtual void AttributeRestore()
    {
        m_attackDamage = m_originalAttackDamage;
        m_health = m_originalHealth;
        m_currentHealth = m_originalHealth;
        m_armorBreak = m_originalArmorBreak;
        m_defense = m_originalDefense;
        m_attackPercent = m_originalAttackPercent;
        m_healthPercent = m_originalHealthPercent;
        m_attackSpeed = m_originalAttackSpeed;
        m_moveSpeed = m_originalMoveSpeed;
        m_coolingReduced = m_originalCoolingReduced;
        m_damageBonus = m_originalDamageBonus;
        m_damageReduced = m_originalDamageReduced;
    }



    public virtual bool CannotBeSelected { get => false; }

    public Transform GetAnchor(AnchorType type)
    {
        int index = (int)type - 1;
        if (index < 0 || index >= m_Anchors.Length) return transform;
        return m_Anchors[index];
    }

    public virtual void PlayDeath()
    {
    }

    public virtual Skill getActiveSkill()
    {
        return null;
    }

    public Skill AddSkill(int id)
    {
        Skill skill = m_skillList.Find(skill => skill.SkillID == id);
        if (skill != null) return skill;

        skill = new Skill(id, this);

        skill.CastingRange = skill.originalCastingRange * m_attributeCoefficient;

        m_skillList.Add(skill);
        m_skillList.Sort((left, right) => right.Priority.CompareTo(left.Priority));
        ResetCastingRange();
        m_normalSkill = m_skillList.Find(element => element.Priority == Skill.PriorityEnum.Normal);
        return skill;
    }
    public void RemoveSkill(int skillID)
    {
        m_skillList.RemoveAll(skill => skill.SkillID == skillID);
        m_buffSystem.RemoveBuff(skillID);
    }

    protected void ResetCastingRange()
    {
        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (m_skillList[i].CastingRange > 0 && m_castingRange > m_skillList[i].CastingRange)
            {
                m_castingRange = m_skillList[i].CastingRange;
            }
        }
    }

    /// <summary>
    /// 普攻造成的伤害
    /// </summary>
    /// <param name="damage"></param>
    public virtual void NormalDamage(float damage)
    {
        float preHealth = CurrentHealth;

        ApplyDamage(damage);
        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (m_currentSkill == m_skillList[i]) continue;
            m_skillList[i].CheckHealthTrigger(Health, CurrentHealth, preHealth);
        }
    }

    /// <summary>
    /// 反弹伤害
    /// </summary>
    /// <param name="damage"></param>
    public virtual void ReboundDamage(float damage)
    {
        ApplyDamage(damage);
    }
    /// <summary>
    /// 技能造成的伤害
    /// </summary>
    /// <param name="attacker"></param>
    /// <param name="damage"></param>
    public virtual void SkillDamage(GameEntity attacker, Skill skill, float damage)
    {
        attacker.hitCount++;

        AddThreat(attacker);

        if (skill.Priority == Skill.PriorityEnum.Normal)
        {
            attacker.normalAttackCount++;
        }

        if (m_buffSystem.haveDamageImmunity(skill, 1, BuffData.MainEnum.None))
        {
            FloatTextManager.Instance.Add("0", GetAnchor(AnchorType.Top).position);
            return;
        }

        float preHealth = CurrentHealth;
        //反弹伤害
        m_buffSystem.ReboundDamage(attacker, damage);

        //伤害减免
        Buff buff = buffSystem.getDamageReduction();
        if (buff != null)
        {
            //受到什么的攻击获得受伤加成
            //- 1 = 任意敌方的攻击
            //1 = 主角
            //2 = 士兵单位
            //3 = 防御塔
            int type = (int)buff.BuffData.buffParams1;
            if (type == -1
              || (type == 1 && attacker is Role)
              || (type == 2 && attacker.ObjectType == ObjectType.Soldier)
              || (type == 3 && attacker is GuardTower))
            {
                float coefficient = buff.BuffData.buffParams2;
                damage = damage * (1 + coefficient);
            }
        }
        //部分伤害持续
        buff = m_buffSystem.getPartDamageOverTime();
        if (buff != null)
        {
            float partDamage = damage * buff.BuffData.buffParams1;
            int count = (int)(buff.BuffData.buffParams2 / 1000.0f);
            PartDamage pd = new PartDamage();
            pd.splitDamage = partDamage / count;
            pd.damage = partDamage;
            pd.interval = 1;
            pd.lastTime = Time.time;

            buff.partDamageList.Add(pd);

            damage = damage * (1 - buff.BuffData.buffParams1);
        }

        ApplyDamage(damage, attacker);
        //检测是否有关于生命值的技能
        for (int i = 0; i < m_skillList.Count; i++)
        {
            if (m_currentSkill == m_skillList[i]) continue;

            m_skillList[i].CheckHealthTrigger(Health, CurrentHealth, preHealth);
        }

        attacker.AttackResult(damage);
        //m_attributeSet.CurrenHealth -= damage;
    }
    /// <summary>
    /// buff造成的伤害
    /// </summary>
    /// <param name="buff"></param>
    /// <param name="damage"></param>
    public virtual void BuffDamage(Buff buff, float damage)
    {
        if (m_buffSystem.haveDamageImmunity(buff.bindSkill, 0, buff.BuffData.MainType))
        {
            FloatTextManager.Instance.Add("0", GetAnchor(AnchorType.Top).position);
            return;
        }

        ApplyDamage(damage);

        buff.Attacher.AttackResult(damage);
        //m_attributeSet.CurrenHealth -= damage;
    }

    /// <summary>
    /// 伤害生效
    /// </summary>
    /// <param name="damage"></param>
    public virtual void ApplyDamage(float damage, GameEntity attacker = null)
    {
        if (m_buffSystem.haveUnbeatable) return;

        if (m_buffSystem.haveDamageBonus(attacker))
        {
            damage = damage * m_buffSystem.DamageBonus;
        }

        if (m_buffSystem.haveShield)
        {
            //盾消耗自身防御伤害
            Buff buff = m_buffSystem.GetShieldBuff();
            if (buff.ShieldHealth > 0)
            {
                if (buff.ShieldHealth < damage)
                {
                    damage = (damage - buff.ShieldHealth);
                    CurrentHealth -= damage;
                    buff.ShieldHealth = 0;
                }
                else
                {
                    buff.ShieldHealth -= damage;
                }
            }
        }
        else
        {
            CurrentHealth -= damage;
        }
        if (attacker != null && CurrentHealth <= 0)
        {
            attacker.killCount++;
        }
        FloatTextManager.Instance.Add(((int)damage).ToString(), GetAnchor(AnchorType.Top).position);

        EventDispatcher.TriggerEvent(EventDispatcherType.ShowHealthBar, this);
    }


    public virtual void HealthRecover(float recover)
    {
        if (isGhost) return;
        CurrentHealth += recover;

        FloatTextManager.Instance.Add(((int)recover).ToString(), GetAnchor(AnchorType.Top).position, 3, Color.green);

        EventDispatcher.TriggerEvent(EventDispatcherType.ShowHealthBar, this);
    }
    //public virtual void Revive(float health)
    //{

    //}

    public virtual void ChangeAttribute(int attrID, float coefficient, int mode)
    {
        //1 = 效果目标自身的该项属性值 * 系数值
        //2 = 效果目标自身的该项属性值 + 系数值
        //当改变的属性类型为生命值时，是改变目标的生命上限，且改变后，若当前剩余的生命值超出生命上限，则将剩余的生命值自动降低到与生命上限持平，改变后的生命值上限最少为1

    }

    public virtual void BeatFly(GameEntity target, float height, float airTime, float upSpeed, float downSpeed, bool interrupting)
    {

    }

    public virtual void BeatBack(Vector3 dir, float distance, float speed, bool interrupting)
    {

    }

    public virtual void Pull(GameEntity target, Vector3 to, float speed, bool interrupting)
    {
    }
    public virtual void DoRush(GameEntity target, Skill skill, float distance, float speed, string effectID)
    {
    }

    public virtual void JumpTo(GameEntity target, float height, float time)
    {

    }

    public virtual void AddBuff(int buffPoolID, Skill skill)
    {
        m_buffSystem.AddBuffPool(buffPoolID, skill);
    }
    public virtual void RemoveBuff(int mainType, int subType)
    {
        m_buffSystem.RemoveBuff(mainType, subType);
    }

    public virtual void AttackResult(float damage)
    {
    }

    public virtual void OnDeath()
    {
        gameObject.SetActive(false);
        GameObject.Destroy(gameObject, 3);
    }

    public void Destroy()
    {
        gameObject.SetActive(false);
        GameObject.Destroy(gameObject, 3);
    }
    public void AddSkillEffect(Skill skill, List<SkillEffect> listEffect, GameEntity attacker)
    {
        SkillEffectBase effect;
        for (int i = 0; i < listEffect.Count; i++)
        {
            effect = listEffect[i];// new SkillEffect(skill, int.Parse(effectArray[i]), this);
            effect.bindActor = this;
            effect.Releaser = attacker;
            effect.isTrigger = false;
            m_skillEffectSystem.AddEffect(effect);
        }
    }

    public void AddSkillEffect(Skill skill, string effectID, GameEntity attacker)
    {
        string[] effectArray = effectID.Split('|');
        SkillEffectBase effect;
        for (int i = 0; i < effectArray.Length; i++)
        {
            effect = new SkillEffect(skill, int.Parse(effectArray[i]), this);
            effect.Releaser = attacker;
            effect.bindSkill = skill;
            m_skillEffectSystem.AddEffect(effect);
        }
    }


    public virtual void AddFiveElementDamage(GameEntity attacker, ObjectType attackerType, int damageType, int damageNum)
    {

    }

    public virtual void OnActorDeath(GameEntity entity)
    {
        //if (this == entity) return;
        if (this != entity && m_camp == entity.Camp) return;
        m_threatList.RemoveAll(data => data.gameEntity == entity);

        for (int i = 0; i < m_skillList.Count; ++i)
        {
            //if (m_currentSkill == m_skillList[i]) continue;
            m_skillList[i].CheckDeathTrigger(this, entity);
        }
    }
    // 添加 仇恨目标
    public void AddThreat(GameEntity enemy, bool updateTime = true)
    {
        if (m_objectType == ObjectType.Building) return;
        if (enemy.isDeath) return;
        if (enemy.Camp == CampEnum.Neutral) return;
        if (enemy.Camp == m_camp) return;

        //判断是否已经在列表里
        ThreatRecord threat = m_threatList.Find(threat => threat.gameEntity == enemy);
        if (threat != null)
        {
            if (updateTime)
                threat.time = Time.time;
            return;
        }
        //if (enemy.Camp == CampEnum.Ally)
        //    Debug.Log("Add Threat:" + enemy.name);

        m_threatList.Add(new ThreatRecord(enemy, Time.time));
    }
    // 获取仇恨目标 
    public GameEntity GetHighestThreatTarget()
    {
        if (m_threatList.Count == 0) return null;

        float minDistance = 60;

        //主角不能主动跑向敌人，所以忽略超出攻击距离的敌人
        if (m_objectType == ObjectType.Role)
            minDistance = CastingRange;

        //GameEntity gameEntity = null;
        m_threatList.Sort((left, right) => right.time.CompareTo(left.time));

        //float distance = 0;
        //for (int i = 0; i < m_threatList.Count; i++)
        //{
        //    distance = Vector3.Distance(transform.position, m_threatList[i].gameEntity.transform.position);
        //    if (distance < minDistance)
        //    {
        //        minDistance = distance;
        //        gameEntity = m_threatList[i].gameEntity;
        //    }
        //}
        return m_threatList[0].gameEntity;
    }

    public virtual void Update()
    {
        //List<ThreatRecord> list = m_threatList.FindAll(threat => (Time.time - threat.time) > m_threatTimeThreshold);
        //if( list.Count > 0 && Camp == CampEnum.Invader )
        //{
        //    for (int i = 0; i < list.Count; i++)
        //        Debug.Log("remove :" + list[i].gameEntity.name);
        //}
        m_threatList.RemoveAll(threat => (Time.time - threat.time) > m_threatTimeThreshold);

        UpdateAroundBuff();
    }
    public bool haveThreat()
    {
        return m_threatList.Count > 0;
    }
    public bool isThreat(GameEntity gameEntity)
    {
        return m_threatList.Exists(threat => threat.gameEntity == gameEntity);
    }

    public void materialSetOpcity(Material material, float opacity)
    {
        material.SetFloat("_Surface", 1);
        material.SetOverrideTag("RenderType", "Transparent");
        material.SetInt("_Blend", 1);
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        material.SetInt("_ZWrite", 0);
        material.renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
        material.EnableKeyword("_SURFACE_TYPE_TRANSPARENT");

        Color color = material.GetColor("_BaseColor");
        color.a = opacity;
        material.SetColor("_BaseColor", color);
    }

    public void recoverMaterial(Material material)
    {
        material.SetFloat("_Surface", 0);
        material.SetOverrideTag("RenderType", "Opaque");
        material.SetInt("_Blend", 0);
        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.One);
        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.Zero);
        material.SetInt("_ZWrite", 1);
    }

    /// <summary>
    /// 完成一回合后恢复数值
    /// </summary>
    public virtual void Renew()
    {
        for (int i = 0; i < m_skillList.Count; i++)
        {
            m_skillList[i].Reset();
        }
        m_threatList.Clear();
        m_buffSystem.Clear();
        m_isDeath = false;
        m_isGhost = false;
        m_currentHealth = this.Health;
        m_killCount = 0;
        m_normalAttackCount = 0;
        m_hitCount = 0;
    }

    public virtual void ReplaceSkill(int oldSkillID, int newSkillID)
    {
        m_skillList.RemoveAll(s => s.SkillID == oldSkillID);
        AddSkill(newSkillID);
    }

    public static void Distance(GameEntity entity1, GameEntity entity2)
    {

    }

    public void ClearSkill()
    {
        m_skillEffectSystem.Clear();
        m_buffSystem.Clear();
        for (int i = 0; i < m_skillList.Count; i++)
        {
            m_skillList[i].Clear();
        }
        m_skillList.Clear();

    }

    //加入影响周围单位属性的BUFF
    public void AddAroundBuff(Buff buff)
    {
        if (!m_aroundEntities.ContainsKey(buff))
            m_aroundEntities.Add(buff, new List<GameEntity>());

        //如果没有这个buff的属性值，添加一个
        if (!m_extraAttributeValues.Exists(e => e.buff == buff))
            m_extraAttributeValues.Add(new ExtraAttributeValue(buff, 0));
    }

    public void RemoveAroundBuff(Buff buff)
    {
        if (!m_aroundEntities.ContainsKey(buff))
            return;

        List<GameEntity> list = m_aroundEntities[buff];
        for (int i = 0; i < list.Count; i++)
        {
            GameEntity entity = list[i];
            GameEntityAndBuff gameEntityAndBuff = m_gameEntityAndBuffList.Find(element => element.entity == entity);
            if (gameEntityAndBuff != null)
            {
                entity.buffSystem.RemoveBuff(gameEntityAndBuff.buff);
                m_gameEntityAndBuffList.Remove(gameEntityAndBuff);
            }
        }
        m_aroundEntities.Remove(buff);

        ExtraAttributeValue extra = m_extraAttributeValues.Find(e => e.buff == buff);
        if (extra != null)
        {
            AttributeEnum attributeEnum = (AttributeEnum)(int)buff.BuffData.buffParams1;

            SetAttributeExtra(attributeEnum, -extra.extraValue);
            m_extraAttributeValues.Remove(extra);
        }

        return;

    }


    List<GameEntity> m_removeGameEntityList = new List<GameEntity>();
    List<GameEntity> m_newEntityList = new List<GameEntity>();//确定是新的被选中单位
    /// <summary>
    /// 改变周围单位属性的BUFF
    /// </summary>
    public void UpdateAroundBuff()
    {
        GameEntity entity;
        AttributeEnum attributeEnum;
        List<GameEntity> lastList;
        List<GameEntity> inRangeList;
        Buff buff;
        float radius = 1;
        foreach (var pair in m_aroundEntities)
        {
            m_newEntityList.Clear();
            m_removeGameEntityList.Clear();

            buff = pair.Key;
            lastList = pair.Value;

            if (buff.buffType == BuffData.BuffEnum.GainPowerBasedAllies)
                radius = buff.BuffData.buffParams4;
            else if (buff.buffType == BuffData.BuffEnum.ChangeAttributesInRange)
                radius = buff.BuffData.buffParams2;

            inRangeList = GameMain.Instance.GetFriend(this, radius);

            //不在新的里面，说明已经不在范围内了
            for (int i = 0; i < lastList.Count; i++)
            {
                //不在新的里面.要删除
                if (!inRangeList.Exists(n => n == lastList[i]))
                {
                    m_removeGameEntityList.Add(lastList[i]);
                }
            }
            //不在老的里面，说明是新的
            for (int i = 0; i < inRangeList.Count; i++)
            {
                //不存在，是新的
                if (!lastList.Exists(n => n == inRangeList[i]))
                    m_newEntityList.Add(inRangeList[i]);
            }
            if (m_newEntityList.Count > 0)
            {
                for (int i = 0; i < m_newEntityList.Count; i++)
                {
                    entity = m_newEntityList[i];
                    m_aroundEntities[buff].Add(entity);
                }
            }

            //移除超出范围的单位
            for (int i = 0; i < m_removeGameEntityList.Count; i++)
            {
                lastList.RemoveAll(gb => gb == m_removeGameEntityList[i]);
            }

            if (buff.buffType == BuffData.BuffEnum.GainPowerBasedAllies)
            {
                //先还原
                attributeEnum = (AttributeEnum)(int)buff.BuffData.buffParams1;
                ExtraAttributeValue extra = m_extraAttributeValues.Find(e => e.buff == buff);
                SetAttributeExtra(attributeEnum, -extra.extraValue);
                //再计算新的值
                float coefficient = buff.BuffData.buffParams3 * lastList.Count;
                extra.extraValue = coefficient;
                SetAttributeExtra(attributeEnum, extra.extraValue);
            }
            else if (buff.buffType == BuffData.BuffEnum.ChangeAttributesInRange)
            {
                for (int i = 0; i < m_newEntityList.Count; i++)
                {
                    entity = m_newEntityList[i];
                    if (m_gameEntityAndBuffList.Exists(element => element.entity == entity)) continue;

                    if ((int)buff.BuffData.buffParams1 == 1)
                    {
                        if (entity.ObjectType != ObjectType.Building)
                            continue;
                        if (entity is not GuardTower)
                            continue;
                    }
                    if ((int)buff.BuffData.buffParams1 == 2)
                    {
                        if (entity.ObjectType == ObjectType.Building)
                            continue;
                    }

                    Buff newbuff = new Buff((int)buff.BuffData.buffParams3);
                    entity.buffSystem.AddBuff(newbuff);
                    m_gameEntityAndBuffList.Add(new GameEntityAndBuff(entity, newbuff));
                }
                GameEntityAndBuff gameEntityAndBuff = null;
                for (int i = 0; i < m_removeGameEntityList.Count; i++)
                {
                    entity = m_removeGameEntityList[i];

                    gameEntityAndBuff = m_gameEntityAndBuffList.Find(elemnet => elemnet.entity == entity);
                    if (gameEntityAndBuff == null) continue;

                    entity.buffSystem.RemoveBuff(gameEntityAndBuff.buff);

                    m_gameEntityAndBuffList.Remove(gameEntityAndBuff);
                }
            }
        }
        m_removeGameEntityList.Clear();
    }

    /// <summary>
    /// 增强的BUFF属性管理系统
    /// </summary>
    #region Enhanced Buff Management

    /// <summary>
    /// 批量添加BUFF属性变化（用于复杂BUFF处理）
    /// </summary>
    /// <param name="buff">BUFF源</param>
    /// <param name="attributeChanges">属性变化字典: AttributeEnum -> 变化值</param>
    public void AddBuffAttributeChanges(Buff buff, Dictionary<AttributeEnum, float> attributeChanges)
    {
        foreach (var kvp in attributeChanges)
        {
            AddAttributeChangeByBuff(kvp.Key, kvp.Value, buff);
        }
        
        // 记录BUFF ID以便调试
        Debug.Log($"[BuffManager] Added buff {buff.BuffData?.buffID} with {attributeChanges.Count} attribute changes to {name}");
    }

    /// <summary>
    /// 批量移除BUFF属性变化
    /// </summary>
    /// <param name="buff">要移除的BUFF源</param>
    public void RemoveBuffAttributeChanges(Buff buff)
    {
        // 移除所有与此BUFF相关的属性变化
        foreach (AttributeEnum attrType in System.Enum.GetValues(typeof(AttributeEnum)))
        {
            DelAttributeChange(attrType, buff);
        }
        
        Debug.Log($"[BuffManager] Removed all attribute changes for buff {buff.BuffData?.buffID} from {name}");
    }

    /// <summary>
    /// 获取当前所有活跃的属性变化信息（调试用）
    /// </summary>
    /// <returns>格式化的属性变化信息</returns>
    public string GetActiveAttributeChangesInfo()
    {
        var info = new System.Text.StringBuilder();
        info.AppendLine($"=== {name} 的活跃属性变化 ===");
        
        foreach (AttributeEnum attrType in System.Enum.GetValues(typeof(AttributeEnum)))
        {
            var changes = GetAttributeChanges(attrType);
            if (changes != null && changes.Count > 0)
            {
                info.AppendLine($"{attrType}: {changes.Count} 个变化");
                foreach (var change in changes)
                {
                    string source = change.buff != null ? $"Buff({change.buff.BuffData?.buffID})" :
                                  change.rune != null ? $"Rune({change.rune.name})" :
                                  change.gameEntity != null ? $"Entity({change.gameEntity.name})" : "Unknown";
                    info.AppendLine($"  - {source}: {change.value:F2}");
                }
            }
        }
        
        return info.ToString();
    }

    /// <summary>
    /// 验证属性系统完整性（调试用）
    /// </summary>
    /// <returns>是否存在问题</returns>
    public bool ValidateAttributeSystem()
    {
        bool hasIssues = false;
        
        foreach (AttributeEnum attrType in System.Enum.GetValues(typeof(AttributeEnum)))
        {
            var changes = GetAttributeChanges(attrType);
            if (changes != null)
            {
                // 检查是否有空引用
                for (int i = changes.Count - 1; i >= 0; i--)
                {
                    var change = changes[i];
                    if (change.buff == null && change.rune == null && change.gameEntity == null)
                    {
                        Debug.LogWarning($"[BuffManager] Found orphaned attribute change for {attrType} in {name}");
                        changes.RemoveAt(i);
                        hasIssues = true;
                    }
                }
            }
        }
        
        return !hasIssues;
    }

    /// <summary>
    /// 清理所有无效的属性变化
    /// </summary>
    public void CleanupInvalidAttributeChanges()
    {
        foreach (AttributeEnum attrType in System.Enum.GetValues(typeof(AttributeEnum)))
        {
            var changes = GetAttributeChanges(attrType);
            if (changes != null)
            {
                // 移除已经销毁的BUFF或Entity的引用
                for (int i = changes.Count - 1; i >= 0; i--)
                {
                    var change = changes[i];
                    bool shouldRemove = false;
                    
                    if (change.buff != null && change.buff.isExpired)
                        shouldRemove = true;
                    else if (change.gameEntity != null && (change.gameEntity == null || change.gameEntity.isDeath))
                        shouldRemove = true;
                        
                    if (shouldRemove)
                    {
                        changes.RemoveAt(i);
                        Debug.Log($"[BuffManager] Cleaned up invalid attribute change for {attrType}");
                    }
                }
            }
        }
    }

    #endregion
}

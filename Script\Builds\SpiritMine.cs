using Excel.unit;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
/// <summary>
/// ???
/// </summary>
public class SpiritMine: BuildingBase
{
    /// <summary>
    /// ??????????????
    /// </summary>
    private int m_turnCount = 1;
    /// <summary>
    ///??????????
    /// </summary>
    private int m_jadeCount = 1;
    private int m_turnPassCount = 0;
    public int turnPassCount { get => m_turnPassCount; }
    public override void Start()
    {
        IdGroup = 103;
        m_currentLv = 0;
        SetData();
        // m_OpenBuildUIEvent = EventDispatcherType.OpenSpiritMineBuildUI;
        // m_OpenUpgradeUIEvent = EventDispatcherType.OpenSpiritMineSelectUI;

        base.Start();
        this.ShowMark();
        this.Build();
    }

    public override void Build()
    {
        m_candidateElfList.Clear();
        for (int i = 0; i < Role.Instance.elfList.Count; i++)
        {
            m_candidateElfList.Add((Elf)Role.Instance.elfList[i]);
        }

        Upgrade();
        // EventDispatcher.TriggerEvent(EventDispatcherType.CloseSpiritMineBuildUI);
        base.Build();
    }

    public override void Upgrade()
    {
        m_currentLv++;
        SetData();
        base.Upgrade();
    }

    // public override void OnTriggerExit(Collider other)
    // {
    //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseSpiritMineBuildUI);
    //     EventDispatcher.TriggerEvent(EventDispatcherType.CloseSpiritMineSelectUI);
    //     base.OnTriggerExit(other);
    // }

    public override void EnterElf(int index)
    {
        if (m_bindElf != null)
        {
            ExitElf();
        }
        base.EnterElf(index);
        m_bindElf.elfActionType = ElfActionType.Miner;
    }

    public override void ExitElf()
    {
        base.ExitElf();
    }

    public override void ExtendSet()
    {
        if(m_currentLv == 0) return;
        m_turnCount = int.Parse(m_Effect.effectParam2);
        m_jadeCount = int.Parse(m_Effect.effectParam3);
    }

    public override void OnTurnNext()
    {
        m_turnPassCount++;
        if (m_turnPassCount >= m_turnCount)
        {
            m_turnPassCount = 0;
            if (m_currentHealth > 0)
            {
                Transform trans = GetAnchor(AnchorType.Center);

                GameObject prefab = Resources.Load("Building\\Prefab\\FlyJade") as GameObject;

                GameObject go = GameObject.Instantiate(prefab);
                go.transform.position = trans.position + new Vector3(Random.Range(-3.0f, 3.0f), 0, Random.Range(-3.0f, 3.0f));
                FlyJade jade = go.GetComponent<FlyJade>();
                jade.jadeCount = m_jadeCount;
            }
        }
        base.OnTurnNext();
    }

    public override string GetIntroduceTxt()
    {
        resourcesShowEntity showEntity = ExcelData.Instance.UnitExcel.resourcesShowList
                                            .Find(show => show.architectureId == IdGroup && show.lv == 1);
        string format = Language.GetText(showEntity.txt);
        return string.Format( format, m_turnCount - m_turnPassCount);
    }

    public override string GetBuildNameTxt()
    {
        return "???";
    }
    public override string GetLvText()
    {
        return "1";
    }
    public override string GetHpTxt()
    {
        return base.GetHpTxt();
    }

    // public override void OpenUpgradeUI()
    // {
    //     m_candidateElfList.Clear();
    //     for (int i = 0; i < Role.Instance.elfList.Count; i++)
    //     {
    //         m_candidateElfList.Add((Elf)Role.Instance.elfList[i]);
    //     }
    //     base.OpenUpgradeUI();
    // }
}

using Excel.rune;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuneTarget
{ 
    private targetEntity m_runeTarget;
    private List<GameEntity> m_listTarget = new List<GameEntity>();
    public List<GameEntity> listTarget { get => m_listTarget; }

    public int TargetType { get => m_runeTarget.customType; }

    public void SetData(targetEntity runeTarget)
    {
        this.m_runeTarget = runeTarget;
    } 

    public List<GameEntity> getTarget() 
    {
        if(m_runeTarget == null ) return null;
        //范围内离主角最近的防御塔
        if (m_runeTarget.customType == 1)
        {
            return TowerClosestToRoleWithInRange();
        }
        //获得建筑
        else if (m_runeTarget.customType == 2)
        {
            return GetBuilding();
        }
        //所有士兵单位
        else if (m_runeTarget.customType == 3)
        {
            return GetAllUnit();
        }
        //特殊状态下的士兵
        else if (m_runeTarget.customType == 4)
        {
            return GetUnit();
        }
        //城墙范围内的士兵
        else if (m_runeTarget.customType == 5)
        {
            return null;
        }
        //远程攻击士兵
        else if (m_runeTarget.customType == 6)
        {
            return GetRangedAttackUnit();
        }
        //主角
        else if (m_runeTarget.customType == 7)
        {
            m_listTarget.Clear();
            m_listTarget.Add(Role.Instance);
            return m_listTarget;
        }
        //火属性积累值满后触发专属效果并处于该效果持续时间内的任意敌方单位
        else if (m_runeTarget.customType == 8)
        {
            m_listTarget.Clear();
            List<Actor> listGanmeEntity = GameMain.Instance.InvaderList;
            for (int i = 0; i < listGanmeEntity.Count; i++)
            {
                if (listGanmeEntity[i] is Monster && ((Monster)listGanmeEntity[i]).IsInFireElement)
                    m_listTarget.Add(listGanmeEntity[i]);
            }
            return m_listTarget;
        }
        //野兽区域挑战时间
        else if (m_runeTarget.customType == 9)
        {
            return null;
        }
        //自身金币数量
        else if (m_runeTarget.customType == 10)
        {
            return null;
        }
        //当前存活的所有士兵型建筑
        else if (m_runeTarget.customType == 11)
        {
            return GetUnitBuildings();
        }
        //牧场所有的灵羊
        else if (m_runeTarget.customType == 12)
        {
            m_listTarget.Clear();
            List<Actor> list = GameMain.Instance.AllyList;
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i] is Sheep)
                    m_listTarget.Add(list[i]);
            }
            return m_listTarget;
        }
        //带有冰冻异常状态的所有技能
        //（能造成冰冻状态的所有技能）
        else if (m_runeTarget.customType == 13)
        {
            return null;
        }
        //自身灵玉数量
        else if (m_runeTarget.customType == 14)
        {
            return null;
        }
        //挑战完成后的野兽区域对应的建筑
        else if (m_runeTarget.customType == 15)
        {

        }
        //辅助宝石
        else if (m_runeTarget.customType == 16)
        {
        }
        return null;
    }

    private List<GameEntity> TowerClosestToRoleWithInRange()
    {
        m_listTarget.Clear();
        float range = float.Parse(m_runeTarget.customParams1);
        float rangeSqr = range * range;
        float distanceSqr = 0;
        List<BuildingBase> list = GameMain.Instance.ListCompletedBuildings;
        for( int i = 0; i < list.Count; i++)
        {
            BuildingBase building = list[i];
            if ( building.Camp == Role.Instance.Camp && building is GuardTower)
            {
                 distanceSqr = Vector3.SqrMagnitude(building.transform.position - Role.Instance.transform.position);
                if (distanceSqr < rangeSqr)
                    m_listTarget.Add(building);
            }
        }
        return m_listTarget;
    }

    private List<GameEntity> GetBuilding()
    {
        m_listTarget.Clear();
        BuildingTypeEnum type = (BuildingTypeEnum)(int.Parse(m_runeTarget.customParams1));
        int buildingId = int.Parse(m_runeTarget.customParams2);
        int buildingLv = int.Parse(m_runeTarget.customParams3);
        List<BuildingBase> listBuilding = GameMain.Instance.ListCompletedBuildings;
        for (int i = 0; i < listBuilding.Count; i++)
        {
            BuildingBase building = listBuilding[i];
            if (building.buildType == type)
            {
                if( buildingId == -1 )
                {
                    if (buildingLv == -1)
                        m_listTarget.Add(building);
                    else if (building.CurrentLv == buildingLv)
                        m_listTarget.Add(building);
                }
                else if (buildingId == building.IdGroup)
                {
                    if (buildingLv == -1)
                        m_listTarget.Add(building);
                    else if (building.CurrentLv == buildingLv)
                        m_listTarget.Add(building);
                }
            }
        }
        return m_listTarget;
    }

    private List<GameEntity> GetAllUnit()
    {
        m_listTarget.Clear();
        m_listTarget.AddRange( GameMain.Instance.AllyList);
        return m_listTarget;
    }

    private List<GameEntity> GetUnit()
    {
        m_listTarget.Clear();
        //1 = 警戒
        //2 = 固守
        //3 = 跟随主角
        int type = int.Parse(m_runeTarget.customParams1);

        List<Actor> list = GameMain.Instance.AllyList;
        for (int i = 0; i < list.Count; i++)
        {
            if( type == 3)
            {
                if (list[i].isFollow)
                    m_listTarget.Add(list[i]);
            }

        }
        return m_listTarget;
    }

    private List<GameEntity> GetRangedAttackUnit()
    {
        m_listTarget.Clear();

        List<Actor> list = GameMain.Instance.AllyList;
        for (int i = 0; i < list.Count; i++)
        {
            if(list[i].CombatType == CombatType.RangedUnit)
                m_listTarget.Add(list[i]);
        }
        return m_listTarget;
    }

    //当前存活的所有士兵型建筑
    private List<GameEntity> GetUnitBuildings()
    {
        m_listTarget.Clear();
        List<BuildingBase> list = GameMain.Instance.ListCompletedBuildings;
        for (int i = 0; i < list.Count; i++)
        {
            if (!list[i].isDeath && (list[i] is Barracks || list[i] is ArcherCamp))
            {
                m_listTarget.Add(list[i]);
            }
        }
        return m_listTarget;
    }
}

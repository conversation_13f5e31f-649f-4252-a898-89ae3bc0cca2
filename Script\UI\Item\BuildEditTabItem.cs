using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class BuildEditTabItem : MonoBehaviour
{
    [Header("Border Color")]
    [SerializeField] private Image borderImg;
    [SerializeField] private BorderColor DefaultBorder = BorderColor.White;
    [SerializeField] private List<ColorsBindSprite> borderList;
    [SerializeField] private Image soltImg; // 对应插槽
    [SerializeField] private Image iconImg; // 图标
    [SerializeField] private LabelIcon labelIcon; // label 或者等级
    [SerializeField] private Image lockImg; // 锁
    [SerializeField] private GameObject selectUplv; // 升级选中icon
    [SerializeField] private GameObject selectInserted; // 选中添加 icon
    [SerializeField] private GameObject selectRemove; // 选中移除 icon
    [SerializeField] private GameObject selectNumber; // 选中替换选择
    [SerializeField] private List<Sprite> slotIcons; // 插槽icon
    [SerializeField] private TMP_Text selectNumberText; // 替换选择按键的数字
    [SerializeField] private GameObject MaskImg; // 遮罩
    private bool isTab = false; // 是否是tab
    private int index = -1; // 索引
    private bool isInsert = false;
    private BaseGoodsItem dataEntity;
    public bool viewInserted  
    {
        set 
        {
            selectInserted.SetActive(value);
        }
    }
    public bool viewRemove
    {
        set 
        {
            selectRemove.SetActive(value);
        }
    }
    public bool viewReplace
    {
        set 
        {
            selectNumber.SetActive(value);
            selectNumberText.text = (index + 1).ToString();
        }
    }
    public int Index {
        set 
        {
            index = value;
            initSlotIcon();
        }
    }
    // 是否是插槽
    public bool IsInserted
    {
        get
        {
            return isInsert;
        }
        set
        {
            isInsert = value;
            if(isInsert)
            {
                soltImg.gameObject.SetActive(value);
                initSlotIcon();
            }
        }
    }
    public bool IsTab {
        get 
        {
            return isTab;
        }
        set 
        {
            isTab = value;
            if (value) 
            {
                LockSlot = false;
            }
        }
    }
    // 选中状态 升级
    public bool SelectUpLv {
        set 
        {
            soltImg.gameObject.SetActive(value);
        }
    }
    // 解锁状态
    public bool LockSlot {
        set 
        {
            lockImg.gameObject.SetActive(value);
        }
    }
    public Sprite Icon 
    {
        set 
        {
            if (value != null && iconImg != value) 
            {
                iconImg.sprite = value;
                iconImg.gameObject.SetActive(true);
            }
        }
    }
    // 等级
    private int m_currentBorder;
    public int CurrentBorder
    {
        get 
        {
            return m_currentBorder;
        }
        set 
        {
            if(m_currentBorder != value) 
            {
                m_currentBorder = value;
                setBorderColor();
            }
        }
    }
    public bool Active
    {
        set 
        {
           MaskImg.SetActive(!value); 
        }
    }
    public void SetData(BaseGoodsData goodsItem) 
    {
        GemGoods goods = goodsItem as GemGoods;
        if(goods == null) return;
        var data = goods.dataEntity;
        Icon = Resources.Load<Sprite>(data.icon);
        labelIcon.gameObject.SetActive(true);
        if(data.subType == 2)
        {
            string _text = "辅"; // Language.GetText();
            labelIcon.RenderDefault(_text);
        } else
        {
            // labelIcon.FindRender(data.lv);
        }
        CurrentBorder = data.quality;

    }
    private void setBorderColor() 
    {
        // 修改当前组件的边框img
        if(borderList == null || borderList.Count <= 0) return;
        borderImg.sprite = borderList?.Find(x => x.color == (BorderColor)m_currentBorder)?.sprite;
    }
    private void initSlotIcon() 
    {
        if(slotIcons == null || slotIcons.Count <= 0) return;
        int _index = isTab ? index - 1 : index;

        if(index >= 0 && _index < slotIcons.Count) 
        {
            soltImg.sprite = slotIcons[_index];
        }
    }
}
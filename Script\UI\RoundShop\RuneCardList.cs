using UnityEngine;
using System.Collections.Generic;
using Excel.shop;
using System;
using System.Linq;
using Excel.rune;
using System.ComponentModel;
public enum GoodsType {
    // 符文
    [Description("1027")]
    Rune = 1,
    // 宝石
    [Description("1036")]
    Gems = 2,
    // 精华
    [Description("1040")]
    Essence = 3,
    // 技能
    [Description("1038")]
    Skill = 4,
    // 圣物
    [Description("910004")]
    Artifact = 5
}

[Serializable]
public class GoodsToPrefab {
    public GoodsType goodsType;
    public GameObject prefab;
}
public class RuneCardList : MonoBehaviour
{
    [SerializeField] List<GoodsToPrefab> Prefabs;
    public BtnEvent reloadNext; // 刷新事件组件
    private static int ShopId = (int)ShopType.Rune; // 当前商店Id
    private shopExcel excelData; // excel数据
    private shopTypeEntity shopTypeEntity; // 当前商店类型参数
    private int maxItemCount = 0; // 当前展示最大数量
    public bool isClickRefresh = false; // 是否手动刷新
    public bool isAutoRefresh = true; // 是否回合刷新
    private List<gecangConfEntity> weightData; // 奖池类型配置
    private List<gecang_runeEntity> gecang_runeList; // 符文商品列表
    private bool runeLoadOver = false; // 符文是否加载完全部的
    public List<BaseGoodsItem> itemList = new List<BaseGoodsItem>(); // 子节点列表
    public List<BaseGoodsItem> prelloadRune = new List<BaseGoodsItem>(); // 上一次刷新出来的符文
    public List<BaseGoodsItem> loadRune = new List<BaseGoodsItem>(); // 这次刷新出来的符文
    void Awake()
    {
        // 加载Excel数据
        excelData = ExcelData.Instance.ShopExcel;
        // 商店类型配置
        shopTypeEntity = excelData?.shopTypeList?.Find(x => x.id == ShopId);
        // 符文数据
        gecang_runeList = excelData?.gecang_runeList;
        // 奖池类型配置
        weightData = excelData?.gecangConfList;
        // 初始化数据
        if(shopTypeEntity != null) {
            isClickRefresh = shopTypeEntity.malyRefresh == 1;
            isAutoRefresh = shopTypeEntity.autoRefresh == 1;
            maxItemCount = shopTypeEntity.gridNum;
        }
    }

    /// <summary>
    /// 判断是否禁止刷新回合
    /// </summary>
    /// <param name="target"></param>
    /// <returns></returns>
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    /// <summary>
    /// 筛选数据
    /// </summary>
    /// <returns></returns>
    private List<gecangConfEntity> filterData() {
        List<gecangConfEntity> result = new List<gecangConfEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);
            // 符文数据是否被加载完
            bool isRuneLoadOver = weightData[i].id == (int)GoodsType.Rune && runeLoadOver;
            if(isRoundLimit && !isRuneLoadOver) {
                result.Add(weightData[i]);
            }
        }
        return result;
    }
    /// <summary>
    /// 获取权重列表
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private List<int> getListWeight(List<gecangConfEntity> val) {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0)
        {
            return list;
        }
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }

    // 执行抽奖
    public void LoadData() {
        ClearChild();
        // 清空上上次数据
        prelloadRune.Clear();
        // 将上次的数据保存
        prelloadRune.AddRange(loadRune);
        loadRune.Clear();
        updateLoadable();
        if(weightData == null || weightData.Count <= 0) return;
        for(int i = 0; i < maxItemCount; i++) {
            List<gecangConfEntity> filter = filterData(); // 前置筛选
            List<int> weightList = getListWeight(filter); // 获取权重列表
            int index = WeightCalculator.GetWeightedRandomIndex(weightList); // 获取随机索引
            getItemInfomation(filter[index]); // 创建对应的实体
        }
    }

    // 获取物品信息
    private void getItemInfomation(gecangConfEntity data) {
        int type = Mathf.Clamp(data.id, 1, 5);
        if(Prefabs == null && Prefabs.Count <= 0) return;
        // 获取对应类型的商品
        GoodsToPrefab _prefab = Prefabs.Find(x => x.goodsType == (GoodsType)type);
        GameObject _instantiate = Instantiate(_prefab.prefab, transform);
        BaseGoodsItem script = _instantiate.GetComponent<BaseGoodsItem>();
        script.isGift = false;
        script.shopOpen = true;
        script.parentObject = gameObject;
        script.onBuy(x => itemList.Remove(x)); // 购买事件
        script.setBackImg(data.BackResourcesShow); // 设置背面图片
        script.LoadItem();
        itemList.Add(script); // 添加到列表
        if(type == (int)GoodsType.Rune)
        {
            loadRune.Add(script);
        }
    }

    /// <summary>
    /// 获取当前可加载的符文卡数量（未被展示在列表中的符文数量）
    /// </summary>
    /// <returns>可加载的符文数量，若数据异常返回0</returns>
    private int getLoadableRune()
    {
        // 防御性编程：检查数据源有效性
        if (gecang_runeList == null || gecang_runeList.Count <= 0)
        {
            Debug.LogWarning("gecang_runeList 未加载");
            return 0;
        }
        
        int result = 0;
        
        // 当前已购买的符文商品
        List<BaseGoodsData> buyRune = RuneInventory.Instance.buyList;

        foreach (gecang_runeEntity rune in gecang_runeList)
        {
            // 检查当前符文是否不在buyRune和prelloadRune中
            bool isInBuyRune = buyRune != null && buyRune.Any(r => r.id == rune.id);
            // 上回合刷新的数据
            bool isInPrelloadRune = prelloadRune != null && prelloadRune.Any(r => r.baseGoodsData.id == rune.id);
            // 当前已加载的符文商品
            bool isInLoadRune = loadRune != null && loadRune.Any(r => r.baseGoodsData.id == rune.id);
            // 检查refFront条件
            bool refFrontConditionMet = true;
            if (rune.refFront > 0) // 假设refFront为0或负数表示无依赖
            {
                // 如果refFront > 0，则需要buyRune中存在ID等于refFront的符文
                refFrontConditionMet = buyRune != null && buyRune.Any(r => r.id == rune.refFront);
            }
            // 如果两个集合中都不存在，则添加到可用符文列表
            if (!isInBuyRune && !isInPrelloadRune && refFrontConditionMet && !isInLoadRune)
            {
                result++;
            }
        }
        
        return result;
    }
    /// <summary>
    /// 更新可加载符文数量
    /// </summary>
    private void updateLoadable() {
        int loadabel = getLoadableRune();
        runeLoadOver = loadabel <= 0;
        // Debug.Log($"当前可加载符文数量：{loadabel}");
    }

    /// <summary>
    /// 清除所有子物体
    /// </summary>
    private void ClearChild() {
        for (int i = 0; i < itemList.Count; i++)
        {
            itemList[i].DestroyItem();   
        }
        itemList.Clear();
    }
}
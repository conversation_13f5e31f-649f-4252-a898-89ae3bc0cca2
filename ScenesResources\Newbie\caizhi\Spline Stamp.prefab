%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5657714721650481488
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3496617390046746722}
  - component: {fileID: 7365457323408026342}
  m_Layer: 0
  m_Name: Spline Stamp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3496617390046746722
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5657714721650481488}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 72.8, y: -12.3, z: 401.89996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7365457323408026342
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5657714721650481488}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfb48a9f575b2464fa7c04deba8020d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stamp: {fileID: 11400000, guid: 4c51d7aaafd629b40b28f9b2ead896b5, type: 2}
  maskOverride: {fileID: 0}
  maskMap:
    mapType: 2
    input: 3
    target: 0
    modifier:
      modifierType: 2
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  heightMap:
    mapType: 0
    input: 1
    target: 1
    modifier:
      modifierType: 0
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  colorMap:
    mapType: 1
    input: 2
    target: 2
    modifier:
      modifierType: 1
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  highlightMap:
    mapType: 5
    input: 0
    target: 19
    modifier:
      modifierType: 4
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 0
    selfIndex: 0
  roadMaskMap:
    mapType: 4
    input: 12
    target: 0
    modifier:
      modifierType: 2
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 0
    selfIndex: 0
  stampMaps:
  - mapType: 3
    input: 0
    target: 3
    modifier:
      modifierType: 0
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 0
    selfIndex: 0
  holeMap:
    mapType: 6
    input: 0
    target: 20
    modifier:
      modifierType: 5
      blendMode: 0
      blendRatio: 1
      edgeBlend: 1
      opacity: 1
      power: 1
      edgeErase: 0
      offset: 0
      multiplier: 1
      cutoffMin: 0
      cutoffMax: 1
      invert: 0
      brightness: 1
      contrast: 1
      saturation: 1
      hue: 0
      enabled: 0
      edgeMode: 0
      edgeFade: 0.1
      edgeSize: 1
      edgeOpacity: 1
      maskInvert: 0
      heightSelectFrom: 0
      heightSelectTo: 0.1
    inspectorInitialized: 1
    selfIndex: 0
  center: {x: 139.10873, y: 0, z: -89.263504}
  size: {x: 694.5522, y: 8, z: 635.81494}
  roadBlending: 0
  tileMask: 0
  stampTiling: {x: 1, y: 1}
  width: 20
  tiling: 1
  fadeStart: 0.1
  fadeEnd: 0.9
  closed: 0
  quality: 1
  qualitySettings:
    maxLength: 9.85
    maxDistanceBetweenPoints: 100
    widthSegments: 2
  meshExportSettings:
    material: {fileID: 2100000, guid: 486a475a4dfd3c740a6a69177355289e, type: 2}
    width: 3
    tiling: 100
    maxDistanceBetweenPoints: 1
    maxPoints: 10000
    widthSegments: 5
    edgePushDown: -0.2
    midPushUp: 0.2
    fadeStart: 0.1
    fadeEnd: 0.9
    useSplitting: 1
    splitGridSize: 1000
    splitGridOffset: {x: 0, y: 0}
  editMode: 0
  points:
  - point: {x: -208.16737, y: 0, z: -254.5774}
    scale: 1
  - point: {x: -9.803108, y: 0.0000076293945, z: -208.83347}
    scale: 1.8649377
  - point: {x: 66.92026, y: 2.2370806, z: -121.86868}
    scale: 1.2866887
  - point: {x: 138.44064, y: -2.0579863, z: -142.61908}
    scale: 1.4499427
  - point: {x: 111.68901, y: -0.000019073486, z: -252.39874}
    scale: 1.0194505
  - point: {x: 75.24518, y: -0.0000076293945, z: -315.45776}
    scale: 0.94562876
  - point: {x: 73.64804, y: 0.0000076293945, z: -407.171}
    scale: 1.3417987
  - point: {x: 145.75189, y: -0.0000076293945, z: -329.55124}
    scale: 1
  - point: {x: 208.8239, y: -0.0000047683716, z: -281.8103}
    scale: 1
  - point: {x: 285.63055, y: -0.0000019073486, z: -233.41132}
    scale: 1
  - point: {x: 375.57538, y: 0.0000009536743, z: -199.45476}
    scale: 1
  - point: {x: 467.09125, y: 0.0000019073486, z: -138.66284}
    scale: 1
  - point: {x: 486.38483, y: 0.000002861023, z: -44.5744}
    scale: 1
  - point: {x: 448.09222, y: 0.0000038146973, z: 22.492249}
    scale: 1
  - point: {x: 372.10303, y: 0.0000038146973, z: 65.01926}
    scale: 1
  - point: {x: 311.9071, y: 0.0000038146973, z: 124.00836}
    scale: 1
  - point: {x: 342.32416, y: 0.0000038146973, z: 190.35382}
    scale: 1
  - point: {x: 429.2973, y: 0.000012397766, z: 228.64398}
    scale: 1

public enum EventDispatcherType
{
    /// <summary>
    /// 死亡
    /// </summary>
    ActorDeath = 1000,
    /// <summary>
    /// 选中一个Actor
    /// </summary>
    SelectActor,

    /// <summary>
    /// 主角增加技能
    /// </summary>
    LearnSkill,
    /// <summary>
    /// 主角增加研究好的技能
    /// </summary>
    AddAcquiredSkill,
    /// <summary>
    /// 升级主城
    /// </summary>
    UpgradeMainBase,
    ///开始锻造
    StartForge,
    /// <summary>
    /// 建筑建造完成
    /// </summary>
    BuildComplete,
    /// <summary>
    /// 建筑升级
    /// </summary>
    BuildUpgrade,
    /// <summary>
    /// 下一个回合
    /// </summary>
    NextTurn,
    /// <summary>
    /// 所有怪物都生成完成
    /// </summary>
    SpawnComplete,
    /// <summary>
    /// 开始战斗
    /// </summary>
    StartFight,
    /// <summary>
    /// 主城升级了
    /// </summary>
    MainBaseUpgrade,
    /// <summary>
    /// 灵玉收入
    /// </summary>
    JadeIncome,
    /// <summary>
    /// 更新灵玉
    /// </summary>
    UpdateJadeTotal,
    /// <summary>
    /// 更新金币
    /// </summary>
    UpdateGoldTotal,
    /// <summary>
    /// 重建导航网格
    /// </summary>
    RebuildNavMesh,
    ShowHealthBar,

    CreateHero, // 创建英雄

    RoleReviveCountDown,
    CloseRoleReviveCountDown,
    InvadersIsClear, // 怪物清空
    BeastGameLose, // 野兽区域回合失败
    BeastGameWin, // 野兽区域回合胜利

    GameLose,

    GameWin,
    /// <summary>
    /// 建筑摧毁
    /// </summary>
    BuildingDestroy,

    ShowMessage,

    /// <summary>
    /// 更新五行伤害
    /// </summary>
    UpdateFiveElementDamage,
    /// <summary>
    /// 取消显示五行伤害
    /// </summary>
    DisableFiveElementDamage,
    /// <summary>
    /// 购买符文
    /// </summary>
    BuyRune,
    /// <summary>
    /// 卖符文
    /// </summary>
    SellRune,
    /// <summary>
    /// 购买技能
    /// </summary>
    BuySkill,
    /// <summary>
    /// 出卖技能
    /// </summary>
    SellSkill,
    /// <summary>
    /// 在超视角下显示建筑的建造费用
    /// </summary>
    ShowBuildingCostInSuperView,
    CancelShowBuildingCostInSuperView,
    /// <summary>
    /// 正在切换视角
    /// </summary>
    SwitchingView,
    /// <summary>
    /// 野兽区域战斗
    /// </summary>
    BeastDomainStartFight,
    /// <summary>
    /// 野兽区域战斗结束
    /// </summary>
    BeastDomainEndFight,
    //----------------------------------以下是界面消息
    /// <summary>
    /// 打开建造界面
    /// </summary>
    OpenBuildPopUp = 100000,
    /// <summary>
    /// 关闭建造界面
    /// </summary>
    CloseBuildPopUp,
    /// <summary>
    /// 打开灵兽建筑界面
    /// </summary>
    OpenBuildPetHouseUI,
    /// <summary>
    /// 关闭灵兽建筑界面
    /// </summary>
    CloseBuildPetHouseUI,
    /// <summary>
    /// 打开灵兽升级界面
    /// </summary>
    OpenUpgradePetHouseUI,
    /// <summary>
    /// 关闭灵兽升级界面
    /// </summary>
    CloseUpgradePetHouseUI,
    /// <summary>
    /// 打开选项界面
    /// </summary>
    OpenOptionsUI,
    /// <summary>
    /// 关闭选项界面
    /// </summary>
    CloseOptionsUI,
    /// <summary>
    /// 打开共鸣界面
    /// </summary>
    OpenResonanceUI,
    /// <summary>
    /// 关闭共鸣界面
    /// </summary>
    CloseResonanceUI,

    //公共升级界面
    OpenCommonUpgradeUI,
    CloseCommonUpgradeUI,

    //公共升级界面
    OpenCommonBuildUI2,
    CloseCommonBuildUI2,

    //公共建造界面
    OpenCommonBuildUI,
    CloseCommonBuildUI,

    //主城升级界面
    OpenMainBaseUpgradeUI,
    CloseMainBaseUpgradeUI,

    //解锁技能
    OpenUnlockSkillUI, 
    CloseUnlockSkillUI,

    //挖矿界面
    OpenSpiritMineBuildUI,
    CloseSpiritMineBuildUI,

    //灵矿界面
    OpenSpiritMineSelectUI,
    CloseSpiritMineSelectUI,

    //农田界面
    OpenFarmLandSelectUI,
    CloseFarmLandSelectUI,

    ////农田界面
    //OpenFarmLandBriefUI,
    //CloseFarmLandBriefUI,

    //农田界面
    OpenBuildFarmLandUI,
    CloseBuildFarmLandUI,
    //农场界面
    OpenFarmUI,
    CloseFarmUI,

    //研究所界面
    OpenResearchInstituteUI,
    CloseResearchInstituteUI,

    //锻造界面
    OpenSmithyUI,
    CloseSmithyUI,

    /// <summary>
    /// 打开建筑desc页面
    /// </summary>
    OpenBuildingDescUI,
    /// <summary>
    /// 关闭建筑desc页面
    /// </summary>
    CloseBuildingDescUI,
    /// <summary>
    /// 打开UI时执行
    /// </summary>
    OnEnableUIBase,
    /// <summary>
    /// 关闭UI时执行
    /// </summary>
    OnDisableUIBase,
    /// <summary>
    /// 商店状态改变
    /// </summary>
    ShopChange,
    /// <summary>
    /// 最后一回合
    /// </summary>
    LastRound,
    // 商店选择商品
    SelectGoods,
    /// <summary>
    /// 野兽区域界面
    /// </summary>
    OpenBeastDomainUI,
    CloseBeastDomainUI,
    /// <summary>
    /// Msg 确定点击
    /// </summary>
    MsgClose,
    /// <summary>
    /// 编辑建筑
    /// </summary>
    onBuildEdit,
    onBuildEditClose,
}
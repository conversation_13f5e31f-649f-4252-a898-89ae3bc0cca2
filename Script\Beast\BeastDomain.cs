using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Excel.map;
using System.Linq;
using UnityEngine.AI;

public class MonsterPolygonData
{
    public Monster monster;
    public Vector3 startPos;
    public Vector3 endPos;
}
public class BeastDomain : MonoBehaviour
{
    [SerializeField] private int BeastDataId;
    [SerializeField] private beastAreaEntity beastData; // 怪兽区域数据
    [SerializeField] private Vector3[] boundaryPoints; // 边界点
    [SerializeField] private Vector3 centerPoint; // 中心点
    [SerializeField] private Vector3[] insetPolygon; // 内嵌边界点
    [SerializeField] float shrinkFactor = 0.9f; // 边界点缩小到原来的90%

    [Header("怪物生成设置")]
    [SerializeField] private float moveSpeed = 3f; // 移动速度
    
    [Header("边界接近设置")]
    [SerializeField] private float nearDistanceA = 5f; // 边界接近距离A
    [SerializeField] private float nearDistanceB = 2f; // 边界接近距离B
    // 修改颜色定义
    [SerializeField] private Color normalBoundaryColor = new Color(1, 0, 0, 0.5f); // 初始半透明红
    [SerializeField] private Color nearColorA = Color.red; // 目标不透明红
    [SerializeField] private Color nearColorB = Color.white; // 边界接近颜色B
    [SerializeField] private Transform AnchorPoint; // 中心锚点
    // [SerializeField] private float patrolPauseDuration = 2.0f; // 在每个点停留的时间
    public Vector3 CenterPoint => centerPoint;
    public beastAreaEntity BeastData => beastData;
    List<Actor> patrolMonsters = new List<Actor>(); // 创建的怪物列表
    private LineRenderer boundaryLineRenderer; // 边界线渲染器

    public BeastDomainUI selfUI;
    private Bounds areaBounds; // 预计算的包围盒
    private float timeSinceLastCheck; // 时间累积器
    private float checkInterval = 2f; // 检测间隔（秒）

    private float timeCharacterLastCheck; // 角色位置检测时间累计
    private float checkCharacterInterval = 1f; // 角色位置检测间隔（秒）
    private Camera mainCamera; // 主相机
    public GameObject titleUI; // 标题UI
    public BeastDomainSpawn spawnScript; // 出怪脚本

    private List<MonsterPolygonData> cachedDestinations = new List<MonsterPolygonData>();
    public bool FightState {
        get => spawnScript.StartFight;
    }
    // 标题锚点坐标
    public Vector3 AnchorPointPosition {
        get {
            if(AnchorPoint != null) {
                return AnchorPoint.position;
            }
            return Vector3.zero;
        }
    }
    // 新增材质引用字段
    private Material boundaryMaterial;
    public bool CharacterInDomain = false;
    public bool CharacterInState {
        get
        {
            return CharacterInDomain;
        }
        set
        {
            if(CharacterInDomain != value) {
                CharacterInDomain = value;
            }
        }
    }

    private bool isDefeat = false;
    public bool IsDefeat {
        get => isDefeat;
    }

    private bool isVisible = false;
    private bool isWin = false;
    private void Awake()
    {
        mainCamera = Camera.main;
        
        // 读取资源
        List<beastAreaEntity> _list = Resources.Load<mapExcel>("Excel/mapExcel").beastAreaList;
        beastData = _list.Find(x => x.id == BeastDataId);

        spawnScript = gameObject.GetComponent<BeastDomainSpawn>();
        if(spawnScript != null) {
            spawnScript.indexScript = this;

        }
        if (boundaryPoints != null && boundaryPoints.Length >= 3)
        {
            // 预计算包围盒
            areaBounds = new Bounds(boundaryPoints[0], Vector3.zero);
            foreach (var point in boundaryPoints) 
                areaBounds.Encapsulate(point);
        }

        if((insetPolygon == null || insetPolygon.Length == 0) && boundaryPoints.Length > 0) {
            ShrinkPolygon();
        }

        EventDispatcher.AddEventListener(EventDispatcherType.NextTurn, unDefeat);
        EventDispatcher.AddEventListener(EventDispatcherType.MsgClose, roleStartMove);
    }

    void unDefeat() {
        isDefeat = false;
    }

    void roleStopMove() {
        Role.StopMove = true;
    }
    void roleStartMove() {
        Role.StopMove = false;
        if(isWin) {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        // 创建巡逻怪兽
        CreatePatrolMonsters();
    }
    /// <summary>
    /// 胜利
    /// </summary>
    public void GameWin() {
        isDefeat = false; //初始化
        isWin = true;
        selfUI.winGame();
        if(titleUI != null) {
            Destroy(titleUI);
        }
        Role.Instance.Renew();
        EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, "挑战成功");
        EventDispatcher.TriggerEvent(EventDispatcherType.BeastGameWin, this);
        roleStopMove();
    }
    /// <summary>
    /// 战败
    /// </summary>
    public void defeatBeastDomain() {
        spawnScript.defeatAction();
        EnableMonsters();
        isDefeat = true;
        Role.Instance.Renew();
        // 显示失败界面
        EventDispatcher.TriggerEvent(EventDispatcherType.ShowMessage, "挑战失败");
        roleStopMove();
    }
    /// <summary>
    /// 初始化边界线渲染器
    /// </summary>
    private void InitializeBoundaryLine()
    {
        GameObject lineObj = new GameObject("BoundaryLine");
        lineObj.transform.SetParent(transform);
        boundaryLineRenderer = lineObj.AddComponent<LineRenderer>();
        
        // 1. 创建虚线材质
        boundaryMaterial = new Material(Shader.Find("Unlit/Transparent"))
        { 
            renderQueue = 3000,
            mainTexture = Resources.Load<Texture2D>("Art/BeastDomain/BeastDomainBorderAi"),
            color = new Color(1, 0, 0, 0.5f)
        };
        boundaryLineRenderer.sharedMaterial = boundaryMaterial;
        // 2. 设置线渲染器参数
        boundaryLineRenderer.textureMode = LineTextureMode.Tile;
        boundaryLineRenderer.startWidth = 1f;
        boundaryLineRenderer.endWidth = 1f;
        boundaryLineRenderer.loop = true;
        boundaryLineRenderer.textureScale = new Vector2(0.01f, 0.2f);
        
        // 3. 设置初始虚线参数
        boundaryMaterial.SetTextureScale("_MainTex", new Vector2(10, 1)); // 控制虚线密度
        UpdateBoundaryLine();
    }
    /// <summary>
    /// 更新边界线渲染器
    /// </summary>
    private void UpdateBoundaryLine()
    {
        if (boundaryLineRenderer == null) return;

        boundaryLineRenderer.positionCount = boundaryPoints.Length;
        for (int i = 0; i < boundaryPoints.Length; i++)
        {
            boundaryLineRenderer.SetPosition(i, boundaryPoints[i]);
        }
    }
    /// <summary>
    /// 设置边界点
    /// </summary>
    /// <param name="points"></param>
    public void SetBoundaryPoints(Vector3[] points)
    {
        boundaryPoints = points;
        ShrinkPolygon(); // 缩小多边形
        InitializeBoundaryLine();
    }
    /// <summary>
    /// 设置区域数据
    /// </summary>
    /// <param name="data"></param>
    public void setBaseData(int id) {
        BeastDataId = id;
    }
    /// <summary>
    /// 设置中心点
    /// </summary>
    /// <param name="center"></param>
    public void SetCenter(Vector3 center)
    {
        centerPoint = center;
    }
    
    /// <summary>
    /// 区域出现在屏幕内
    /// </summary>
    private void CheckDomainVisibility()
    {
        if (mainCamera == null || boundaryPoints == null) return;
        bool isVisibility = CalculateDomainVisibility(); // 在屏幕内
        if(isVisibility == isVisible) return;
        isVisible = isVisibility;
        EventDispatcherType EventType = isVisibility ? EventDispatcherType.OpenBeastDomainUI : EventDispatcherType.CloseBeastDomainUI;
        EventDispatcher.TriggerEvent(EventType, this);
    }

    /// <summary>
    /// 检查角色是否在区域内
    /// </summary>
    private void CheckForCharacters()
    {
        GameObject character = Role.Instance?.gameObject;
        if (character == null) return;

        Vector3 pos = character.transform.position;
        
        // 快速排除：先检查是否在包围盒内
        if (!areaBounds.Contains(pos))
        {
            CharacterInState = false;
            return;
        }

        // 检测距离
        // float distance = CalculateDistanceToBoundary(pos);
        // 根据距离展示边缘线条
        // UpdateBoundaryVisual(distance);
        
        // 精确检测
        CharacterInState = IsPointInPolygon(pos);
    }

    /// <summary>
    /// 计算区域可见性（优化版：包围盒 + 关键点检测）
    /// </summary>
    private bool CalculateDomainVisibility()
    {
        // 方法1：快速检测 - 通过包围盒与摄像机视锥体
        if (GeometryUtility.TestPlanesAABB(GeometryUtility.CalculateFrustumPlanes(mainCamera), areaBounds))
        {
            return true;
        }

        // 方法2：精确检测 - 检查边界点是否在屏幕内
        foreach (var point in boundaryPoints)
        {
            Vector3 screenPoint = mainCamera.WorldToViewportPoint(point);
            if (screenPoint.x >= 0 && screenPoint.x <= 1 && 
                screenPoint.y >= 0 && screenPoint.y <= 1 && 
                screenPoint.z > 0) // z>0 表示在摄像机前方
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 更新边界线颜色和宽度
    /// </summary>
    /// <param name="distance"></param>
    // 在UpdateBoundaryVisual方法中添加透明度控制
    private void UpdateBoundaryVisual(float distance)
    {
        float progress = Mathf.Clamp01(1 - distance / nearDistanceA); // 根据距离计算渐变进度
        Color currentColor = Color.Lerp(normalBoundaryColor, nearColorA, progress);
        
        // 同时控制颜色和透明度
        currentColor.a = Mathf.Lerp(0.5f, 1f, progress); // 透明度从50%渐变到100%
        SetLineColor(currentColor);

        // 控制虚线密度（根据需求选择是否调整）
        float tileScale = Mathf.Lerp(10f, 0f, progress); // 贴图平铺比例从10到0
        boundaryLineRenderer.material.SetTextureScale("_MainTex", new Vector2(tileScale, 1));
    }
    // private void UpdateBoundaryVisual(float distance)
    // { 
    //     if (distance <= nearDistanceB)
    //     {
    //         SetLineColor(nearColorB);
    //     }
    //     else if (distance <= nearDistanceA)
    //     {
    //         float t = Mathf.InverseLerp(nearDistanceA, nearDistanceB, distance);
    //         SetLineColor(Color.Lerp(nearColorA, nearColorB, t));
    //     }
    //     else
    //     {
    //         SetLineColor(normalBoundaryColor);
    //     }
    // }
    /// <summary>
    /// 设置边界线颜色
    /// </summary>
    /// <param name="color"></param>
    private void SetLineColor(Color color)
    {
        boundaryLineRenderer.startColor = color;
        boundaryLineRenderer.endColor = color;
    }
    /// <summary>
    /// 计算点到边界的距离
    /// </summary>
    /// <param name="point"></param>
    /// <returns></returns>
    public float CalculateDistanceToBoundary(Vector3 point)
    {
        float minDistance = float.MaxValue;
        for (int i = 0; i < boundaryPoints.Length; i++)
        {
            Vector3 A = boundaryPoints[i];
            int nextIndex = i+1;
            Vector3 B = boundaryPoints[nextIndex >= boundaryPoints.Length ? 0 : nextIndex];
            minDistance = Mathf.Min(minDistance, DistanceToSegment(A, B, point));
        }
        return minDistance;
    }
    /// <summary>
    /// 计算点到线段的距离
    /// </summary>
    /// <param name="A">起点</param>
    /// <param name="B">终点</param>
    /// <param name="C">目标点</param>
    /// <returns>C点到AB线段的垂直距离</returns>
    private float DistanceToSegment(Vector3 A, Vector3 B, Vector3 C) {
        // 步骤1：计算向量AB和AC
        Vector3 AB = B - A;    // 线段的向量
        Vector3 AC = C - A;   // 起点到目标点的向量

        // 步骤2：计算投影参数t（点C在线段AB上的归一化投影）
        float dotProduct = Vector3.Dot(AC, AB);  // 点乘计算投影长度
        float lengthSqAB = AB.sqrMagnitude;      // 线段长度的平方（避免开根号提高性能）

        // 处理线段AB长度为0的特殊情况（A和B重合）
        if (lengthSqAB < Mathf.Epsilon) {
            return Vector3.Distance(A, C);      // 直接返回A到C的距离
        }

        // 计算投影参数t（t=0时在A点，t=1时在B点）
        float t = Mathf.Clamp01(dotProduct / lengthSqAB); // 使用Clamp01代替手动判断边界

        // 步骤3：根据投影参数t计算最近点
        Vector3 nearestPoint = A + t * AB;  // 通过插值计算线段上的最近点

        // 最终返回点C到最近点的距离
        return Vector3.Distance(nearestPoint, C);
    }
    /// <summary>
    /// 检查点是否在多边形内
    /// </summary>
    /// <param name="point">坐标</param>
    /// <returns>是否在区域内</returns>
    public bool IsPointInPolygon(Vector3 point)
    {
        // 使用射线投射算法检查点是否在多边形内
        bool isInside = false;
        for (int i = 0, j = boundaryPoints.Length - 1; i < boundaryPoints.Length; j = i++)
        {
            Vector3 vertI = boundaryPoints[i];
            Vector3 vertJ = boundaryPoints[j];
            
            if (((vertI.z > point.z) != (vertJ.z > point.z)) &&
                (point.x < (vertJ.x - vertI.x) * (point.z - vertI.z) / (vertJ.z - vertI.z) + vertI.x))
            {
                isInside = !isInside;
            }
        }
        
        return isInside;
    }
    
    /// <summary>
    /// 添加怪物生成方法
    /// </summary>
    public void CreatePatrolMonsters()
    {
        if (beastData == null) return;

        var monsterConfigs = beastData.patrolMonsterShow.Split('|')
            .Select(config => config.Split(':'))
            .Where(parts => parts.Length == 2)
            .Select(parts => new 
            {
                id = parts[0],
                count = int.TryParse(parts[1], out int num) ? num : 0
            });
        foreach (var config in monsterConfigs)
        {
            for (int i = 0; i < config.count; i++)
            {
                List<Vector3> polygonPoints = new List<Vector3>();
                Vector3 spawnPos = GetRandomPointInPolygonAndNavMesh(); // 出生点
                polygonPoints.Add(spawnPos);
                for (int j = 0; j < 3; j++)
                {
                    Vector3 randomPoint = GetRandomPointInPolygonAndNavMesh();
                    polygonPoints.Add(randomPoint);
                }
                Monster monster = GameMain.Instance.CreateMonster(int.Parse(config.id), CampEnum.Neutral, spawnPos);

                if (monster != null)
                {
                    monster.StateMachine.patrolPoints = polygonPoints;
                    monster.StateMachine.ChangeState(monster.m_polygonState);
                    patrolMonsters.Add(monster);
                }
            }
        }
    }
    
    /// <summary>
    /// 获取随机点 Gemini create
    /// </summary>
    Vector3 GetRandomPointInPolygonAndNavMesh()
    {
        // 前置校验（新增）
        if (insetPolygon == null || insetPolygon.Length < 3)
        {
            Debug.LogError("内嵌多边形数据异常，请检查boundaryPoints设置");
            return centerPoint; // 退回中心点
        }
        // 计算多边形包围盒
        float minX = float.MaxValue, minZ = float.MaxValue;
        float maxX = float.MinValue, maxZ = float.MinValue;
        foreach (Vector3 point in insetPolygon)
        {
            minX = Mathf.Min(minX, point.x);
            minZ = Mathf.Min(minZ, point.z);
            maxX = Mathf.Max(maxX, point.x);
            maxZ = Mathf.Max(maxZ, point.z);
        }

        // 随机生成点，直到找到在多边形内且在 NavMesh 上的点
        Vector3 randomPoint;
        NavMeshHit hit; // 用于存储 NavMesh.SamplePosition 的结果
        int safetyCounter = 0;
        
        // 定义 NavMesh.SamplePosition 的搜索距离和区域掩码
        // 2.0f 表示在 randomPoint 的 2.0f 范围内搜索最近的 NavMesh 点
        // NavMesh.AllAreas 表示搜索所有可寻路区域，可以根据需要修改
        float searchDistance = 1f;
        int areaMask = NavMesh.AllAreas; 
        int maxSafetyCounter = 1000;

        while (safetyCounter < maxSafetyCounter) // 使用while循环，条件为安全计数器
        {
            randomPoint = new Vector3(
                Random.Range(minX, maxX),
                insetPolygon[0].y, // 使用y轴高度与边界点相同
                Random.Range(minZ, maxZ)
            );

            safetyCounter++;
            bool isPointIn = IsPointInPolygon(randomPoint);
            //bool isNavMesh = NavMesh.SamplePosition(randomPoint, out hit, searchDistance, areaMask);
            //bool isObstacle = spawnScript.IsPointInsideAnyObstacle(randomPoint);
            //if( isPointIn && isNavMesh && isObstacle) {
            //    return randomPoint;
            //}
            if(!isPointIn) continue;
            if(GridPathfinding.HasObstaclesInRadius(randomPoint, 1f)) continue;

            return randomPoint;
        }

        // 如果循环结束（达到最大尝试次数）仍未找到有效点
        Debug.LogWarning("无法在多边形内且 NavMesh 上找到随机点，已达到最大尝试次数。");

        // 失败回退：尝试在多边形顶点附近采样 NavMesh
        int _maxIndex = insetPolygon.Length - 1;
        int _randomIndex = Random.Range(0, _maxIndex);
        Vector3 fallbackVertex = insetPolygon[_randomIndex];

        if (NavMesh.SamplePosition(fallbackVertex, out hit, searchDistance * 2, areaMask)) // 失败回退时可以使用稍大的搜索距离
        {
            Debug.LogWarning("回退：返回多边形顶点附近的一个 NavMesh 点。");
            return hit.position; 
        }
        else
        {
            Debug.LogWarning("回退：无法在多边形顶点附近找到 NavMesh 点，返回随机顶点（可能不在 NavMesh 上）。");
            return fallbackVertex; // 最终回退，返回随机顶点，可能不在 NavMesh 上
        }
    }

    /// <summary>
    /// 缩小多边形
    /// </summary>
    public void ShrinkPolygon()
    {
        // 创建新的顶点数组用于存储缩小后的多边形顶点
        Vector3[] shrunkPoints = new Vector3[boundaryPoints.Length];
        
        // 对每个顶点进行处理
        for (int i = 0; i < boundaryPoints.Length; i++)
        {
            // 计算从质心到顶点的向量
            Vector3 direction = boundaryPoints[i] - centerPoint;
            
            // 按照缩小因子缩小该向量
            // shrinkFactor应该是0-1之间的值，1表示不缩小，0表示缩小到中心点
            direction *= shrinkFactor;
            
            // 计算新的顶点位置
            shrunkPoints[i] = centerPoint + direction;
        }
        
        insetPolygon = shrunkPoints;
    }

    // 添加销毁时清理
    private void OnDestroy()
    {
        // 清理动态创建的材质
        if (boundaryMaterial != null)
        {
            if (Application.isPlaying)
            {
                Destroy(boundaryMaterial);
            }
            else
            {
                DestroyImmediate(boundaryMaterial);
            }
        }
        if (patrolMonsters != null && patrolMonsters.Count > 0) 
        {            
            for(int i = patrolMonsters.Count - 1; i >= 0; i--)            
            {
                Actor monster = patrolMonsters[i];
                if (monster != null) Destroy(monster);
                patrolMonsters.Remove(monster);
            }
        }
        EventDispatcher.RemoveEventListener(EventDispatcherType.NextTurn, unDefeat);
        EventDispatcher.RemoveEventListener(EventDispatcherType.MsgClose, roleStartMove);
    }
    
    public void StartFiting() {
        DesableMonsters();
        spawnScript.BuildingToInvader();
        spawnScript.StartFight = true;
        GameMain.Instance.isBeastRound = true;
    }

    public void DesableMonsters()
    {
        foreach (var monster in patrolMonsters)
        {
            monster.gameObject.SetActive(false);
        }
    }
    public void EnableMonsters()
    {
        foreach (var monster in patrolMonsters)
        {
            monster.gameObject.SetActive(true);
        }
    }
    void OnTriggerEnter(Collider other)
    {
        if(other.transform == Role.Instance.transform) 
        {
            selfUI.toggle(this);
        }
    }
    void OnTriggerExit(Collider other)
    {
        if(other.transform == Role.Instance.transform) 
        {
            selfUI.toggle(this);
        }
    }
    private void Update()
    {
        timeCharacterLastCheck +=  Time.deltaTime;
        timeSinceLastCheck += Time.deltaTime;
        // 累积时间，按间隔检测
        if(checkCharacterInterval >= timeCharacterLastCheck) 
        {
            // 角色检测
            CheckForCharacters();
            timeCharacterLastCheck = 0f;
        }

        // 累积时间，按间隔检测
        if (timeSinceLastCheck >= checkInterval)
        {
            CheckDomainVisibility();
            timeSinceLastCheck = 0f;
        }
        if(titleUI != null) 
        {
            Vector3 pos = Camera.main.WorldToScreenPoint(AnchorPointPosition);
            float posY = pos.y + 50;
            titleUI.transform.position = new Vector3(pos.x, posY, pos.z);
        }
    }
}
using UnityEngine;
using System.Collections.Generic;
using Excel.rune;
using Excel.shop;
using System.Linq;
using System;
using Excel.lable;

[Serializable]
public class RuneGood: BaseGoodsData
{
    public runeBaseEntity dataEntity;
}

public class RuneCard : BaseGoodsItem
{
    private List<gecang_runeEntity> weightData;
    // 源数据
    private List<runeBaseEntity> SourceData;
    private void Awake() 
    {
        baseGoodsData = new RuneGood();
        // 加载Excel数据
        weightData = ExcelData.Instance.ShopExcel?.gecang_runeList;
        runeExcel excelData = Resources.Load<runeExcel>("Excel/runeExcel");
        SourceData = excelData?.runeBaseList;
    }
    // 获取权重列表
    private List<int> getListWeight(List<gecang_runeEntity> val) 
    {
        List<int> list = new List<int>();
        if(val == null || val.Count <= 0) return list;
        for (int i = 0; i < val.Count; i++)
        {
            list.Add(val[i].weight);
        }
        return list;
    }
    // 查询是否存在前置
    private bool isExistFront(int id) {
        List<BaseGoodsData> packageData = RuneInventory.Instance.buyList;
        if(packageData == null || packageData.Count <= 0) 
        {
            return false;
        }
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                return true;
            }
        }
        return false;
    }
    // 查询购买次数
    private int getBuyNum(int id) 
    {
        List<BaseGoodsData> packageData = RuneInventory.Instance.buyList;
        if(packageData == null || packageData.Count <= 0) 
        {
            return 0;
        }
        int result = 0;
        for (int i = 0; i < packageData.Count; i++)
        {
            if(packageData[i].id == id)
            {
                result++;
            }
        }
        return result;
    }
    // 判断回合限制
    private bool filterRoundLimit(string target) {
        if(target == "" || target == "0") return true;
        int[] intArray = target.Split('|').Select(int.Parse).ToArray();
        if(intArray.Length <= 0) return true;
        return !intArray.Contains(GameMain.Instance.CurrentTurn);
    }
    // 筛选已经被加载的
    private bool filterIsLoad(gecang_runeEntity val) 
    {
        bool result = false;
        if(parentObject == null) return result;
        RuneCardList script = parentObject.GetComponent<RuneCardList>();
        if(script == null) return result;
        List<BaseGoodsItem> preRunes = script.prelloadRune; // 上回合的数据
        List<BaseGoodsItem> nowRunes = script.loadRune; // 本回合数据

        bool preHasData = preRunes != null && preRunes.Count > 0 && preRunes.Any(r => r.baseGoodsData.id == val.id);
        bool nowHasData = nowRunes != null && nowRunes.Count > 0 && nowRunes.Any(r => r.baseGoodsData.id == val.id);
        result = preHasData || nowHasData;
        return result;
    }
    // 筛选
    private List<gecang_runeEntity> filterData() 
    {
        List<gecang_runeEntity> result = new List<gecang_runeEntity>();
        for (int i = 0; i < weightData.Count; i++)
        {
            // 前置条件筛选
            bool hasExistFront = weightData[i].refFront < 0 || (weightData[i].refFront > 0 && isExistFront(weightData[i].refFront));
            // 购买次数限制筛选
            bool isMaxBuy = weightData[i].buyNumMax > 0 && getBuyNum(weightData[i].id) >= weightData[i].buyNumMax;
            // 回合限制
            bool isRoundLimit = filterRoundLimit(weightData[i].roundLimit);
            // 是否已加载
            bool isload = filterIsLoad(weightData[i]);

            if(hasExistFront && !isMaxBuy && isRoundLimit && !isload) 
            {
                result.Add(weightData[i]);
            }
        }
        return result;
    }
    private void setInfomation() 
    {
        RuneGood goods = baseGoodsData as RuneGood;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(itemsDetail == null || dataEntity == null) return;
        // 定义好符文的品质数据做转换
        itemsDetail.goodType = (int)GoodsType.Rune;
        itemsDetail.Title = Language.GetText(dataEntity.name);
        List<MutationsLabelData> labelsList = new List<MutationsLabelData>();
        string [] labelsSplit = dataEntity.label.Split('|');
        for(int i = 0; i < labelsSplit.Length; i++)
        {
            int labelId = int.Parse(labelsSplit[i]);
            lableEntity label = ExcelData.Instance.getLableEntity(labelId);
            MutationsLabelData mutationsLabelData = new MutationsLabelData();
            mutationsLabelData.color = (BorderColor)label.quality;
            mutationsLabelData.text = Language.GetText(label.textId);
            labelsList.Add(mutationsLabelData);
        }
        itemsDetail.labels = labelsList;
        itemsDetail.Content = Language.GetText(dataEntity.desc);
        itemsDetail.render();
    }
    // 创建符文实例
    private void renderUI(int id) 
    {
        if(SourceData == null || SourceData.Count <= 0) return;
        runeBaseEntity target = SourceData.Find(x=> x.id == id);
        if(target == null) return;
        initGoods(target);
        setFrontImg(target.icon);
        baseBorderItem.CurrentBorder = target.quality; // 边框颜色
        setPrice(target.price);
        // 详情
        setInfomation();
    }
    private void initGoods(runeBaseEntity target)
    {
        RuneGood goods = new RuneGood();
        goods.dataEntity = target;
        goods.id = target.id;
        goods.price = target.price;
        goods.sellDiscount = shopConfig.sellDiscount;
        goods.sellPrice = (int)Math.Round(target.price * shopConfig.sellDiscount, MidpointRounding.AwayFromZero);
        goods.icon = target.icon;
        goods.background = backImageUrl;
        goods.goodsType = (int)GoodsType.Rune;
        baseGoodsData = goods;
    }
    /// <summary>
    /// 权重加载符文
    /// </summary>
    public override void LoadItem() 
    {
        if(SourceData == null || SourceData.Count <= 0) return;
        List<gecang_runeEntity> filter = filterData(); // 前置筛选
        List<int> itemTypeList = getListWeight(filter); // 获取权重列表
        int index = WeightCalculator.GetWeightedRandomIndex(itemTypeList); // 根据权重获取随机索引
        int targetId = filter[index].id; // 根据索引获取对应的数据
        renderUI(targetId);
    }

    /// <summary>
    /// 根据id加载符文
    /// </summary>
    /// <param name="id"></param>
    public override void LoadItem(int id) 
    {
        renderUI(id);
    }
    // 购买
    public override void BuyItem() 
    {
        RuneGood goods = baseGoodsData as RuneGood;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || RuneInventory.Instance == null) 
        {
            return;
        }
        // 尝试购买行为
        bool payRes = GameMain.Instance.payJade(dataEntity.price);
        if(!payRes) return;
        EventDispatcher.TriggerEvent<int>(EventDispatcherType.BuyRune, dataEntity.id);
        // 添加到列表
        if(RuneInventory.Instance.AddItem(this))
        {
            base.BuyItem();
        }
    }
    /// <summary>
    ///  选择
    /// </summary>
    public override void ChooseItem() 
    {
        RuneGood goods = baseGoodsData as RuneGood;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || RuneInventory.Instance == null) {
            return;
        }
        // 添加前校验
        if(RuneInventory.Instance.AddItem(this))
        {
            base.ChooseItem();
        }
    }
}
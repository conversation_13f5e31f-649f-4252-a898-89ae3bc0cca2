﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Excel.monster;
using Excel.skill;
using UnityEngine.AI;
using Unity.Mathematics;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using UnityEngine.Rendering;
using Unity.VisualScripting;

public class SkillEffect : SkillEffectBase
{
    //造成伤害、恢复生命、添加buff、删除buff、突进、跳跃、击退、吸引、击飞、改变目标属性、复活、自爆、分身
    public enum EffectTypeEnum
    {
        Damage      = 1,
        Recover     = 2,//恢复生命
        AddBuff     = 3,//添加buff
        RemoveBuff  = 4,//删除buff
        Rush        = 5,//
        Jump,
        BeatBack,
        Pull, //吸引
        BeatFly,
        ChangeAttribute = 10,
        Revive          = 11, //复活
        SelfBurst       = 12, //自爆
        Clone           = 13, //分身
        Summon,
        LightningChain,
        ReduceCooldownTime  = 18,
        Teleport            = 19,  //传送
        RangeClear          = 21 //范围清除要素
    }

    private SkillEffectShow m_skillEffectShow;
    public SkillEffectShow skillEffectShow { get => m_skillEffectShow; set => m_skillEffectShow = value; }

    private EffectTypeEnum m_effectType;
    public EffectTypeEnum EffectType { get => m_effectType; set => m_effectType = value; }

    public string EffectParam1;
    public string EffectParam2;
    public string EffectParam3;
    public string EffectParam4;
    public string EffectParam5;
    public string EffectParam6;
    public string EffectParam7;
    public string EffectParam8;
    public string EffectParam9;

    public int showId;

    public SkillEffect(Skill skill, int effectID, GameEntity bindActor)
    {
        m_bindSkill = skill;
        m_bindActor = bindActor;
        m_releaser = bindActor;

        effectEntity entity = ExcelData.Instance.GetSkillEffect(effectID);
        if (entity == null)
        {
            Debug.LogError("技能效果不存在");
            throw new System.ArgumentException($"技能效果不存在，effectID: {effectID}");
        }
        m_id = effectID;
        m_delay = entity.delay/1000f;
        m_targetDelay = entity.targetDelay;
        m_skillRange = new SkillRange(entity.rangeID);
        m_effectType = (EffectTypeEnum)entity.effectType;

        if (entity.targetId > 0)
            m_skillTarget = new SkillTarget(entity.targetId);

        if (entity.showId > 0 && ExcelData.Instance.GetEffectShow(entity.showId) != null)
            m_skillEffectShow = new SkillEffectShow(entity.showId);
        showId = entity.showId;
        if (!string.IsNullOrEmpty(entity.EffectParam1))
            EffectParam1 = entity.EffectParam1;
        if (!string.IsNullOrEmpty(entity.EffectParam2))
            EffectParam2 = entity.EffectParam2;
        if (!string.IsNullOrEmpty(entity.EffectParam3))
            EffectParam3 = entity.EffectParam3;
        if (!string.IsNullOrEmpty(entity.EffectParam4))
            EffectParam4 = entity.EffectParam4;
        if (!string.IsNullOrEmpty(entity.EffectParam5))
            EffectParam5 = entity.EffectParam5;
        if (!string.IsNullOrEmpty(entity.EffectParam6))
            EffectParam6 = entity.EffectParam6;
        if (!string.IsNullOrEmpty(entity.EffectParam7))
            EffectParam7 = entity.EffectParam7;
        if (!string.IsNullOrEmpty(entity.EffectParam8))
            EffectParam8 = entity.EffectParam8;
        if (!string.IsNullOrEmpty(entity.EffectParam9))
            EffectParam9 = entity.EffectParam9;
    }

    public void ApplyEffectShow(GameEntity actor,  SkillEffectShow effectShow)
    {
        if (effectShow == null || string.IsNullOrEmpty(effectShow.attackEffect)) return;

        GameObject goEffectPrefab = Resources.Load(effectShow.attackEffect) as GameObject;
        if (goEffectPrefab != null)
        {
            GameObject effect = GameObject.Instantiate(goEffectPrefab) as GameObject;
            Transform anchor = actor.GetAnchor(effectShow.attackAnchor);
            if (effectShow.attackMove)
            {
                effect.transform.parent = anchor;
                //effect.transform.forward = actor.transform.forward;
                effect.transform.localPosition = Vector3.zero;
            }
            else
                effect.transform.position = anchor.position;
            effect.transform.localRotation = Quaternion.identity;

            if (effectShow.attackDuration > 0)
            {
                TimeLimit tl = effect.GetComponent<TimeLimit>();
                if(tl ==  null)
                    tl = effect.AddComponent<TimeLimit>();

                tl.StartTime(effectShow.attackDuration/1000.0f);
            }
            else
            {
                TimeLimit tl = effect.GetComponent<TimeLimit>();
                if (tl == null)
                    tl = effect.AddComponent<TimeLimit>();

                tl.StartTime(1);
            }
        }
            
    }

    public void ApplyHitEffectShow(GameEntity actor, SkillEffectShow effectShow)
    {
        if (effectShow == null || string.IsNullOrEmpty(effectShow.hitEffect)) return;

        GameObject goEffectPrefab = Resources.Load(effectShow.hitEffect) as GameObject;
        if (goEffectPrefab != null)
        {
            GameObject effect = GameObject.Instantiate(goEffectPrefab) as GameObject;
            Transform anchor = actor.GetAnchor(effectShow.hitAnchor);
            if (effectShow.hitMove)
            {
                effect.transform.parent = anchor;
                //effect.transform.forward = actor.transform.forward;
                effect.transform.localPosition = Vector3.zero;
            }
            else
                effect.transform.position = anchor.position;


            if (effectShow.hitDuration > 0)
            {
                TimeLimit tl = effect.GetComponent<TimeLimit>();
                if (tl == null)
                    tl = effect.AddComponent<TimeLimit>();

                tl.StartTime(effectShow.hitDuration / 1000.0f);
            }
            else
            {
                TimeLimit tl = effect.GetComponent<TimeLimit>();
                if (tl == null)
                    tl = effect.AddComponent<TimeLimit>();

                tl.StartTime(1);
            }
        }

    }
    public override void ApplyEffect()
    {
        if (m_actorsTargetList == null || m_actorsTargetList.Count == 0 ) return;
        if (m_skillEffectShow != null)
        {
            if (!string.IsNullOrEmpty(m_skillEffectShow.attackEffect))
            {
                if (m_skillEffectShow.setOut == 1)
                {
                    ApplyEffectShow(m_bindActor, m_skillEffectShow);
                }
                else
                {
                    for (int i = 0; i < m_actorsTargetList.Count; i++)
                        ApplyEffectShow(m_actorsTargetList[i], m_skillEffectShow);
                }
            }
            if(!string.IsNullOrEmpty(m_skillEffectShow.hitEffect))
            {
                for (int i = 0; i < m_actorsTargetList.Count; i++)
                    ApplyHitEffectShow(m_actorsTargetList[i], m_skillEffectShow);
            }
        }


        if (m_effectType == EffectTypeEnum.Damage)
        {
            ////攻击对方增强自己
            Buff buff = m_bindActor.buffSystem.getAttackEnemiesToPowerUp();
            if (buff != null)
            {
                if (buff.lastTarget == null)
                {
                    buff.lastTarget = m_actorsTargetList[0];
                    buff.ApplyAttackEnemiesToPowerUp();
                }
                else if (!m_actorsTargetList.Exists(element => element == buff.lastTarget))
                    buff.BuffData.duration = 0; //不是同一个目标，结束buff
                else
                {
                    buff.Stack();
                    //buff.ApplyAttackEnemiesToPowerUp();
                }

            }

            for (int i = 0; i < m_actorsTargetList.Count; i++)
                DamageEffect(m_actorsTargetList[i]);
        }
        else if (m_effectType == EffectTypeEnum.Recover)
        {
            for (int i = 0; i < m_actorsTargetList.Count; i++)
                HealthRecover(m_bindActor, m_actorsTargetList[i]);
        }
        else if (m_effectType == EffectTypeEnum.AddBuff)
        {
            //m_bindActor.AddBuff(int.Parse(EffectParam1), m_bindActor, null);
            for (int i = 0; i < m_actorsTargetList.Count; i++)
                m_actorsTargetList[i].AddBuff(int.Parse(EffectParam1), m_bindSkill);
        }
        else if (m_effectType == EffectTypeEnum.RemoveBuff)
        {
            for (int i = 0; i < m_actorsTargetList.Count; i++)
                m_actorsTargetList[i].RemoveBuff(int.Parse(EffectParam1), int.Parse(EffectParam2));
        }
        else if (m_effectType == EffectTypeEnum.Rush)
        {
            m_bindActor.DoRush(m_actorsTargetList[0], m_bindSkill, float.Parse(EffectParam1), float.Parse(EffectParam2), EffectParam3);
        }
        else if (m_effectType == EffectTypeEnum.Jump)
        {
            GameEntity actorTarget;
            if (int.Parse(EffectParam1) == 1)
                actorTarget = GameMain.Instance.GetClosestEnemy(m_bindActor, 20);
            else
                actorTarget = GameMain.Instance.GetClosestFriend(m_bindActor, 20);
            m_bindActor.JumpTo(actorTarget, float.Parse(EffectParam2), float.Parse(EffectParam3));
        }
        else if (m_effectType == EffectTypeEnum.BeatBack)
        {
            for (int i = 0; i < m_actorsTargetList.Count; i++)
            {
                BeatBack(m_bindActor, m_actorsTargetList[i]);
            }
        }
        else if (m_effectType == EffectTypeEnum.Pull)
        {
            Vector3 targetPosition = m_bindActor.transform.position;
            if (int.Parse(EffectParam3) == 1)
                targetPosition = m_bindActor.transform.position;
            else if (int.Parse(EffectParam3) == 2)
                targetPosition = GameMain.Instance.GetClosestEnemy(m_bindActor, 20).transform.position;
            for (int i = 0; i < m_actorsTargetList.Count; i++)
                m_bindActor.Pull(m_actorsTargetList[i], targetPosition, float.Parse(EffectParam1), (int.Parse(EffectParam2)) == 1);
        }
        else if (m_effectType == EffectTypeEnum.BeatFly)
        {
            for (int i = 0; i < m_actorsTargetList.Count; i++)
                m_bindActor.BeatFly(m_actorsTargetList[i], float.Parse(EffectParam1), float.Parse(EffectParam2), float.Parse(EffectParam3), float.Parse(EffectParam4), (int.Parse(EffectParam5)) == 1);
        }
        else if (m_effectType == EffectTypeEnum.ChangeAttribute)
        {
            int attributeID = int.Parse(EffectParam1);
            float coefficient = float.Parse(EffectParam2);

            for (int i = 0; i < m_actorsTargetList.Count; i++)
                m_actorsTargetList[i].ChangeAttribute(attributeID, coefficient, int.Parse(EffectParam3));
        }
        else if (m_effectType == EffectTypeEnum.Revive)
        {
            //EffectParam1
            //      1 = 使用施法者攻击力计算
            //      2 = 使用目标生命上限值计算
            float health = 0;
            if (int.Parse(EffectParam1) == 1)
                health = m_bindActor.AttackDamage;
            //EffectParam2
            //      治疗系数
            //      浮点数,例1.5 = 1.5倍
            float coefficient = float.Parse(EffectParam2);

            for (int i = 0; i < m_actorsTargetList.Count; i++)
            {
                if (int.Parse(EffectParam1) == 2)
                    health = m_actorsTargetList[i].Health;

                m_actorsTargetList[i].Renew();

                m_actorsTargetList[i].originalHealth = health * coefficient;
                m_actorsTargetList[i].Health         = health * coefficient;
                m_actorsTargetList[i].CurrentHealth  = health * coefficient;
            }
        }
        else if (m_effectType == EffectTypeEnum.SelfBurst)
        {
            //参数1
            //1 = 使用自身攻击力计算伤害
            //2 = 使用自身剩余生命值计算伤害
            //3 = 使用自身最高生命值计算伤害
            float damage = 0;
            int damageType = int.Parse(EffectParam1);
            if (damageType == 1)
                damage = m_bindActor.AttackDamage;
            else if (damageType == 2)
                damage = m_bindActor.CurrentHealth;
            else if (damageType == 3)
                damage = m_bindActor.Health;

            //伤害系数
            float coefficient = float.Parse(EffectParam2);
            float extra = 0;
            //固定值（整数，可以为0）
            if (!string.IsNullOrEmpty(EffectParam3))
                extra = float.Parse(EffectParam3);

            damage = damage * coefficient + extra;

            if (string.IsNullOrEmpty(EffectParam4) || string.IsNullOrEmpty(EffectParam5))
                return;
            //获得范围内的目标
            SkillRange skillRange = new SkillRange(int.Parse(EffectParam4));
            SkillTarget skillTarget = new SkillTarget(int.Parse(EffectParam5));
            List<GameEntity> listTarget = skillRange.GetRangeTargets(m_bindActor, skillTarget, 60);

            for (int i = 0; i < listTarget.Count; i++)
            {
                listTarget[i].SkillDamage(m_bindActor, m_bindSkill, damage);
            }

            //m_bindActor.CurrentHealth = 0;
        }
        else if (m_effectType == EffectTypeEnum.Clone)
        {
            //EffectParam1
            //      继承本体属性百分比10000 = 100 %，上限值为10000，向上取整
            //EffectParam2
            //      额外拥有的技能ID，多个ID用 | 间隔
            //EffectParam3
            //      分身持续时间 单位：毫秒
            //EffectParam4
            //      分身出场特效
            //EffectParam5
            //      分身出场动作

            float percent = float.Parse(EffectParam1) / 10000.0f;

            Clone(percent, EffectParam2, float.Parse(EffectParam3)/1000.0f);
        }
        else if (m_effectType == EffectTypeEnum.Summon)
        {
            string[] array = EffectParam1.Split(":");
            int monsterID = int.Parse(array[0]);
            int monsterCount = int.Parse(array[1]);
            Monster monster;
            TimeLimit timeLimit;

            for (int i = 0; i < monsterCount; i++)
            {
                float distance = UnityEngine.Random.Range(float.Parse(EffectParam4), float.Parse(EffectParam5));
                float angle = UnityEngine.Random.Range(0, 360);
                // 获取前方向
                Vector3 forward = m_bindActor.transform.forward;

                // 计算旋转后的方向
                Vector3 rotatedDirection = Quaternion.AngleAxis(angle, Vector3.up) * forward;

                // 计算新位置
                Vector3 newPosition = m_bindActor.transform.position + rotatedDirection * distance;

                monster = GameMain.Instance.CreateMonster(monsterID, m_bindActor.Camp, newPosition);

                monster.transform.forward = m_bindActor.transform.forward;
                timeLimit = monster.gameObject.AddComponent<TimeLimit>();
                timeLimit.StartTime(float.Parse(EffectParam2) / 1000.0f);
            }
        }
        else if (m_effectType == EffectTypeEnum.LightningChain)
        {
            int maxJumps = int.Parse(EffectParam2);
            string[] array = EffectParam1.Split("|");

            GameObject prefab = Resources.Load("SkillEffects\\LightningChain") as GameObject;
            for (int i = 0; i < m_actorsTargetList.Count; i++)
            {
                float damageCoefficient = float.Parse(array[(int)(m_actorsTargetList[i].Quality)]);
                float damage = m_releaser.AttackDamage * damageCoefficient;

                GameObject go = GameObject.Instantiate(prefab);
                LightningChain lightningChain = go.GetComponent<LightningChain>();
                lightningChain.CastLightningChain(m_actorsTargetList[i],damage, maxJumps);
            }
        }
        else if (m_effectType == EffectTypeEnum.ReduceCooldownTime)
        {
            Skill skill = m_bindActor.getActiveSkill();
            if (skill != null)
            {
                float coefficient = float.Parse(EffectParam1);
                skill.ReduceCooldownTime(coefficient);
            }
        }
        else if (m_effectType == EffectTypeEnum.RangeClear)
        {
            //EffectParam1
            //      清除要素类型 1 = 飞行道具
            //EffectParam2
            //      区域范围，填写圆心半径，单位：米
            float range = float.Parse(EffectParam2);
            float sqrRange = range * range;
            List<Bullet> listRemove = new List<Bullet>();
            for (int i = 0; i < Bullet.ListBullet.Count; i++)
            {
                if (m_bindActor.Camp != Bullet.ListBullet[i].Attacker.Camp)
                {
                    if( Vector3.SqrMagnitude( m_bindActor.transform.position - Bullet.ListBullet[i].transform.position) < sqrRange)
                        listRemove.Add(Bullet.ListBullet[i]);
                }
            }
            for(int i = 0; i < listRemove.Count; i++)
            {
                GameObject.Destroy(listRemove[i].gameObject);
            }

        }
        else if( m_effectType == EffectTypeEnum.Teleport)
        {
            //EffectParam1
            //      传送位置
            //      1 = 天枢核心（默认为建筑前方） 
            //EffectParam2
            //      传送耗时，单位：毫秒
            Vector3 destination =  MainBase.Instance.SpawnPoint[0].transform.position;
            if (GridPathfinding.HasObstaclesInRadius(destination, 0.5f))
            {
                float radius = 2;
                while (true)
                {
                    if(GridPathfinding.FindWalkableOnRadius(m_bindActor.transform, destination, radius, m_bindActor.Radius, out destination))
                        break;
                    radius++;
                    if (radius > 5)
                    {
                        Debug.LogError("传送位置不可行走");
                        return;
                    }
                }
            }
            ((Actor)m_bindActor).Teleport(destination, float.Parse(EffectParam2)/1000.0f);
        }
    }
    //>第一个参数代表计算技能伤害值的基础属性，定义参数值1 ~3分别代表不同的属性														
    //	1=使用技能释放者的攻击力来计算伤害值													
    //	2=使用技能释放者的生命上限值来计算伤害值													
    //	3=使用技能释放者的当前生命值来计算伤害值													
    //	4=使用技能受击者的生命上限值来计算伤害值													
    //>第二个参数是自定义的伤害系数值，用来参与伤害公式的计算，参数值=系数值，这个值是浮点数														
    //>第三个参数代表计算伤害值的固定伤害，参数值=固定伤害（整数，可以为0）														
    //	>当第一个参数=1时，按照通用的伤害计算公式来计算伤害值，通用伤害公式请查看本文档属性公式													
    //	>当第一个参数=2、3、4时，伤害值=参数1* 参数2+参数3													
    //>第四个参数代表造成伤害时是否触发吸血，定义参数值0和1分别代表不同含义，参数可留空，留空代表不触发														
    //	0=不触发吸血													
    //	1=触发吸血													
    //>第五个参数代表造成伤害时的吸血比例，群体技能以伤害衰减后的伤害值为基数来计算吸血量														
    //	>第四个参数是第五个参数的前置参数，当第四个参数=1时，第五个参数才会生效													
    //	>第五个参数的参数值=吸血比例，定义标准参数值10000=100%，保留小数点后2位													
    //	>当第七个参数值=1时，代表本次伤害效果会触发吸血，吸血量=实际伤害值* 吸血比例
    //
    //攻击伤害=max [ 攻方攻击力*攻击参数A+攻击参数B ，攻方攻击力*技能系数2*(1+攻方伤害加成-敌方伤害减免) +技能参数3]
    //攻击伤害=max [ 攻方攻击力*攻击参数A+攻击参数B ，（攻方攻击力*技能系数2-max（0，敌方防御-我方破防））*(1+攻方伤害加成-敌方伤害减免) +技能参数3]
    public void DamageEffect( GameEntity target)
    {
        GameEntity self = m_bindActor;

        float defense = target.Defense;
        if (self.buffSystem.haveArmorBreak)////攻击时无视目标防御
        {
            defense = defense*(1 - self.buffSystem.ArmorBreak);
        }

        float param1 = 0;
        float damage;
        float coefficient=0;
        int calculationMethod = int.Parse(EffectParam1);
        ////1=使用技能释放者的攻击力来计算伤害值
        if (calculationMethod == 1)
        {
            float damage1 = m_releaser.AttackDamage * fightConfig.atkA + fightConfig.atkB;

            float damage2 = (m_releaser.AttackDamage * float.Parse(EffectParam2) - Math.Max(0, defense - m_releaser.ArmorBreak)) * (1 + m_releaser.DamageBonus - target.DamageReduced) + float.Parse(EffectParam3);

            damage = Mathf.Max(damage1, damage2);
        }
        else
        {
            //2=使用技能释放者的生命上限值来计算伤害值
            if (calculationMethod == 2)
                param1 = m_releaser.Health;
            else if (calculationMethod == 3)//3=使用技能释放者的当前生命值来计算伤害值
                param1 = m_releaser.CurrentHealth;
            else if (calculationMethod == 4)//4=使用技能受击者的生命上限值来计算伤害值
                param1 = target.Health;

            coefficient = float.Parse(EffectParam2);

            damage = param1 * coefficient +  float.Parse(EffectParam3);
        }
        float baseDamage = damage;

        //元素伤害
        int damageType = int.Parse(EffectParam6);
        int damageNum = 0;
        if( m_bindSkill  != null )        
            damageNum = m_bindSkill.DamageNum;

        if (damageType != 0 && damageNum != 0)
            target.AddFiveElementDamage(self, self.ObjectType, damageType, damageNum);

        //攻击伤害加成
        Buff buff = self.buffSystem.getAttackDamageBonus();
        if (buff != null)
        {
            coefficient = buff.BuffData.buffParams1;
            damage += (baseDamage * (1+coefficient));
        }


        //生命值伤害加成
        buff = self.buffSystem.getHPDamageBonusBuff();
        if ( buff != null )
        {
            if ((target.CurrentHealth/target.Health) < buff.BuffData.buffParams2)
            {
                float bonus = buff.BuffData.buffParams3;
                if (bonus > 0)
                {
                    damage += (baseDamage * bonus);
                }
            }
        }
        //怪物种族伤害加成
        buff = self.buffSystem.getRaceDamageBonus();
        if (buff != null)
        {
            if( target.raceEnum == (RaceEnum)(int)buff.BuffData.buffParams1)
            {
                damage += (baseDamage * buff.BuffData.buffParams2);
            }
        }
        // 速度伤害加成
        buff = self.buffSystem.getSpeedDamageBonus();
        if (buff != null)
        {
            //伤害计算公式 = max{ 0，基础伤害 * [1 +（目标当前移速值 - 移速基础值）*加成系数）]}
            float newDamage = Mathf.Max(0, baseDamage * (1 + (target.MoveSpeed - buff.BuffData.buffParams1) * buff.BuffData.buffParams2));
            damage += (newDamage - baseDamage);
        }
        //伤害回血
        buff = self.buffSystem.getDamageLifesteal();
        if (buff != null)
        {
            float addHP = baseDamage * buff.BuffData.buffParams2;
            self.CurrentHealth += addHP;
            FloatTextManager.Instance.Add(((int)addHP).ToString(), self.GetAnchor(AnchorType.Top).position, 3, Color.green);
        }

        target.SkillDamage(self, m_bindSkill, damage);

        //是否吸血
        if (int.Parse(EffectParam4) > 0) 
        { 
            m_releaser.CurrentHealth += baseDamage * (float.Parse(EffectParam5) /10000.0f);
        }
    }

    void HealthRecover(GameEntity self, GameEntity target)
    {
        //EffectParam1
        // 1 = 使用攻击力计算
        // 2 = 使用生命上限值计算
        //EffectParam2
        //  治疗系数(浮点数)
        //EffectParam3
        // 固定值（整数，可以为0）

        float param1 = 0;
        float coefficient = float.Parse(EffectParam2);
        if (int.Parse(EffectParam1) == 1)
            param1 = target.AttackDamage;
        else if (int.Parse(EffectParam1) == 2)
            param1 = target.Health;

        float additional = 0;
        if (!string.IsNullOrEmpty(EffectParam3))
        {
            additional = float.Parse(EffectParam3);
        }

        float recover = param1 * coefficient + additional;
        target.HealthRecover(recover);
    }

    void BeatBack(GameEntity attacker, GameEntity target)
    {
        //EffectParam1
        //           1 - 固定击退距离=参数1(距离以目标与自身为直线，自身朝目标为正朝向，将目标击退，负数为反方向击退)
        //           2 - 变动击退距离 = min（参数2，参数1 - buff释放者与buff目标的距离）
        //           3 - 固定击退距离 = min(跟目标的距离，参数1)"	
        //EffectParam2
        //          参数1
        //EffectParam3
        //          可选：参数2
        //EffectParam4
        //          击退速度单位：米/秒
        //EffectParam5
        //          是否打断施法（留空代表不打断）
        //          0 = 不打断
        //          1 = 打断"

        float distance = 1;
        if (int.Parse(EffectParam1) == 1)
        {
            distance = float.Parse(EffectParam2);
        }
        else if (int.Parse(EffectParam1) == 2)
        {
            //min（参数3，参数2-效果释放者与效果目标之间的距离）	
            float d1 = float.Parse(EffectParam3);
            float d2 = float.Parse(EffectParam2) - Vector3.Distance(attacker.transform.position, target.transform.position);
            distance = Mathf.Min(d2, d1);
        }
        float speed = float.Parse(EffectParam4);
        Vector3 dir = target.transform.position - attacker.transform.position;
        dir.Normalize();
        target.BeatBack(dir, distance, speed, (int.Parse(EffectParam5) == 1));
    }

    //public override List<GameEntity> GetAllTarget()
    //{
    //    m_targetList.Clear();
    //    skillRange.GetRangeTargets(m_bindActor, ref m_targetList, skillTarget,50);

    //    List<GameEntity> list = m_targetList.Distinct().ToList();
    //    m_targetList = list;

    //    m_targetList.RemoveAll(actor => actor.CannotBeSelected);

    //    return m_targetList;
    //}


    private void Clone(float percent, string skillID, float cloneDuration)
    {
        Vector3 clonePosition = m_bindActor.transform.position;
        //Vector3 position = m_bindActor.transform.position;

        float radius = m_bindActor.Radius + 0.5f;
        while(true)
        {
            if (GridPathfinding.FindWalkableOnRadius(m_bindActor.transform, clonePosition, radius, m_bindActor.Radius, out clonePosition))
                break;
            radius++;
        }

        Actor actor = ((Actor)m_bindActor).Clone(percent, skillID, cloneDuration);

        actor.transform.position = clonePosition;
    }

}

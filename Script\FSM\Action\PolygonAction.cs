using System.Collections.Generic;
using FSM;
using UnityEngine;
using UnityEngine.AI;
// 巡逻
namespace FSM
{
    [CreateAssetMenu(menuName = "FSM/Actions/PolygonAction")]
    public class PolygonAction : FSMAction
    {
        public List<Vector3> patrolPoints; // 巡逻点
        public override void Entering(BaseStateMachine stateMachine)
        {
            // 初始化
            patrolPoints = stateMachine.patrolPoints;
            if (patrolPoints.Count > 0)
            {
                stateMachine.Destination = patrolPoints[0];

                stateMachine.navMeshAgent.isStopped = false;
                stateMachine.navMeshAgent.speed = stateMachine.Attacher.MoveSpeed;
                stateMachine.navMeshAgent.SetDestination(stateMachine.Destination);
            }
        }
        public override void Execute(BaseStateMachine stateMachine)
        {
            GridAgent navMeshAgent = stateMachine.navMeshAgent;
            if (navMeshAgent.pathStatus == PathStatus.Searching) return;

            //var patrolPoints = stateMachine.patrolPoints;
            // 移动中
            //if (Vector3.Distance(stateMachine.Destination, stateMachine.Attacher.transform.position) > 1)
            //{
            //    stateMachine.Attacher.PlayRun();
            //    // 调整朝向
            //    Vector3 dir = stateMachine.Destination - stateMachine.Attacher.transform.position;
            //    dir.Normalize();
            //    if (dir != Vector3.zero)
            //        stateMachine.Attacher.transform.forward = dir;
            //    // 网格导航的参数
            //    navMeshAgent.isStopped = false;
            //    navMeshAgent.speed = stateMachine.Attacher.MoveSpeed;
            //    navMeshAgent.SetDestination(stateMachine.Destination);
            //}
            if( navMeshAgent.pathComplete)
            {
                stateMachine.Attacher.PlayIdle();
                // 满足累加时间后巡逻下一个目标点
                if (stateMachine.m_lastTime >= 2)
                {
                    var list = patrolPoints.FindAll(x => x != stateMachine.Destination);
                    int index = Random.Range(0, list.Count);
                    stateMachine.Destination = list[index];
                    stateMachine.m_lastTime = 0;

                    navMeshAgent.isStopped = false;
                    navMeshAgent.speed = stateMachine.Attacher.MoveSpeed;
                    navMeshAgent.SetDestination(stateMachine.Destination);
                }
                else
                {
                    // 累加时间
                    stateMachine.m_lastTime += Time.deltaTime;
                }

            }


        }
        public override void Leaving(BaseStateMachine stateMachine)
        {
            // 清除当前巡逻点
            stateMachine.patrolPoints.Clear();
            stateMachine.m_lastTime = 0;
        }
    }
}
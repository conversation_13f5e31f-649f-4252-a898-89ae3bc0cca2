%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &51151394416030297
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7439071665363082746}
  m_Layer: 0
  m_Name: RigLFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7439071665363082746
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 51151394416030297}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0017842993, y: 0.038817674, z: 0.15180486, w: 0.9876464}
  m_LocalPosition: {x: -0.040451184, y: 0, z: -0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5185032383940699762}
  m_Father: {fileID: 6234271148349601036}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &151861183073094877
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3825369302702895767}
  m_Layer: 8
  m_Name: anchorCenter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3825369302702895767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151861183073094877}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.795, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &252594825331229006
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 500098865933736289}
  m_Layer: 0
  m_Name: RigSpine3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &500098865933736289
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 252594825331229006}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000004032032, y: -0.0000000021958935, z: -0.092958696, w: 0.99566996}
  m_LocalPosition: {x: -0.14642449, y: 0.0000000023841857, z: 9.094947e-15}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5908932833676599658}
  m_Father: {fileID: 8775124600775636151}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &394591529482165178
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7818844007900781804}
  m_Layer: 0
  m_Name: RigLToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7818844007900781804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 394591529482165178}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014886793, y: -0.0057130647, z: -0.35732222, w: 0.93384504}
  m_LocalPosition: {x: -0.13313861, y: -0.000051307677, z: -0.0000045394895}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5529895661711568585}
  m_Father: {fileID: 6728213327434447183}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &590221220367530651
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1232409052644580745}
  m_Layer: 0
  m_Name: RigRFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1232409052644580745
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590221220367530651}
  serializedVersion: 2
  m_LocalRotation: {x: -0.008941533, y: 0.04077184, z: -0.035507925, w: 0.99849737}
  m_LocalPosition: {x: -0.13877109, y: 0.00067558285, z: 0.007317619}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3180253932332379970}
  m_Father: {fileID: 8102805073511311206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &609315216298532693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6075989484452779980}
  m_Layer: 0
  m_Name: RigLCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6075989484452779980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609315216298532693}
  serializedVersion: 2
  m_LocalRotation: {x: -0.060566247, y: 0.009119826, z: 0.07917347, w: 0.9949775}
  m_LocalPosition: {x: -0.25720724, y: 0.0000071310997, z: -0.00000039100647}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6728213327434447183}
  m_Father: {fileID: 2680169349238525213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &632938385138757038
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8102805073511311206}
  m_Layer: 0
  m_Name: RigRPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8102805073511311206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 632938385138757038}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71642655, y: -0.073651426, z: 0.045939587, w: 0.6922413}
  m_LocalPosition: {x: -0.20861332, y: 0.000000038146972, z: 0}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2864400638940369533}
  - {fileID: 1232409052644580745}
  - {fileID: 3912255027695329097}
  m_Father: {fileID: 6136149036913204444}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1052315257479196443
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6136149036913204444}
  m_Layer: 0
  m_Name: RigRForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6136149036913204444
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1052315257479196443}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0060543306, y: -0.03270579, z: -0.18547416, w: 0.98208606}
  m_LocalPosition: {x: -0.27621725, y: 0, z: 0}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8102805073511311206}
  m_Father: {fileID: 8188588276455584813}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1280210143827807165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6322741102845587501}
  - component: {fileID: 1254783291767630834}
  - component: {fileID: 6297620132992772348}
  - component: {fileID: 5998931571811481050}
  m_Layer: 13
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6322741102845587501
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1280210143827807165}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 5, y: 5, z: 5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1254783291767630834
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1280210143827807165}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6297620132992772348
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1280210143827807165}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a1f798269267cd74e8ba30ecb7d68be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &5998931571811481050
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1280210143827807165}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1806960459130897951
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8788392490955312107}
  m_Layer: 0
  m_Name: RigRFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8788392490955312107
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1806960459130897951}
  serializedVersion: 2
  m_LocalRotation: {x: -0.01778631, y: 0.017348096, z: 0.059156116, w: 0.9979395}
  m_LocalPosition: {x: -0.03831825, y: 0.000000038146972, z: 0.000000114440915}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3180253932332379970}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1931417511289956198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1883043802320481857}
  m_Layer: 0
  m_Name: RigHead
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1883043802320481857
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1931417511289956198}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007756447, y: -0.00000008983025, z: 0.09581774, w: 0.99539894}
  m_LocalPosition: {x: -0.050812453, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8386782363765787892}
  - {fileID: 1182113112218663304}
  m_Father: {fileID: 3002869414499527152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1988303614589481228
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6284110837353195351}
  m_Layer: 0
  m_Name: RigLFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6284110837353195351
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1988303614589481228}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012239547, y: -0.0048797904, z: 0.0341146, w: 0.99933106}
  m_LocalPosition: {x: -0.053491116, y: 0, z: 0}
  m_LocalScale: {x: 0.9999993, y: 1.0000001, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7394210418412728667}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2081769640922300240
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8617726563041428211}
  m_Layer: 8
  m_Name: anchorTop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8617726563041428211
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2081769640922300240}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.905, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2130834107554365390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3002869414499527152}
  m_Layer: 0
  m_Name: RigNeck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3002869414499527152
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2130834107554365390}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000007756446, y: 0.000000089830266, z: -0.09581775, w: 0.99539894}
  m_LocalPosition: {x: 0.005050278, y: -0.031543914, z: -0.0000000021330198}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1883043802320481857}
  m_Father: {fileID: 5908932833676599658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2153237265999479007
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4102441399298339227}
  - component: {fileID: 2221729398104301216}
  - component: {fileID: 525083059901797721}
  m_Layer: 0
  m_Name: Head 01 Fair
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4102441399298339227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2153237265999479007}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1182113112218663304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2221729398104301216
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2153237265999479007}
  m_Mesh: {fileID: 4300000, guid: a50e3f7f3e580c843a6a31d558faccc9, type: 3}
--- !u!23 &525083059901797721
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2153237265999479007}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7d64267e5eb8ca74d8c9c25ffd8dcd35, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2267546567259250462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3672775100345658935}
  m_Layer: 0
  m_Name: RigRFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3672775100345658935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2267546567259250462}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02642038, y: -0.020399032, z: -0.40813255, w: 0.9123123}
  m_LocalPosition: {x: -0.20291305, y: -0.0000000017881393, z: 0.000000009536743}
  m_LocalScale: {x: 0.9999986, y: 1.0000013, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3451792414859024436}
  m_Father: {fileID: 8394838872087553977}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2722157267878740476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3715837676770067991}
  m_Layer: 0
  m_Name: RigLPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3715837676770067991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2722157267878740476}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0.11712611, y: 0.0044879722, z: 0.100568734}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2734951453501832454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5185032383940699762}
  m_Layer: 0
  m_Name: RigLFinger23
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5185032383940699762
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2734951453501832454}
  serializedVersion: 2
  m_LocalRotation: {x: 0.017789682, y: -0.017345062, z: 0.059156764, w: 0.99793947}
  m_LocalPosition: {x: -0.03831825, y: -0.000000038146972, z: 0.000000114440915}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7439071665363082746}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2893594248475620767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6816392660218150604}
  m_Layer: 8
  m_Name: anchorBullet
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6816392660218150604
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2893594248475620767}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.84, z: 0.91}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2941064350160856374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6234271148349601036}
  m_Layer: 0
  m_Name: RigLFinger21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6234271148349601036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941064350160856374}
  serializedVersion: 2
  m_LocalRotation: {x: 0.008941438, y: -0.040772375, z: -0.035507936, w: 0.9984973}
  m_LocalPosition: {x: -0.13877106, y: 0.00067565916, z: -0.0073177717}
  m_LocalScale: {x: 1, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7439071665363082746}
  m_Father: {fileID: 235331962691141483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3185344961982209470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7019204867420531074}
  m_Layer: 0
  m_Name: RigLFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7019204867420531074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3185344961982209470}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00091992173, y: 0.47066158, z: 0.0029012684, w: 0.8823086}
  m_LocalPosition: {x: -0.035616796, y: 0.00009338379, z: 0.052462384}
  m_LocalScale: {x: 0.99999934, y: 1.0000001, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7394210418412728667}
  m_Father: {fileID: 235331962691141483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3217325733271724455
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8032048404943034038}
  m_Layer: 0
  m_Name: RigRThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8032048404943034038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3217325733271724455}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037409574, y: 0.9985059, z: 0.038666777, w: -0.009564561}
  m_LocalPosition: {x: 0.06101242, y: 0.000443881, z: -0.115600996}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8394838872087553977}
  m_Father: {fileID: 6674190536688115028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3254805175485937062
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2869806349760715487}
  - component: {fileID: 7302832777508194992}
  m_Layer: 0
  m_Name: Male Knight 02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2869806349760715487
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3254805175485937062}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7302832777508194992
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3254805175485937062}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5a2367a59560a9a46ad181a95549a71d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 7362b8aa007d8f14aaf5e851294e1e05, type: 3}
  m_Bones:
  - {fileID: 1883043802320481857}
  - {fileID: 5908932833676599658}
  - {fileID: 500098865933736289}
  - {fileID: 8775124600775636151}
  - {fileID: 6907818060464879739}
  - {fileID: 6674190536688115028}
  - {fileID: 8188588276455584813}
  - {fileID: 6136149036913204444}
  - {fileID: 3523815073623967908}
  - {fileID: 342429270712673935}
  - {fileID: 6023538033361899790}
  - {fileID: 5744056933600457573}
  - {fileID: 235331962691141483}
  - {fileID: 7019204867420531074}
  - {fileID: 7394210418412728667}
  - {fileID: 6234271148349601036}
  - {fileID: 7439071665363082746}
  - {fileID: 6284110837353195351}
  - {fileID: 5185032383940699762}
  - {fileID: 8102805073511311206}
  - {fileID: 2864400638940369533}
  - {fileID: 1945755010069102200}
  - {fileID: 1232409052644580745}
  - {fileID: 3180253932332379970}
  - {fileID: 136890306661159544}
  - {fileID: 8788392490955312107}
  - {fileID: 8032048404943034038}
  - {fileID: 2680169349238525213}
  - {fileID: 6075989484452779980}
  - {fileID: 6728213327434447183}
  - {fileID: 7818844007900781804}
  - {fileID: 5529895661711568585}
  - {fileID: 8394838872087553977}
  - {fileID: 3672775100345658935}
  - {fileID: 3451792414859024436}
  - {fileID: 4372905011394219177}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6674190536688115028}
  m_AABB:
    m_Center: {x: 0.00379771, y: 0.02045983, z: 0}
    m_Extent: {x: 0.6702048, y: 0.24179909, z: 0.60506284}
  m_DirtyAABB: 0
--- !u!1 &3373573751925838675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3465990053292512814}
  m_Layer: 0
  m_Name: Rig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3465990053292512814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3373573751925838675}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3429633026474553637
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 342429270712673935}
  m_Layer: 0
  m_Name: RigLUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &342429270712673935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3429633026474553637}
  serializedVersion: 2
  m_LocalRotation: {x: 0.021966677, y: 0.30736104, z: 0.0071289334, w: 0.9513127}
  m_LocalPosition: {x: -0.14668106, y: 0.00000004053116, z: -0.000000076293944}
  m_LocalScale: {x: 0.9999998, y: 0.99999994, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6023538033361899790}
  m_Father: {fileID: 3523815073623967908}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3460346300045791091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7394210418412728667}
  m_Layer: 0
  m_Name: RigLFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7394210418412728667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3460346300045791091}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0012692147, y: -0.017850408, z: 0.07902566, w: 0.99671197}
  m_LocalPosition: {x: -0.05138337, y: 0, z: 0}
  m_LocalScale: {x: 0.99999934, y: 1.0000001, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6284110837353195351}
  m_Father: {fileID: 7019204867420531074}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3894545733962848230
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1182113112218663304}
  m_Layer: 0
  m_Name: + Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1182113112218663304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3894545733962848230}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999958, y: 0.5, z: 0.50000036, w: 0.5000002}
  m_LocalPosition: {x: 1.2133555, y: 0.014542067, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4102441399298339227}
  - {fileID: 1849945740476986088}
  - {fileID: 7622245095982016566}
  m_Father: {fileID: 1883043802320481857}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3952871081515795044
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5529895661711568585}
  m_Layer: 0
  m_Name: RigLToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5529895661711568585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3952871081515795044}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0005379386, y: -0.0016754845, z: 0.9954929, w: 0.09481993}
  m_LocalPosition: {x: -0.05733176, y: 0.0000000011920929, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7818844007900781804}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4033168944454024452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8775124600775636151}
  m_Layer: 0
  m_Name: RigSpine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8775124600775636151
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4033168944454024452}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000003637839, y: 0.0000000053254308, z: 0.00037867192, w: 0.99999994}
  m_LocalPosition: {x: -0.12812133, y: 0, z: 0}
  m_LocalScale: {x: 1.0000004, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 500098865933736289}
  m_Father: {fileID: 6907818060464879739}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4349077977802535898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3451792414859024436}
  m_Layer: 0
  m_Name: RigRToe11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3451792414859024436
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4349077977802535898}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014476015, y: 0.005883097, z: -0.37644693, w: 0.9263064}
  m_LocalPosition: {x: -0.12765376, y: -0.00000377655, z: 0.0000016212463}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4372905011394219177}
  m_Father: {fileID: 3672775100345658935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4444341325090481145
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4091244067766483659}
  - component: {fileID: 8257671921212069341}
  - component: {fileID: 9219316550258891225}
  m_Layer: 0
  m_Name: Sword Awry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4091244067766483659
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4444341325090481145}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3912255027695329097}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8257671921212069341
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4444341325090481145}
  m_Mesh: {fileID: 4300000, guid: 27974b50f5ee5ad438871feaa61e8342, type: 3}
--- !u!23 &9219316550258891225
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4444341325090481145}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4759435709632075182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6916820020257807764}
  - component: {fileID: 9161174666109189437}
  - component: {fileID: 1598477086768016220}
  m_Layer: 0
  m_Name: Shield Nail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6916820020257807764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4759435709632075182}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2403150604414229457}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &9161174666109189437
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4759435709632075182}
  m_Mesh: {fileID: 4300000, guid: c7f8ad2cb5707bc4ebe2ba2def459c50, type: 3}
--- !u!23 &1598477086768016220
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4759435709632075182}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5047726902719909591
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1945755010069102200}
  m_Layer: 0
  m_Name: RigRFinger12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1945755010069102200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5047726902719909591}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0012681836, y: 0.017850175, z: 0.07902588, w: 0.99671197}
  m_LocalPosition: {x: -0.051383417, y: 0, z: 0}
  m_LocalScale: {x: 1.0000004, y: 1.0000001, z: 0.9999996}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 136890306661159544}
  m_Father: {fileID: 2864400638940369533}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5299589424089802385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5004794624577573399}
  m_Layer: 0
  m_Name: + Back
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5004794624577573399
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5299589424089802385}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999985, y: 0.50000006, z: 0.5000001, w: 0.5}
  m_LocalPosition: {x: 0.13352661, y: -0.18530917, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6566094201734822205}
  m_Father: {fileID: 5908932833676599658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5414491945969168166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2098870540804557516}
  m_Layer: 8
  m_Name: anchorBottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2098870540804557516
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5414491945969168166}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5529893797134513514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 136890306661159544}
  m_Layer: 0
  m_Name: RigRFinger13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &136890306661159544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5529893797134513514}
  serializedVersion: 2
  m_LocalRotation: {x: -0.01224037, y: 0.004878792, z: 0.034112968, w: 0.9993311}
  m_LocalPosition: {x: -0.053491153, y: -0.000000076293944, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000004, y: 1.0000001, z: 0.9999996}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945755010069102200}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5776626880863628016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5908932833676599658}
  m_Layer: 0
  m_Name: RigRibcage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5908932833676599658
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5776626880863628016}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000035377564, y: -0.00000008137332, z: 0.07053914, w: 0.997509}
  m_LocalPosition: {x: -0.17131469, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3523815073623967908}
  - {fileID: 3002869414499527152}
  - {fileID: 5744056933600457573}
  - {fileID: 5004794624577573399}
  m_Father: {fileID: 500098865933736289}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5931165749640596152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6728213327434447183}
  m_Layer: 0
  m_Name: RigLFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6728213327434447183
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931165749640596152}
  serializedVersion: 2
  m_LocalRotation: {x: 0.025892146, y: 0.02066955, z: -0.43320212, w: 0.9006877}
  m_LocalPosition: {x: -0.20310445, y: -0.0000000011920929, z: 0}
  m_LocalScale: {x: 1.0000017, y: 0.99999857, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7818844007900781804}
  m_Father: {fileID: 6075989484452779980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5934662030255959611
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3387636230696545824}
  - component: {fileID: 4412130253779992316}
  - component: {fileID: 7642857936364980608}
  - component: {fileID: 5249392413491177967}
  - component: {fileID: 121069672777575109}
  - component: {fileID: 5950305873977473734}
  - component: {fileID: 7171893882516069643}
  m_Layer: 0
  m_Name: jinzhan 02 huo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3387636230696545824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.97425216, z: 0, w: 0.2254612}
  m_LocalPosition: {x: 6.22, y: 0, z: 1.33}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2869806349760715487}
  - {fileID: 3465990053292512814}
  - {fileID: 3715837676770067991}
  - {fileID: 6674190536688115028}
  - {fileID: 5779100838447543559}
  - {fileID: 2098870540804557516}
  - {fileID: 8617726563041428211}
  - {fileID: 3825369302702895767}
  - {fileID: 6816392660218150604}
  - {fileID: 6322741102845587501}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 153.94, z: 0}
--- !u!95 &4412130253779992316
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: f6605568d3cd20e4db1a0b19cfe83787, type: 3}
  m_Controller: {fileID: 9100000, guid: 349b08dac79b72b4096e1eafb4934e74, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &7642857936364980608
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0fe0c0abe69c93a4f81ad3fc643500a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_camp: 1
  m_targetValue: 100
  m_id: 0
  m_health: 0
  m_currentHealth: 0
  m_defense: 0
  m_attackDamage: 0
  m_attackPecent: 0
  m_healthPercent: 0
  m_attackSpeed: 0
  m_moveSpeed: 0
  m_coolingReduced: 0
  m_damageBonus: 0
  m_damageReduced: 0
  m_attackRange: 1
  m_raceEnum: 0
  m_attackSpeedChange: 1
  m_disableAction: 0
  m_Anchors:
  - {fileID: 3825369302702895767}
  - {fileID: 2098870540804557516}
  - {fileID: 8617726563041428211}
  - {fileID: 6816392660218150604}
  m_radius: 0.5
  m_normalAttackCount: 0
  m_StateMachine:
    m_destination: {x: 0, y: 0, z: 0}
    m_currentState: {fileID: 0}
    startLoopTime: 0
    startPosition: {x: 0, y: 0, z: 0}
    direction: {x: 0, y: 0, z: 0}
    distance: 0
    speed: 0
    beatBackFinish: 0
    isBeatBack: 0
    patrolPoints: []
    m_lastTime: 0
  m_idleState: {fileID: 11400000, guid: 3ceb064498b04784fb44b5598bac2ed4, type: 2}
  m_skillCastState: {fileID: 11400000, guid: 0417d335b5697f448bff0e41bde819fc, type: 2}
  m_runToSkillTargetState: {fileID: 11400000, guid: bf165ea46cb450a41b2ee8bd298edd87, type: 2}
  m_rushState: {fileID: 11400000, guid: e50da9d6eafea494287f398dc0c224a2, type: 2}
  m_guardState: {fileID: 0}
  m_deathState: {fileID: 11400000, guid: 0cc21a66f1586c64f9d6a732c22b2fc5, type: 2}
  m_beatBackState: {fileID: 0}
  m_polygonState: {fileID: 0}
  m_runToDestinationState: {fileID: 0}
  m_targetActor: {fileID: 0}
  horizontal: 0
  vertical: 0
  lastNormalizedTime: 0
--- !u!195 &5249392413491177967
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  m_Enabled: 0
  m_AgentTypeID: -334000983
  m_Radius: 0.6
  m_Speed: 3.5
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 0.5
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 1
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 0
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!136 &121069672777575109
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 448
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 1, z: 0}
--- !u!54 &5950305873977473734
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &7171893882516069643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5934662030255959611}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3b1a70ab69e5774eb62f9e4d3bac895, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_speed: 5
  m_stoppingDistance: 0.1
  m_rotationSpeed: 360
  m_autoBraking: 1
  m_pathStatus: 0
--- !u!1 &5947893442776005178
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4372905011394219177}
  m_Layer: 0
  m_Name: RigRToe12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4372905011394219177
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5947893442776005178}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0006140422, y: 0.0014642962, z: 0.98400944, w: 0.17810921}
  m_LocalPosition: {x: -0.057583522, y: 0.0000000011920929, z: 0.000000009536743}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3451792414859024436}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6278582186664137940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2403150604414229457}
  m_Layer: 0
  m_Name: + L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2403150604414229457
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6278582186664137940}
  serializedVersion: 2
  m_LocalRotation: {x: 0.68782973, y: 0.7256809, z: -0.013955802, w: -0.009100813}
  m_LocalPosition: {x: -0.10299998, y: -0.072999895, z: 0.0039999937}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6916820020257807764}
  m_Father: {fileID: 235331962691141483}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &6719942442610312091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6566094201734822205}
  - component: {fileID: 425759890649863776}
  - component: {fileID: 4750085746259547840}
  m_Layer: 0
  m_Name: Survival Backpack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6566094201734822205
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6719942442610312091}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.000000009599307, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5004794624577573399}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &425759890649863776
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6719942442610312091}
  m_Mesh: {fileID: 4300000, guid: 8c0c9fbab8d6cc14aa9c6dc342ea630a, type: 3}
--- !u!23 &4750085746259547840
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6719942442610312091}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 35c81461f83c1864599cad9751c3cef1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6883200663798182123
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7622245095982016566}
  - component: {fileID: 2829714031093153472}
  - component: {fileID: 7138658682759618931}
  m_Layer: 0
  m_Name: Helmet Endurance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7622245095982016566
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6883200663798182123}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1182113112218663304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2829714031093153472
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6883200663798182123}
  m_Mesh: {fileID: 4300000, guid: 4c685bee2c3d23b4088411e5b3efb352, type: 3}
--- !u!23 &7138658682759618931
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6883200663798182123}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5ccae13f286ac54e9385f59d48ed5cf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6911856041476108782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3912255027695329097}
  m_Layer: 0
  m_Name: + R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3912255027695329097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6911856041476108782}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6878299, y: 0.72568077, z: -0.013955591, w: -0.009101371}
  m_LocalPosition: {x: -0.12100026, y: -0.048999973, z: 0.023000093}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4091244067766483659}
  m_Father: {fileID: 8102805073511311206}
  m_LocalEulerAnglesHint: {x: 0.44300002, y: -178.143, z: 86.939}
--- !u!1 &6926896031878184199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5744056933600457573}
  m_Layer: 0
  m_Name: RigRCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5744056933600457573
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6926896031878184199}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013815174, y: -0.8200881, z: 0.019317467, w: 0.57174426}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: -0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8188588276455584813}
  m_Father: {fileID: 5908932833676599658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7110416268909858299
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8188588276455584813}
  m_Layer: 0
  m_Name: RigRUpperarm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8188588276455584813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7110416268909858299}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02196675, y: -0.30736113, z: 0.0071289074, w: 0.95131266}
  m_LocalPosition: {x: -0.14668112, y: -0.00000004529953, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 1, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6136149036913204444}
  m_Father: {fileID: 5744056933600457573}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7161543352834503373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6674190536688115028}
  m_Layer: 0
  m_Name: RigPelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6674190536688115028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7161543352834503373}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: -0.00000013351438, y: 0.6149364, z: 0.0003728537}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2680169349238525213}
  - {fileID: 8032048404943034038}
  - {fileID: 6907818060464879739}
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7246866075944496062
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 235331962691141483}
  m_Layer: 0
  m_Name: RigLPalm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &235331962691141483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7246866075944496062}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7164266, y: 0.073651, z: 0.045939766, w: 0.6922413}
  m_LocalPosition: {x: -0.20861335, y: -0.000000076293944, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7019204867420531074}
  - {fileID: 6234271148349601036}
  - {fileID: 2403150604414229457}
  m_Father: {fileID: 6023538033361899790}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7350330874819228866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8394838872087553977}
  m_Layer: 0
  m_Name: RigRCalf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8394838872087553977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7350330874819228866}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06117853, y: -0.008315124, z: 0.06785113, w: 0.99578327}
  m_LocalPosition: {x: -0.2570285, y: 0.0000067472456, z: 0.0000004196167}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3672775100345658935}
  m_Father: {fileID: 8032048404943034038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7451896455822262214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2864400638940369533}
  m_Layer: 0
  m_Name: RigRFinger11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2864400638940369533
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7451896455822262214}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00091970083, y: -0.4706613, z: 0.0029011748, w: 0.8823087}
  m_LocalPosition: {x: -0.035616912, y: 0.00009338379, z: -0.052462384}
  m_LocalScale: {x: 1.0000004, y: 1, z: 0.9999995}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1945755010069102200}
  m_Father: {fileID: 8102805073511311206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7709581641210594372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6023538033361899790}
  m_Layer: 0
  m_Name: RigLForearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6023538033361899790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7709581641210594372}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0060543697, y: 0.032705784, z: -0.185474, w: 0.9820861}
  m_LocalPosition: {x: -0.27621725, y: 0, z: 0}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 235331962691141483}
  m_Father: {fileID: 342429270712673935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8162467770798551913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8386782363765787892}
  m_Layer: 0
  m_Name: RigHeadBone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8386782363765787892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8162467770798551913}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000006664002, y: 8.4669435e-29, z: -1.2705494e-21, w: 1}
  m_LocalPosition: {x: -0.49453285, y: 0.09610081, z: 0.00000008530383}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1883043802320481857}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8235583104181250925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680169349238525213}
  m_Layer: 0
  m_Name: RigLThigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680169349238525213
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8235583104181250925}
  serializedVersion: 2
  m_LocalRotation: {x: -0.041629344, y: 0.9983547, z: -0.038122114, w: 0.0100836065}
  m_LocalPosition: {x: 0.061012648, y: -0.0036894195, z: 0.11560108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6075989484452779980}
  m_Father: {fileID: 6674190536688115028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8609855491844523557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3180253932332379970}
  m_Layer: 0
  m_Name: RigRFinger22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3180253932332379970
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609855491844523557}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0017855217, y: -0.038816083, z: 0.15180457, w: 0.98764646}
  m_LocalPosition: {x: -0.04045116, y: 0, z: -0.000000038146972}
  m_LocalScale: {x: 0.9999998, y: 1.0000002, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8788392490955312107}
  m_Father: {fileID: 1232409052644580745}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8613424701438044390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5779100838447543559}
  m_Layer: 0
  m_Name: RigRPlatform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5779100838447543559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8613424701438044390}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50000006, y: -0.50000006, z: -0.49999997, w: 0.49999997}
  m_LocalPosition: {x: 0.117125824, y: 0.004488783, z: 0.09778633}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3387636230696545824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8664857959302146654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1849945740476986088}
  - component: {fileID: 8956088634780096420}
  - component: {fileID: 1095131697172325509}
  m_Layer: 0
  m_Name: Face Male 02 Orange
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1849945740476986088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664857959302146654}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1182113112218663304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8956088634780096420
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664857959302146654}
  m_Mesh: {fileID: 4300000, guid: a91e2c8bbd0e53447adeb7d239346501, type: 3}
--- !u!23 &1095131697172325509
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8664857959302146654}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3f7aafce9309b2d4f8bf48e4a9fa3be4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8890664867541582315
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6907818060464879739}
  m_Layer: 0
  m_Name: RigSpine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6907818060464879739
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8890664867541582315}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000014107537, y: 7.023184e-10, z: 0.022114862, w: 0.99975544}
  m_LocalPosition: {x: -0.10970802, y: -0.0049213637, z: 7.3929185e-10}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8775124600775636151}
  m_Father: {fileID: 6674190536688115028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9069382265431687359
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3523815073623967908}
  m_Layer: 0
  m_Name: RigLCollarbone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3523815073623967908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9069382265431687359}
  serializedVersion: 2
  m_LocalRotation: {x: -0.013815689, y: 0.8200879, z: 0.019317966, w: 0.5717445}
  m_LocalPosition: {x: 0.030096358, y: -0.031506512, z: 0.06875161}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 342429270712673935}
  m_Father: {fileID: 5908932833676599658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

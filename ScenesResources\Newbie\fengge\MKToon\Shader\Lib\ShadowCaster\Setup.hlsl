﻿//////////////////////////////////////////////////////
// M<PERSON> <PERSON><PERSON> ShadowCaster Setup			       		//
//					                                //
// Created by <PERSON>                       //
// www.micha<PERSON>k<PERSON><PERSON>.de                            //
// Copyright © 2020 All rights reserved.            //
//////////////////////////////////////////////////////

#ifndef MK_TOON_SHADOWCASTER_SETUP
	#define MK_TOON_SHADOWCASTER_SETUP

	#ifndef MK_SHADOWCASTER_PASS
		#define MK_SHADOWCASTER_PASS
	#endif

	#include "../Core.hlsl"

	#if defined(MK_SURFACE_TYPE_TRANSPARENT) && SHADER_TARGET > 30
		#ifndef MK_TOON_DITHER_MASK
			#define MK_TOON_DITHER_MASK
		#endif
	#endif

	//Hightmap is only needed if a UV is required
	#if !defined(<PERSON><PERSON>_TEXCLR) && !defined(M<PERSON>_DISSOLVE)
		#ifdef MK_<PERSON>
			#undef MK_PARALLAX
		#endif
	#endif

	#include "ProgramShadowCaster.hlsl"
#endif
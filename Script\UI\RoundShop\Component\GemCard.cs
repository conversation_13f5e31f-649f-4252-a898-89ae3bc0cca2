using UnityEngine;
using Excel.shop;
using Excel.empower;
using System.Collections.Generic;
using System;
using Excel.lable;
/// <summary>
/// 密宝UI
/// </summary>
[Serializable] 
public class GemGoods: BaseGoodsData
{
    public empowerItemEntity dataEntity;
}
public class GemCard : BaseGoodsItem
{
    [SerializeField] LabelIcon GemLabel; // 辅助宝石标签
    private List<gecang_gemEntity> weightData; //权重数据
    private List<empowerItemEntity> BaseSourceData; // 源数据
    private WeightFilter<gecang_gemEntity, GemGoods> weightFilter = null; // 权重筛选器
    public void Awake() {
        // 加载Excel数据
        weightData = ExcelData.Instance.ShopExcel?.gecang_gemList;
        empowerExcel gemExcelData = ExcelData.Instance.EmpowerExcel;
        BaseSourceData = gemExcelData?.empowerItemList;

        List<GemGoods> gemItems = getGemItems();
        // 权重筛选器
        weightFilter = new WeightFilter<gecang_gemEntity, GemGoods>(
            weightData,
            gemItems,
            x => x.id,
            x => x.weight,
            x => x.refFront,
            x => x.roundLimit,
            x => x.buyNumMax,
            x => x.id
        );
    }

    // 获取已购买的essence
    private List<GemGoods> getGemItems()
    {
        List<GemGoods> result = new List<GemGoods>();
        List<BaseGoodsData> baseGoodsItems = OtherInventory.Instance?.buyList?.FindAll(x=> x.goodsType == (int)GoodsType.Gems);
        
        if (baseGoodsItems != null && baseGoodsItems.Count > 0)
        {
            for (int i = 0; i < baseGoodsItems.Count; i++)
            {
                var data = baseGoodsItems[i] as GemGoods;
                result.Add(data);
            }
        }
        return result;
    }

    // 渲染详情信息
    private void setInfomation() 
    {
        GemGoods goods = baseGoodsData as GemGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(itemsDetail == null || dataEntity == null) return;
        // 定义好符文的品质数据做转换
        itemsDetail.goodType = (int)GoodsType.Gems;
        itemsDetail.Title = Language.GetText(dataEntity.name); // 名字
        itemsDetail.isAuxiliary = dataEntity.subType == 2; // 是否是辅助
        // itemsDetail.level = dataEntity.lv; //等级
        // 标签信息
        List<MutationsLabelData> labelsList = new List<MutationsLabelData>();
        string [] labelsSplit = dataEntity.label.Split('|');
        for(int i = 0; i < labelsSplit.Length; i++)
        {
            int labelId = int.Parse(labelsSplit[i]);
            lableEntity label = ExcelData.Instance.getLableEntity(labelId);
            MutationsLabelData mutationsLabelData = new MutationsLabelData();
            mutationsLabelData.color = (BorderColor)label.quality;
            mutationsLabelData.text = Language.GetText(label.textId);
            labelsList.Add(mutationsLabelData);
        }
        itemsDetail.labels = labelsList;
        itemsDetail.Content = Language.GetText(dataEntity.desc);
        itemsDetail.render();
    }
    // 渲染宝石UI
    private void renderUI(int id) {
        if(BaseSourceData == null || BaseSourceData.Count <= 0) return;
        empowerItemEntity target = BaseSourceData.Find(x=> x.Id == id);
        if(target == null) return;
        initGoods(target);
        setPrice(target.price); // 价格
        setFrontImg(target.icon); // 图标
        baseBorderItem.CurrentBorder = target.quality; // 边框颜色
        // 1-主宝石 2-辅助宝石
        if(target.subType == 2) {
            // 翻牌完成后显示Label
            FlipEndEvent.AddListener(()=> {
                GemLabel?.gameObject.SetActive(true);
                GemLabel?.RenderDefault("辅助");
            });
        }
        // 详情
        setInfomation();
    }
    private void initGoods(empowerItemEntity target)
    {
        GemGoods goods = new GemGoods();
        goods.dataEntity = target;
        goods.id = target.Id;
        goods.price = target.price;
        goods.sellDiscount = shopConfig.sellDiscount;
        goods.sellPrice = (int)Math.Round(target.price * shopConfig.sellDiscount, MidpointRounding.AwayFromZero);
        goods.icon = target.icon;
        goods.background = backImageUrl;
        goods.goodsType = (int)GoodsType.Gems;
        baseGoodsData = goods;
    }
    /// <summary>
    /// 加载
    /// </summary>
    public override void LoadItem() {
        if(BaseSourceData == null || BaseSourceData.Count <= 0) return;
        weightFilter.setStoreData(getGemItems());
        List<gecang_gemEntity> filter = weightFilter.FilterData(); // 前置筛选
        List<int> itemTypeList = weightFilter.GetListWeight(filter); // 获取权重列表
        int index = WeightCalculator.GetWeightedRandomIndex(itemTypeList); // 根据权重获取随机索引
        int targetId = filter[index].id; // 根据索引获取对应的数据
        renderUI(targetId);
    }

    /// <summary>
    /// 加载指定id
    /// </summary>
    /// <param name="id"></param>
    public override void LoadItem(int id) {
        renderUI(id);
    }
    // 购买
    public override void BuyItem() 
    {
        GemGoods goods = baseGoodsData as GemGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || OtherInventory.Instance == null) {
            return;
        }
        // 支付
        bool payRes = GameMain.Instance.payJade(dataEntity.price);
        if(!payRes) return;
        if(OtherInventory.Instance.AddItems(baseGoodsData))
        {
            base.BuyItem();
        }
    }
    /// <summary>
    ///  选择
    /// </summary>
    public override void ChooseItem() {
        GemGoods goods = baseGoodsData as GemGoods;
        if(goods == null) return;
        var dataEntity = goods.dataEntity;
        if(dataEntity == null || OtherInventory.Instance == null) {
            return;
        }
        if (OtherInventory.Instance.AddItems(baseGoodsData))
        {
            base.ChooseItem();
        }
    }
}
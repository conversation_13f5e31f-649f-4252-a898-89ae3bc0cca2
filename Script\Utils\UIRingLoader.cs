using System;
using System.Collections.Generic; // 为 List<KeyCode> 添加
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class UIRingLoader : MonoBehaviour
{
    private enum RingTypeEnum
    {
        // 长按
        LongPress = 1,
        // 按下
        Press = 2,
    }

    [Header("配置项")]
    [SerializeField] private Image ringImageWrapper;
    [SerializeField] private Image ringImage; // 设置为Fill模式的Image，使用Sprite类型为 Filled、Radial 360
    [SerializeField] private TMP_Text ringText; // 设置为TextMeshProUGUI
    [SerializeField] private float ringTime = unitConfig.LongPressTime / 1000f; // 旋转一周的时间
    
    // MODIFIED: 从单个 KeyCode 更改为 KeyCode 列表
    [SerializeField] private List<KeyCode> triggerKeyCodes = new List<KeyCode>() { KeyCode.F }; // 按下键列表，默认为 F 键

    private static readonly float ringTimeDefault = unitConfig.LongPressTime / 1000f;
    private float holdTime = 0f; // 按下按键的累计时间
    private bool actionTriggered = false; // 是否触发过动作
    private Action DoAction;
    private Action<KeyCode> ReplaceAction;
    private Action<float> UpdateProgress;
    private bool isDisable = false;

    // NEW/MODIFIED: 用于在运行时访问或设置触发按键列表的属性
    public List<KeyCode> TriggerKeyCodes 
    {
        get { return triggerKeyCodes; }
        set { triggerKeyCodes = value; }
    }

    // 原来的单按键设置属性 (keyCode) 可以移除或根据需要调整其行为
    /* public KeyCode keyCode {
        set {
            // 例如: 清空列表并添加这一个键, 或者直接添加到列表中
            triggerKeyCodes.Clear();
            if (!triggerKeyCodes.Contains(value)) {
                 triggerKeyCodes.Add(value);
            }
        }
    }
    */

    public float RingTime {
        set {
            ringTime = value;
        }
        get { // 添加 getter
            return ringTime;
        }
    }

    public int RingType {
        set {
            if((RingTypeEnum)value == RingTypeEnum.LongPress) {
                ringTime = ringTimeDefault;
            } else { // Press Type
                ringTime = 0f; // 对于即时按下，ringTime 设置为0
            }
        }
    }

    public bool DisableRing {
        set {
            if(isDisable == value) return;
            isDisable = value; // 在颜色更改前设置状态
            if (ringText != null) // 添加空检查
            {
                ringText.color = value ? Color.red : Color.green;
            }
        }
        get { // 添加 getter
            return isDisable;
        }
    }

    private bool show = true;

    private void SetProgress(float progress)
    {
        if (ringImage != null)
        {
            ringImage.fillAmount = Mathf.Clamp01(progress);
        }
    }

    public void setText(string text) 
    {
        if (ringText != null) // 添加空检查
        {
            ringText.text = text;
        }
    }

    public void onEnd(Action action) 
    {
        DoAction = action;
    }

    public void onEnd(Action<KeyCode> action)
    {
        ReplaceAction = action;
    }

    public void onUpdate(Action<float> action) 
    {
        UpdateProgress = action;
    }

    void OnEnable()
    {
        show = true;
        // 可选: 如果需要在启用时重置状态
        // holdTime = 0f;
        // actionTriggered = false;
        // if (ringImageWrapper != null && ringImageWrapper.activeSelf) ringImageWrapper.gameObject.SetActive(false);
        // SetProgress(0);
    }

    void OnDisable()
    {
        show = false;
        // 禁用时确保视觉元素被重置/隐藏
        if (ringImageWrapper != null && ringImageWrapper.gameObject.activeSelf)
        {
            ringImageWrapper.gameObject.SetActive(false);
        }
        SetProgress(0); // 重置进度条
        holdTime = 0f; // 重置按住时间
        actionTriggered = false; // 重置动作触发标记
    }

    void Update()
    {
        if (!show || isDisable) return;

        bool anyMonitoredKeyPressed = false;
        KeyCode usekeyCode = KeyCode.None;
        if (triggerKeyCodes != null && triggerKeyCodes.Count > 0)
        {
            for(int i = 0; i < triggerKeyCodes.Count; i++)
            {
                if (Input.GetKey(triggerKeyCodes[i])) // 检查是否有任何一个指定的键被按住
                {
                    anyMonitoredKeyPressed = true;
                    usekeyCode = triggerKeyCodes[i];
                    break; // 一旦找到一个被按下的键，就无需检查其余的
                }
            }
        }

        if (anyMonitoredKeyPressed)
        {
            holdTime += Time.deltaTime;

            bool isImmediatePressType = (ringTime <= 0f);

            if (!isImmediatePressType) // 长按逻辑 (ringTime > 0f)
            {
                if (ringImageWrapper != null && !ringImageWrapper.gameObject.activeSelf)
                {
                     ringImageWrapper.gameObject.SetActive(true);
                }
                float progress = Mathf.Clamp01(holdTime / ringTime);
                SetProgress(progress);
                UpdateProgress?.Invoke(progress);

                if (holdTime >= ringTime && !actionTriggered)
                {
                    if (ReplaceAction != null)
                    {
                        ReplaceAction?.Invoke(usekeyCode);
                    } else
                    {
                        DoAction?.Invoke();
                    }
                    
                    actionTriggered = true; 
                    if (ringImageWrapper != null && ringImageWrapper.gameObject.activeSelf)
                    {
                        ringImageWrapper.gameObject.SetActive(false);
                    }
                }
            }
            else // 即时按下逻辑 (ringTime <= 0f)
            {
                // 动作应该在按键按下时触发一次，而不是在按键持续按下期间重复触发。
                // actionTriggered 标志用于防止在按键释放前重复调用 DoAction。
                if (!actionTriggered)
                {
                    // 对于即时按下，通常没有视觉进度条，或者可以快速显示一个完成状态。
                    // SetProgress(1f); // 可选：指示动作已触发
                    // UpdateProgress?.Invoke(1f); // 可选
                    DoAction?.Invoke();
                    actionTriggered = true; 
                    // 即时按下的视觉效果通常由 DoAction 本身处理，或者保持 ringImageWrapper 不可见。
                    // if (ringImageWrapper != null && ringImageWrapper.gameObject.activeSelf) {
                    //    ringImageWrapper.gameObject.SetActive(false);
                    // }
                }
            }
        }
        else // 当前没有指定的按键被按下
        {
            // 如果按键被释放，则重置状态
            if (holdTime > 0f) // 仅当之前有交互时才重置
            {
                SetProgress(0); // 重置视觉进度
                if (ringImageWrapper != null && ringImageWrapper.gameObject.activeSelf && ringTime > 0f) // 仅当是长按类型时才隐藏
                {
                    ringImageWrapper.gameObject.SetActive(false);
                }
            }
            holdTime = 0f; // 重置按住时间
            actionTriggered = false; // 准备好下一次按键动作
        }
    }
}
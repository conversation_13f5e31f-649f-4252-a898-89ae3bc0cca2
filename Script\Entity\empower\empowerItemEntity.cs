using System;

namespace Excel.empower
{
	[Serializable]
	public class empowerItemEntity
	{
		/// <summary>
		/// 材料ID
		/// </summary>
		public int Id;
		/// <summary>
		/// 名称
		/// </summary>
		public int name;
		/// <summary>
		/// 材料大类
		/// </summary>
		public int mainType;
		/// <summary>
		/// 材料小类
		/// </summary>
		public int subType;
		/// <summary>
		/// 品质
		/// </summary>
		public int quality;
		/// <summary>
		/// 属性
		/// </summary>
		public int attr;
		/// <summary>
		/// 价格
		/// </summary>
		public int price;
		/// <summary>
		/// 赋能效果参数
		/// </summary>
		public string effect;
		/// <summary>
		/// 标签
		/// </summary>
		public string label;
		/// <summary>
		/// 信息说明
		/// </summary>
		public int desc;
		/// <summary>
		/// 图标
		/// </summary>
		public string icon;
	}
}

using UnityEngine;
using UnityEngine.UI;
using System.IO;
using UnityEngine.SceneManagement;
using Excel.MiniMap;

/// <summary>
/// 圆形小地图显示组件，用于加载和显示生成的小地图
/// </summary>
public class CircleMiniMapDisplay : MonoBehaviour
{
    [Header("小地图路径")]
    public string miniMapImagePath = "MiniMap/3 flatland in front of the mountain 2";

    [<PERSON><PERSON>("UI组件")]
    [Tooltip("小地图图像")]
    public RawImage miniMapImage;

    [Tooltip("小地图边框")]
    public Image miniMapBorder;

    [Header("玩家标记")]
    [Tooltip("玩家标记图像")]
    public Image playerMarker;

    [Toolt<PERSON>("玩家引用")]
    public Transform player;

    [Header("小地图设置")]
    [Tooltip("小地图半径")]
    public float miniMapRadius = 100f;

    [Tooltip("小地图旋转（是否跟随玩家旋转）")]
    public bool rotateWithPlayer = true;

    [Range(0.5f, 2f)]
    [Tooltip("缩放级别")]
    public float zoomLevel = 1f;

    // 小地图纹理
    private Texture2D miniMapTexture;

    // 小地图世界边界
    public Vector3 mapWorldMin;
    public Vector3 mapWorldMax;
    private Vector3 mapSize = new Vector3();

    private Vector3 center = new Vector3();

    private Vector3 cameraPosition = new Vector3();


    // 是否已初始化
    public bool initialized = false;

    // 圆形遮罩
    private RectTransform miniMapMaskRect;

    public void Start()
    {
        // 根据当前场景名称加载小地图数据
        string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        InfoEntity infoEntity = ExcelData.Instance.MiniMapExcel.InfoList.Find(x => x.name == currentSceneName);

        if (infoEntity != null)
        {
            // 设置摄像机位置（生成小地图时的摄像机位置）
            cameraPosition.x = infoEntity.x;
            cameraPosition.y = infoEntity.y;
            cameraPosition.z = infoEntity.z;

            // 设置地图边界中心
            center.x = infoEntity.boundsCenterX;
            center.y = infoEntity.boundsCenterY;
            center.z = infoEntity.boundsCenterZ;

            // 设置地图边界大小
            mapSize.x = infoEntity.boundsSizeX;
            mapSize.y = infoEntity.boundsSizeY;
            mapSize.z = infoEntity.boundsSizeZ;

            // 计算世界边界
            mapWorldMin = new Vector3(center.x - mapSize.x / 2, center.y - mapSize.y / 2, center.z - mapSize.z / 2);
            mapWorldMax = new Vector3(center.x + mapSize.x / 2, center.y + mapSize.y / 2, center.z + mapSize.z / 2);

            // 设置小地图图片路径
            miniMapImagePath = infoEntity.imagePath;

            // 初始化小地图
            InitializeMiniMap();
        }
        else
        {
            Debug.LogError($"未找到场景 '{currentSceneName}' 的小地图数据！请先使用小地图生成工具生成小地图。");
        }
    }

    /// <summary>
    /// 初始化小地图
    /// </summary>
    public void InitializeMiniMap()
    {
        // 加载小地图纹理
        LoadMiniMapTexture();

        if (miniMapTexture != null)
        {
            // 设置小地图图像
            if (miniMapImage != null)
            {
                miniMapImage.texture = miniMapTexture;

                // 确保RawImage有RectTransform组件
                RectTransform rectTransform = miniMapImage.GetComponent<RectTransform>();

                // 保持小地图原始大小，不进行缩放
                // 注意：这里设置的尺寸应该大于或等于小地图的实际尺寸
                // 我们将使用偏移来显示地图的不同部分
                rectTransform.sizeDelta = new Vector2(miniMapTexture.width, miniMapTexture.height);

            }

            // 设置边框大小
            if (miniMapBorder != null)
            {
                RectTransform borderRect = miniMapBorder.GetComponent<RectTransform>();
                borderRect.sizeDelta = new Vector2(miniMapRadius * 2, miniMapRadius * 2);
            }

            // 设置地图世界边界（这里需要根据实际地图大小调整）
            // 这些值应该从MiniMapCaptureEditor中获取或保存

           // mapSize = new Vector2(mapWorldMax.x - mapWorldMin.x, mapWorldMax.z - mapWorldMin.z);

            initialized = true;
        }
        else
        {
            //Debug.LogError("无法加载小地图纹理！");
        }
    }

    /// <summary>
    /// 加载小地图纹理
    /// </summary>
    private void LoadMiniMapTexture()
    {
        // 尝试从Resources加载
        miniMapTexture = Resources.Load<Texture2D>(miniMapImagePath);

        // 如果Resources中没有，尝试从文件加载
        if (miniMapTexture == null)
        {
            string fullPath = Path.Combine(Application.dataPath, "Resources", miniMapImagePath + ".png");
            if (File.Exists(fullPath))
            {
                byte[] fileData = File.ReadAllBytes(fullPath);
                miniMapTexture = new Texture2D(2, 2);
                miniMapTexture.LoadImage(fileData);
            }
        }
    }

    void Update()
    {
        if (!initialized)
            return;

        // 更新玩家标记位置
        UpdatePlayerMarker();

        // 更新小地图位置，使玩家始终在圆形区域中心
        UpdateMiniMapPosition();
    }

    /// <summary>
    /// 更新小地图位置，使玩家始终在圆形区域中心
    /// </summary>
    private void UpdateMiniMapPosition()
    {
        if (miniMapImage == null || Role.Instance == null)
            return;

        Transform playerTransform = Role.Instance.transform;

        // 计算玩家在世界坐标中的相对位置 (0-1范围)
        float percentX = Mathf.InverseLerp(mapWorldMin.x, mapWorldMax.x, playerTransform.position.x);
        float percentZ = Mathf.InverseLerp(mapWorldMin.z, mapWorldMax.z, playerTransform.position.z);

        // 计算玩家在小地图纹理上的像素位置
        float pixelX = percentX * miniMapTexture.width;
        float pixelZ = (1f - percentZ) * miniMapTexture.height; // 注意：Unity UI的Y轴是反向的

        // 计算小地图需要的偏移量，使玩家位置位于圆形区域中心
        // 小地图图片需要移动到相反的方向，这样玩家就会出现在中心
        float offsetX = -(pixelX - miniMapRadius);
        float offsetY = -(pixelZ - miniMapRadius);

        // 如果小地图跟随玩家旋转
        if (rotateWithPlayer)
        {
            // 旋转小地图
            miniMapImage.transform.rotation = Quaternion.Euler(0, 0, -playerTransform.eulerAngles.y);

            // 在旋转的情况下，需要调整偏移量
            float angle = playerTransform.eulerAngles.y * Mathf.Deg2Rad;
            float cosAngle = Mathf.Cos(angle);
            float sinAngle = Mathf.Sin(angle);

            float rotatedOffsetX = offsetX * cosAngle - offsetY * sinAngle;
            float rotatedOffsetY = offsetX * sinAngle + offsetY * cosAngle;

            offsetX = rotatedOffsetX;
            offsetY = rotatedOffsetY;
        }

        // 应用偏移量到小地图图片
        miniMapImage.rectTransform.anchoredPosition = new Vector2(offsetX, offsetY);
    }

    /// <summary>
    /// 更新玩家标记位置
    /// </summary>
    private void UpdatePlayerMarker()
    {
        if (playerMarker == null || Role.Instance == null)
            return;

        Transform playerTransform = Role.Instance.transform;

        // 玩家标记始终在小地图的中心，因为我们移动的是地图而不是标记
        playerMarker.rectTransform.anchoredPosition = Vector2.zero;

        // 如果不跟随玩家旋转，则让玩家标记显示玩家的朝向
        if (!rotateWithPlayer)
        {
            // 更新玩家标记旋转（使标记跟随玩家朝向）
            playerMarker.rectTransform.rotation = Quaternion.Euler(0, 0, -playerTransform.eulerAngles.y);
        }
        else
        {
            // 如果地图跟随玩家旋转，玩家标记保持向上
            playerMarker.rectTransform.rotation = Quaternion.identity;
        }
    }

    /// <summary>
    /// 设置小地图缩放级别
    /// </summary>
    public void SetZoomLevel(float zoom)
    {
        zoomLevel = Mathf.Clamp(zoom, 0.5f, 2f);
    }

    /// <summary>
    /// 切换小地图显示
    /// </summary>
    public void ToggleMiniMap()
    {
        gameObject.SetActive(!gameObject.activeSelf);
    }

    /// <summary>
    /// 设置小地图图像
    /// </summary>
    public void SetMiniMapTexture(Texture2D texture)
    {
        if (texture != null && miniMapImage != null)
        {
            miniMapTexture = texture;
            miniMapImage.texture = miniMapTexture;
        }
    }

    /// <summary>
    /// 设置地图世界边界
    /// </summary>
    public void SetMapWorldBounds(Vector3 min, Vector3 max)
    {
        mapWorldMin = min;
        mapWorldMax = max;
        mapSize = new Vector2(mapWorldMax.x - mapWorldMin.x, mapWorldMax.z - mapWorldMin.z);
    }

    /// <summary>
    /// 将世界坐标转换为小地图上的UI坐标
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <returns>小地图上的UI坐标</returns>
    public Vector2 WorldToMiniMapPosition(Vector3 worldPosition)
    {
        // 计算物体在世界边界中的相对位置 (0-1范围)
        float percentX = Mathf.InverseLerp(mapWorldMin.x, mapWorldMax.x, worldPosition.x);
        float percentZ = Mathf.InverseLerp(mapWorldMin.z, mapWorldMax.z, worldPosition.z);

        // 将百分比转换为小地图上的UI坐标
        // 注意：小地图的中心是(0,0)，所以需要将0-1范围映射到-radius到+radius
        float posX = (percentX - 0.5f) * miniMapRadius * 2;
        float posY = (percentZ - 0.5f) * miniMapRadius * 2;

        // 如果小地图旋转，需要考虑旋转因素
        if (rotateWithPlayer && Role.Instance != null)
        {
            // 创建一个以玩家为中心的旋转矩阵
            float angle = -Role.Instance.transform.eulerAngles.y * Mathf.Deg2Rad;
            float cosAngle = Mathf.Cos(angle);
            float sinAngle = Mathf.Sin(angle);

            // 应用旋转
            float rotatedX = posX * cosAngle - posY * sinAngle;
            float rotatedY = posX * sinAngle + posY * cosAngle;

            posX = rotatedX;
            posY = rotatedY;
        }

        return new Vector2(posX, posY);
    }

    /// <summary>
    /// 将小地图上的UI坐标转换为世界坐标
    /// </summary>
    /// <param name="miniMapPosition">小地图上的UI坐标</param>
    /// <returns>世界坐标</returns>
    public Vector3 MiniMapToWorldPosition(Vector2 miniMapPosition)
    {
        // 如果小地图旋转，需要先反向旋转坐标
        Vector2 adjustedPosition = miniMapPosition;
        if (rotateWithPlayer && player != null)
        {
            float angle = player.eulerAngles.y * Mathf.Deg2Rad;
            float cosAngle = Mathf.Cos(angle);
            float sinAngle = Mathf.Sin(angle);

            // 应用反向旋转
            float rotatedX = adjustedPosition.x * cosAngle - adjustedPosition.y * sinAngle;
            float rotatedY = adjustedPosition.x * sinAngle + adjustedPosition.y * cosAngle;

            adjustedPosition = new Vector2(rotatedX, rotatedY);
        }

        // 将小地图坐标转换为0-1范围的百分比
        float percentX = (adjustedPosition.x / (miniMapRadius * 2)) + 0.5f;
        float percentZ = (adjustedPosition.y / (miniMapRadius * 2)) + 0.5f;

        // 将百分比转换为世界坐标
        float worldX = Mathf.Lerp(mapWorldMin.x, mapWorldMax.x, percentX);
        float worldZ = Mathf.Lerp(mapWorldMin.z, mapWorldMax.z, percentZ);

        // 使用地形高度或默认Y值
        float worldY = 0;
        if (player != null)
        {
            worldY = player.position.y;
        }

        return new Vector3(worldX, worldY, worldZ);
    }
}